# GCP Monitoring and Alerting

[Back to root README](../README.md)...

## Links

-   Frontend Dashboard [(🚨 Prod)](https://console.cloud.google.com/monitoring/dashboards/builder/fb86cfdd-9e20-4601-8e9e-2abfe877f0f0;duration=P1D?project=gum-site-prod-c9b6) [(🧪 Stage)](https://console.cloud.google.com/monitoring/dashboards/builder/99f8482d-9af1-4712-811f-c7353d3c11f4;duration=P1D?project=gum-site-stage-76e0)

-   Static assets bucket [(🚨 Prod)](https://console.cloud.google.com/storage/browser/gum-site-prod-static-assets) [(🧪 Stage)](https://console.cloud.google.com/storage/browser/gum-site-stage-static-assets)

-   Alerting [(🚨 Prod)](https://console.cloud.google.com/monitoring/alerting?project=gum-site-prod-c9b6) [(🧪 Stage)](https://console.cloud.google.com/monitoring/alerting?project=gum-site-stage-76e0)

-   Alerting Slack channel [#gt-tech-web-monitoring](https://gumtree.enterprise.slack.com/archives/C04B1AX9JG2)

-   [Frontend Dashboard PR](https://github.com/gumtree-gcp/terraform/pull/1659/files) created [frontend.json](https://github.com/gumtree-gcp/terraform/blob/a01e87d9a7afd79427a8a4402025d647737aec94/gum-site/monitoring-dashboards/frontend.json)

-   [Frontend Alerting PR](https://github.com/gumtree-gcp/terraform/pull/1696/files) created [frontend-monitoring-alerting.tf](https://github.com/gumtree-gcp/terraform/blob/master/gum-site/frontend-monitoring-alerting.tf)

<br/>

> ℹ️ The Frontend Dashboard links use a time-range of _one day_. You can change this using the GCP UI.
> <span style="float:right"> <img src="./gcp-dashboard-change-time-range.png" title="gcp-dashboard-change-time-range" width="120"/> </span>

> ℹ️ You can also browse from [`gum-site-stage`](<https://console.cloud.google.com/monitoring/dashboards?project=gum-site-stage-76e0&pageState=(%22dashboards%22:(%22t%22:%22All%22))>) or [`gum-site-prod`](<https://console.cloud.google.com/monitoring/dashboards?project=gum-site-prod-c9b6&pageState=(%22dashboards%22:(%22t%22:%22All%22))>) by searching for `Frontend Dashboard`.

## Dashboard Charts

Our `Frontend Dashboard` has the following charts.

<table>

<tr>
<th>Name</th>
<th>Info</th>
<th>Extra</th>
</tr>
<tr>
<td>BFF Pod Count</td>
<td>Number of kubernetes pods currently running.</td>
<td>
<code>Kubernetes Container - Uptime</code> with aggregation <code>Count time series</code>.</td>
</tr>

<tr>
<td>Bucket 404</td>
<td>
NOT_FOUND count for <a href="https://console.cloud.google.com/storage/browser/gum-site-prod-static-assets">static assets bucket</a>.
</td>
<td>
</td>
</tr>

<tr>
<td>Restarts</td>
<td>
Number of times BFF container has failed.
</td>
<td>
Summed over all pods, in 5 minute intervals.
</td>
</tr>

<tr>
<td>Uptime</td>
<td>
Average BFF pod lifetime.
</td>
<td>
50th percentile?
</td>
</tr>

<tr>
<td>Latency</td>
<td>
BFF Response Latencies.
</td>
<td>
</td>
</tr>

<tr>
<td>BFF Pod Count [6hr]</td>
<td>
6 hour rolling average.
</td>
<td>
</td>
</tr>

<tr>
<td>Bucket 404 [6hr]</td>
<td>
6 hour rolling average.
</td>
<td>
</td>
</tr>

<tr>
<td>Errors (≥400)</td>
<td>
BFF Error responses.
</td>
<td>
<img src="./gcp-dashboard-errors-geq-400.png" title="gcp-dashboard-errors-geq-400" width="300"/>
</td>
</tr>

<tr>
<td>Latency (Round Trip)</td>
<td>
BFF Request/Response Latencies.
</td>
<td>
</td>
</tr>

<tr>
<td>Requests</td>
<td>
Average number of requests.
</td>
<td>
istio.io request count, in 1 minute intervals
</td>
</tr>

<tr>
<td>2XX</td>
<td>
BFF 2XX responses.
</td>
<td>
e.g. 201, 201, 204
</td>
</tr>

<tr>
<td>Errors [6hr]</td>
<td>
6 hour rolling average.
</td>
<td>
</td>
</tr>

<tr>
<td>Redirects (3XX)</td>
<td>
BFF 3XX responses
</td>
<td>
e.g. 301, 303, 304
</td>
</tr>

<tr>
<td>Requests [6hr]</td>
<td>
6 hour rolling average.
</td>
<td>
</td>
</tr>

<tr>
<td>2XX [6hr]</td>
<td>
6 hour rolling average.
</td>
<td>
</td>
</tr>

</table>

## Alerts

<table>
<tr>
<th>Name</th>
<th>
Dashboard
<br/>
<a href="https://console.cloud.google.com/monitoring/dashboards/builder/fb86cfdd-9e20-4601-8e9e-2abfe877f0f0;duration=P1D?project=gum-site-prod-c9b6">Prod</a> | <a href="https://console.cloud.google.com/monitoring/dashboards/builder/99f8482d-9af1-4712-811f-c7353d3c11f4;duration=P1D?project=gum-site-stage-76e0">Stage</a>
</th>
</tr>

<tr>
<td>
Error requests (5xx) exceeds threshold
(<a href="https://console.cloud.google.com/monitoring/alerting/policies/15897803037794179374?project=gum-site-prod-c9b6">Prod</a>)
</td>
<td>
Errors (≥400)
</td>
</tr>

<tr>
<td>
Error requests (4xx) exceeds threshold
(<a href="https://console.cloud.google.com/monitoring/alerting/policies/7330356026890100855?project=gum-site-prod-c9b6">Prod</a>)
</td>
<td>
Errors (≥400)
</td>
</tr>

<tr>
<td>
Bucket NOT_FOUND exceeds threshold
(<a href="https://console.cloud.google.com/monitoring/alerting/policies/15996421784349711924?project=gum-site-prod-c9b6">Prod</a>)
</td>
<td>
Bucket 404
</td>
</tr>

<tr>
<td>
Request latency exceeds threshold
(<a href="https://console.cloud.google.com/monitoring/alerting/policies/13787895740135634865?project=gum-site-prod-c9b6">Prod</a>)
</td>
<td>
Latency
</td>
</tr>

<tr>
<td>
Pod count exceeds threshold
(<a href="https://console.cloud.google.com/monitoring/alerting/policies/3740393593259436309?project=gum-site-prod-c9b6">Prod</a>)
</td>
<td>
BFF Pod Count
</td>
</tr>

<tr>
<td>
Request 200s exceeds threshold
(<a href="https://console.cloud.google.com/monitoring/alerting/policies/15996421784349711827?project=gum-site-prod-c9b6">Prod</a>)
</td>
<td>
2XX
</td>
</tr>

<tr>
<td>
Request 200s below threshold
(<a href="https://console.cloud.google.com/monitoring/alerting/policies/15996421784349709872?project=gum-site-prod-c9b6">Prod</a>)
</td>
<td>
2XX
</td>
</tr>

</table>

## Updating the Dashboard

Manually changing the `Frontend Dashboard` on [prod](https://console.cloud.google.com/monitoring/dashboards/builder/fb86cfdd-9e20-4601-8e9e-2abfe877f0f0;duration=P1D?project=gum-site-prod-c9b6) (e.g. via GCP UI) requires temporary admin rights ([Example ticket](https://gumtree-uk.atlassian.net/browse/GTCLOUD-1866)).

Manually changing the `Frontend Dashboard` on [staging](https://console.cloud.google.com/monitoring/dashboards/builder/99f8482d-9af1-4712-811f-c7353d3c11f4;duration=P1D?project=gum-site-stage-76e0) should be possible for any developer.

> You can play around with the `Frontend Dashboard` on staging without saving your changes.
> If you want to save some changes then you can edit [`Frontend Dashboard (WIP)`](https://console.cloud.google.com/monitoring/dashboards/builder/a096203f-3644-48c7-a3c4-3a06044fdd29;duration=P1D?project=gum-site-stage-76e0) or create your own dashboard via the button on the [Dashboards Overview](https://console.cloud.google.com/monitoring/dashboards?project=gum-site-stage-76e0).

> You can clone Charts (also called Widgets) from one dashboard into another.

<img src="./gcp-clone-chart-between-dashboards.png" title="gcp-clone-chart-between-dashboards" width="220"/>

<br/>

To formally update the dashboard,
consider this PR:

> https://github.com/gumtree-gcp/terraform/pull/1659/files
>
> -   `gum-site/monitoring.tf` includes the file into GCP
> -   `gum-site/monitoring-dashboards/frontend.json` was exported from the GCP UI via "Copy JSON"

<br/>

<img src="./gcp-copy-dashboard-json.png" title="gcp-copy-dashboard-json" width="220"/>

## Updating the Alerts

Manually changing Alerting on [Prod](https://console.cloud.google.com/monitoring/alerting?project=gum-site-prod-c9b6) (e.g. via GCP UI) requires temporary admin rights ([Example ticket](https://gumtree-uk.atlassian.net/browse/GTCLOUD-1866)).

Manually changing the Alerting on [Staging](https://console.cloud.google.com/monitoring/alerting?project=gum-site-stage-76e0) should be possible for any developer.

> You can send alerts to your own email address during debugging ([Example](https://console.cloud.google.com/monitoring/alerting/policies/17340428982439158503?project=gum-site-stage-76e0)).

<img src="./gcp-test-notification-channel-example.png" title="gcp-test-notification-channel-example" width="320"/>

> You should (probably) avoid sending alerts to `#gt-tech-web-monitoring` because they may confuse others.

<br/>

To formally update the alerts consider these PRs:

-   https://github.com/gumtree-gcp/terraform/pull/1696/files
-   https://github.com/gumtree-gcp/terraform/pull/1703
-   https://github.com/gumtree-gcp/terraform/pull/1738
