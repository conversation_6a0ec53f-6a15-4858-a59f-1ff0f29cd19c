# Styleguide (Storybook)

## Rationale

**Tech Debt Prevention.**
Adding developers without a clear component library will amplify product and technical debt, requiring costly fixes later.

**Collaboration with 58.**
A lack of standards makes it harder for distributed teams to align, review, and improve code effectively. Engineers unfamiliar with the platform will make varied decisions.

**Development Velocity.**
Accelerate the process of change, enabling teams to deliver updates more efficiently, and at scale

## How to use

To start Storybook in dev mode, run `yarn storybook:start` from the root of the frontend-web-bff project. This will open a new browser and automatically navigate to `http://localhost:6006`.

### Visual Regression Tests

This styleguide makes use of Visual Regression Testing to ensure we don't accidently introduce any issues at a component level. These are image based snapshot comparisons using Backstop and Playwright.

In order to generate new images and run the tests, you must have Docker installed. Any browsers are opened in a Docker container to ensure the browser is exactly the same regardless of the platform you're running on. Without it, you see issues when running within Jenkins, as the text in the image is slightly different on Mac vs Linux.

#### Generating Reference Images

To generate any new references images for the Backstop Visual Regression Tests, run `yarn backstop:reference`. This identifies Stories without existing tests and generates reference images for them. Ensure your Storybook development server is running (`yarn storybook:start`).

If you need to regenerate all reference images (e.g., after a major update or changes to a component), then run `yarn backstop:reference:all`. This will clear down _all_ test images and re-generate. This is actually the default Backstop behaviour - but we want to highlight any changes as test failures and not encourage just doing a full update everytime as seeing the differences is difficult. Also, changing images for existing components should only be off the back of a feature change. Any deviance should be agreed with UX and fully tested (if you notice this happening in PRs, please raise it with the original creator). By following these practices, we ensure changes are intentional and align with the design team's expectations.

#### Running the tests

To run the tests, ensure your Storybook development service is running and run either `yarn backstop:test:chromium` or `yarn backstop:test:webkit`. This will run the tests (in a Docker container) against your local Storybook environment.

If you wish to run against a different environment (e.g. something deployed to Zoidberg), you can change the URLs on the command line.

`yarn backstop:test:chromium --storiesBaseUrl="https://storage.googleapis.com/gum-site-dev-zoidberg-styleguide-assets/master" --webserverBaseUrl="https://storage.googleapis.com/gum-site-dev-zoidberg-styleguide-assets/master"`

Typically, `--storiesBaseUrl` and `--webserverBaseUrl` will be the same when running against an external server. They're separated out for running locally as the default configuration is :-

-   storiesBaseUrl: http://localhost:6006
-   webserverBaseUrl: http://host.docker.internal:6006

Stories are generated before the docker container is spun up - hence the storiesBaseUrl points to localhost. The docker container needs to point at your local storybook server to take snapshots of the page, hence the webserverBaseUrl points to host.docker.internal.

## Coding Standards

### Use only the theme file

To maintain design consistency across components, all colors, spacing, typography, and other design tokens must come exclusively from the theme file. Avoid hardcoding styles or using arbitrary values.

While the previous theme files (TS/SCSS) do abstract common variables, they are not used via a theme provider and therefore cannot be swapped at runtime. We currently maintain two themes: the standard "site" theme and an alternate scheme designed to support apps. All components should be designed to seamlessly integrate with these themes.

### Use only CSS-in-JS (emotion)

All styling must be implemented using Emotion's CSS-in-JS approach. If migrating components from the old library that use SCSS, these must be updated to conform to the new standard.

Some areas of the application, such as the SYI, include a large "base" stylesheet with common styling (typically copied from legacy). However, all components must function independently and should not rely on these base stylesheets that are not universally applied throughout the application

### Write RTL unit tests

Every component must include unit tests for functionality, interactions, and layout. All tests should use RTL (React Testing Library) to simulate real-world usage.

Snapshot tests should be avoided wherever possible. While they provide rapid coverage, they often require frequent updates due to changes in Emotion-generated CSS class names, reducing their overall effectiveness. RTL tests are preferred to achieve the desired level of confidence in component behavior and structure.

### Ensure stories are fully formed

Storybook stories must showcase components with all possible states and variations. Provide comprehensive examples and clear documentation for each component to facilitate reuse and understanding.

### Keep it presentational

All components in the styleguide should remain presentational. Business logic, API calls or dependencies on application state should not be within these components. Use props to control behaviour and maintain clear separation of concerns.

## In Progress / Todo

### Token File / Theme

We are transitioning to a standardised token structure exported from Figma. This change will ensure seamless updates from the UX team, enabling consistent implementation of styles and alignment across all teams by referencing unified variables.

During the development of app views, we initially utilised a Material-UI theme. However, this approach will be restructured to incorporate the new token-based format for improved consistency and scalability.

### Testing Approach

In addition to the existing functional testing with React Testing Library (RTL), we intend to implement Visual Regression Testing (VRT) across the Storybook library. This will allow us to identify any visual regressions that may occur.

## Pipeline

Storybook is deployed to a public facing GCP bucket so it can easily be accessed. It contains no business IP which needs to be hidden, as it just forms part of the website.

The [frontend-publish-storybook](https://jenkins.gum-ops-ci.gumtree.cloud/job/Gumtree/job/frontend-publish-storybook/) Jenkins pipeline :-

-   Builds the static Storybook site
-   Uploads the static site to a GCP bucket, first removing the previous version.
    -   Each branch will be uploaded to a subfolder, so branches can be deployed and tested by a UXD'er easily.
-   Runs the visual regression tests against the deployed Storybook assets.

The [frontend-pipeline](https://jenkins.gum-ops-ci.gumtree.cloud/job/Gumtree/job/frontend-pipeline/) will run the `frontend-publish-storybook` job as part of it's pipeline.

-   To skip the Storybook step, select the `skip_deploy_to_non_prod` checkbox.
-   Storybook will _not_ be uploaded to a prod bucket. As part of a prod deployment, we upload to the staging bucket (in the same way we deploy the frontend app to the staging environment).
-   If the Storybook tests fail, you'll be given the option to skip them and continue with deployment (like the Critical Path tests). However, this should be used in an emergency, and you should always opt for fixing them first.

The relevant buckets can be found at :-

-   Zoidberg
    -   [Latest Master Storybook](https://storage.googleapis.com/gum-site-dev-zoidberg-styleguide-assets/master/index.html)
    -   [Bucket](https://console.cloud.google.com/storage/browser/gum-site-dev-zoidberg-styleguide-assets;tab=objects?forceOnBucketsSortingFiltering=true&inv=1&invt=AbiFow&project=gum-site-dev-zoidberg-694a&prefix=&forceOnObjectsSortingFiltering=false)
-   Staging
    -   [Latest Master Storybook](https://storage.googleapis.com/gum-site-stage-styleguide-assets/master/index.html)
    -   [Bucket](https://console.cloud.google.com/storage/browser/gum-site-stage-styleguide-assets;tab=objects?forceOnBucketsSortingFiltering=true&inv=1&invt=AbiFow&project=gum-site-stage-76e0&prefix=&forceOnObjectsSortingFiltering=false)
