{"name": "tools", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"autogen-api-clients": "PKG_NAME=web-bff yarn autogen-api-clients-pkg && PKG_NAME=web-bff yarn autogen-api-ad-validator-pkg && PKG_NAME=web-bff yarn autogen-api-msgbox-pkg && PKG_NAME=web-bff yarn autogen-api-revies-service && PKG_NAME=payment-messages yarn autogen-api-clients-pkg && PKG_NAME=payment-messages yarn autogen-api-ad-validator-pkg && PKG_NAME=web-bff yarn autogen-api-phone-auth-pkg && PKG_NAME=web-bff yarn autogen-api-conversations && PKG_NAME=web-bff yarn autogen-conversation-reporter", "autogen-api-clients-pkg": "openapi-generator-cli generate -g typescript-axios -i ../../peer-to-peer-payments/contract/contract.yaml -o ../packages/${PKG_NAME}/src/api/clients/payments -t ../config/open-api-templates", "autogen-api-ad-validator-pkg": "openapi-generator-cli generate -g typescript-axios -i ../../p2p-payments-ad-validator/contract/contract.yaml -o ../packages/${PKG_NAME}/src/api/clients/ads-validator -t ../config/open-api-templates", "autogen-api-msgbox-pkg": "openapi-generator-cli generate -g typescript-axios -i ../../message-box-api-contracts/contracts/messagebox.yaml -o ../packages/${PKG_NAME}/src/api/clients/message-box -t ../config/open-api-templates", "autogen-api-reviews-service": "openapi-generator-cli generate -g typescript-axios -i ../../user-reviews-service/contract/user-reviews.yaml -o ../packages/${PKG_NAME}/src/api/clients/user-reviews -t ../config/open-api-templates", "autogen-api-phone-auth-pkg": "openapi-generator-cli generate -g typescript-axios -i ../../phone-number-authenticator/contract/contract.yaml -o ../packages/${PKG_NAME}/src/api/clients/phone-auth -t ../config/open-api-templates", "autogen-api-conversations": "openapi-generator-cli generate -g typescript-axios -i ../../conversations/contract/contract.yaml -o ../packages/web-bff/src/api/clients/conversations -t ../config/open-api-templates", "autogen-api-livead-search": "openapi-generator-cli generate -g typescript-axios -i ../../livead-search/contract/src/main/resources/livead-search-contract.yaml -o ../packages/web-bff/src/api/clients/livead-search -t ../config/open-api-templates", "autogen-api-jwt-authorization": "openapi-generator-cli generate --skip-validate-spec -g typescript-axios -i ../../authorisation/contract/contract.yaml -o ../packages/web-bff/src/api/clients/jwt-authorization -t ../config/open-api-templates", "autogen-conversation-reporter": "PKG_NAME=web-bff && openapi-generator-cli generate --skip-validate-spec -g typescript-axios -i ../../conversation-reporter/contract/contract.yaml -o ../packages/${PKG_NAME}/src/api/clients/conversation-reporter -t ../config/open-api-templates", "autogen-api-user-service": "openapi-generator-cli generate -g typescript-axios -i ../../user-service/contract/contract.yaml -o ../packages/web-bff/src/api/clients/users -t ../config/open-api-templates", "autogen-api-threatmetrix": "openapi-generator-cli generate -g typescript-axios -i ../../threatmetrix/contract/contract.yaml -o ../packages/web-bff/src/api/clients/threatmetrix -t ../config/open-api-templates"}, "devDependencies": {"@openapitools/openapi-generator-cli": "^2.7.0"}, "resolutions": {"ansi-regex": "5.0.1", "axios": "^1.8.2", "lerna": "8.1.8", "node-fetch": "2.7.0", "@nestjs/core": "9.0.5", "@nestjs/axios": "3.0.1", "follow-redirects": "1.15.6", "hosted-git-info": "2.8.9", "semver": "^5.7.2"}}