import type { KeyedReturnTypes } from '@gumtree/shared/src/model/generic.model';
import commonReducers, { updateCommonData } from '@gumtree/shell/src/reducers/common';
import buyerReducers, { updateBuyerData } from '@gumtree/shell/src/reducers/buyer';
import page, { updatePage } from '@gumtree/shell/src/reducers/page';
import type { Transformed } from '../get-data/transform';
import userDetails, { updateUserDetails } from './user-details';
import userAdverts, { updateUserAdverts } from './users-adverts';

export const showPage =
    ({
        title,
        description,
        userDetails,
        userAdverts,
        ...data
    }: Transformed & { pageType: string }) =>
    (dispatch) => {
        dispatch(updateCommonData(data));
        dispatch(updateBuyerData(data));
        dispatch(updatePage({ title, description }));
        dispatch(updateUserDetails(userDetails));
        dispatch(updateUserAdverts(userAdverts));
    };

const index = {
    ...commonReducers,
    ...buyerReducers,
    page,
    userDetails,
    userAdverts,
};

export default index;

export type ProfileState = KeyedReturnTypes<typeof index>;
