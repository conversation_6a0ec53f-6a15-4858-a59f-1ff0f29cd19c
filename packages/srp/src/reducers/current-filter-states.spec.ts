import { setFilterThunk, setSingleFilterState } from './current-filter-states';
import { fetchAbundance } from '../get-data/fetch/fetch-abundance';
import { CUSTOM_FILTERS, NON_DEFAULT_FILTERS } from '../components/shared/constants';

// Mock the dependencies
jest.mock('../get-data/fetch/fetch-abundance');
jest.mock('@gumtree/ui-library/src/utils/browser-service');
jest.mock('../logger', () => ({
    error: jest.fn(),
}));

describe('setFilterThunk', () => {
    let dispatch;
    let getState;

    beforeEach(() => {
        dispatch = jest.fn();
        getState = jest.fn(() => ({
            resultsPage: {
                currentFilterStates: {},
                filtersOrder: ['filter1', 'filter2'],
            },
            baseConfig: {
                category: 'testCategory',
            },
        }));
        fetchAbundance.mockImplementation((filterKey, _, onSuccess) => {
            onSuccess({ [filterKey]: 'mockData' });
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should dispatch setSingleFilterState with the provided payload', async () => {
        const payload = { key: 'testKey', value: 'testValue' };
        await setFilterThunk(payload)(dispatch, getState);
        expect(dispatch).toHaveBeenCalledWith(setSingleFilterState(payload));
    });

    it('should not fetch abundance data if the filter key is CATEGORIES', async () => {
        const payload = { key: CUSTOM_FILTERS.CATEGORIES, value: 'testValue' };
        await setFilterThunk(payload)(dispatch, getState);
        expect(fetchAbundance).not.toHaveBeenCalled();
    });

    it('should fetch abundance data for all filters except NON_DEFAULT_FILTERS', async () => {
        const payload = { key: 'testKey', value: 'testValue' };
        await setFilterThunk(payload)(dispatch, getState);
        expect(fetchAbundance).toHaveBeenCalledTimes(2); // for filter1 and filter2
        expect(fetchAbundance).not.toHaveBeenCalledWith(
            expect.arrayContaining(NON_DEFAULT_FILTERS),
            expect.anything(),
            expect.anything(),
            expect.anything()
        );
    });

    it('should include MODEL filter if it exists in currentFilterStates', async () => {
        getState.mockReturnValue({
            resultsPage: {
                currentFilterStates: { [CUSTOM_FILTERS.MODEL]: 'someModel' },
                filtersOrder: ['filter1'],
            },
            baseConfig: { category: 'testCategory' },
        });
        const payload = { key: 'testKey', value: 'testValue' };
        await setFilterThunk(payload)(dispatch, getState);
        expect(fetchAbundance).toHaveBeenCalledTimes(2); // for filter1 and MODEL
    });

    it('should handle errors when fetching abundance data', async () => {
        const mockError = new Error('Fetch error');
        fetchAbundance.mockImplementation((_, __, ___, onError) => {
            onError(mockError);
        });
        const payload = { key: 'testKey', value: 'testValue' };
        await setFilterThunk(payload)(dispatch, getState);
        expect(dispatch).toHaveBeenCalledTimes(2); // setSingleFilterState and updateFiltersDictionary
    });
});
