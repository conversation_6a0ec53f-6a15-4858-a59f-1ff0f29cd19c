import type { ThunkDispatch } from 'redux-thunk';
import { getNonFalsyUrlSearchParams } from '@gumtree/shared/src/util/url-params';
import { searchParametersFromSRP } from '@gumtree/shared/src/util/ga4-bff';
import { trackGA4PreNav } from '@gumtree/shared/src/util/ga4-shared';
import { redirectToUrl } from '@gumtree/ui-library/src/utils/browser-service';
import {
    removeFalsyProperties,
    diff as diffTwoObjects,
} from '@gumtree/shared/src/util/object-helpers';

import type { SrpState } from '.';
import { fetchAbundance } from '../get-data/fetch/fetch-abundance';
import {
    CUSTOM_FILTERS,
    NON_RESETTABLE_FILTERS,
    NON_DEFAULT_FILTERS,
} from '../components/shared/constants';
import logger from '../logger';
import type { Filter, FilterData, FiltersDictionary, FilterKey, FilterValue } from '../types';
import { updateFiltersDictionary } from './filters-dictionary';

export const RESET_FILTERS = 'RESET_FILTERS';
export const SET_FILTER = 'SET_FILTER';
export const SET_FILTER_BULK = 'SET_FILTER_BULK';

export type SingleFilter = {
    key: FilterKey;
    value: FilterValue;
};

export const trackGA4FiltersDiffThunk =
    (extra?: {
        searchResults?: number;
        updatedSearchFilters?: Record<string, string | number>;
        searchFilters?: Record<string, string | number>;
    }) =>
    (_dispatch: ThunkDispatch<SrpState, any, any>, getState: () => SrpState) => {
        try {
            const state = getState();

            const {
                isDominantCategory,
                searchType,
                category: categoryId,
            } = searchParametersFromSRP(window.dataLayer[0], state);
            const locationName = state.searchBar.location.value;

            const page = {
                isDominant: isDominantCategory,
                screenType: searchType === 'browse' ? 'BRP' : 'SRP',
                categoryId,
                locationType: postcodeRegex.test(locationName) ? 'POSTCODE' : 'LOCATION',
            };

            const prevFilters = (window.clientData as SrpState).resultsPage.currentFilterStates;
            const currFilters = state.resultsPage.currentFilterStates;

            // only care about selected, not deselected
            const diff = diffTwoObjects(prevFilters, currFilters);

            const touched = removeFalsyProperties(currFilters);

            trackGA4PreNav<GA4.ApplySearchFilterEvent>({
                event: 'apply_search_filter',
                prevSelectedSearchFilters: serializeDict({ ...page, ...touched }),
                updatedSearchFilters: serializeDict({ ...diff, ...extra?.updatedSearchFilters }),
                searchResults: extra?.searchResults,
                attributionsValue: JSON.stringify({ ...touched, ...extra?.searchFilters }),
            });
        } catch (e) {
            console.error(`trackGA4FiltersDiffThunk failed`, e);
        }

        function serializeDict(input: Record<string, string | boolean | number>) {
            return Object.entries(input)
                .map((x) => x.join(': '))
                .join('; ');
        }
    };

/** Source https://github.com/melwynfurtado/postcode-validator/blob/8fed941040fbc3cc3c6208b7b5188a5f2b7d11f2/src/postcode-regexes.ts#L11C5-L11C122 */
const postcodeRegex =
    /^([A-Z]){1}([0-9][0-9]|[0-9]|[A-Z][0-9][A-Z]|[A-Z][0-9][0-9]|[A-Z][0-9]|[0-9][A-Z]){1}([ ])?([0-9][A-z][A-z]){1}$/i;

export const resetFilters = () => ({
    type: RESET_FILTERS,
});

export const resetFiltersThunk = () => async (dispatch, getState) => {
    const {
        resultsPage: { currentFilterStates },
    } = getState() as SrpState;

    const resetted = Object.fromEntries(
        Object.entries(currentFilterStates).filter(([key]) => NON_RESETTABLE_FILTERS.includes(key))
    );

    dispatch(resetFilters());

    const url = getNonFalsyUrlSearchParams(resetted);

    redirectToUrl(`/search?${url.toString()}`);
};

export const setFiltersBulk = (payload: Filter) => ({
    type: SET_FILTER_BULK,
    payload,
});

export const setSingleFilterState = (payload: SingleFilter) => ({
    type: SET_FILTER,
    payload,
});

export const setFilterThunk = (payload: SingleFilter) => async (dispatch, getState) => {
    const {
        resultsPage: { currentFilterStates, filtersOrder },
        baseConfig: { category },
    } = getState() as SrpState;
    const newFilterStates = { ...currentFilterStates, [payload.key]: payload.value };

    dispatch(setSingleFilterState(payload));

    if (payload.key !== CUSTOM_FILTERS.CATEGORIES) {
        const responses = {} as FiltersDictionary;

        await Promise.all(
            [
                ...filtersOrder,
                ...(Object.keys(currentFilterStates).includes(CUSTOM_FILTERS.MODEL)
                    ? [CUSTOM_FILTERS.MODEL as FilterKey]
                    : []),
            ]
                .map((filterKey) => {
                    const isRequestShouldBeIgnored = NON_DEFAULT_FILTERS.includes(filterKey);

                    if (!isRequestShouldBeIgnored) {
                        return fetchAbundance(
                            filterKey,
                            newFilterStates,
                            (data: FilterData) => {
                                responses[filterKey] = data;
                            },
                            (error) => {
                                logger.error(
                                    `Error fetching search filters: ${filterKey}`,
                                    `${error}`
                                );
                            }
                        );
                    }

                    return undefined;
                })
                .filter((request) => request)
        );

        dispatch(updateFiltersDictionary(responses, category));
    }
};

export default (state = {} as Filter, action: ReturnType<typeof setSingleFilterState>): Filter => {
    switch (action.type) {
        case RESET_FILTERS:
            return {} as Filter;

        case SET_FILTER:
            return {
                ...state,
                [action.payload.key]: action.payload.value,
            };

        case SET_FILTER_BULK:
            return {
                ...state,
                ...action.payload,
            };

        default:
            return state;
    }
};
