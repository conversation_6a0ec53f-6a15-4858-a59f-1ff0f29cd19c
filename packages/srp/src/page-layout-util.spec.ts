import { PageLayout } from '@gumtree/shared/src/types/srp';
import { DeviceTypes, Categories } from '@gumtree/shared/src/types/client-data';
import { determinePageLayout, determineGaLabel } from './page-layout-util';

describe('determinePageLayout', () => {
    it('should return OneColumn for jobs category', () => {
        const result = determinePageLayout({
            deviceType: DeviceTypes.MOBILE,
            category: { l1Category: 'jobs' } as Categories,
        });
        expect(result).toBe(PageLayout.OneColumn);
    });

    it('should return TwoColumns for mobile with specific categories', () => {
        const categoriesToTest = [
            { l1Category: 'for-sale' },
            { l1Category: 'pets', l2Category: 'pet-equipment-accessories' },
            { l1Category: 'cars-vans-motorbikes', l2Category: 'motors-parts' },
            { l1Category: 'cars-vans-motorbikes', l2Category: 'motors-accessories' },
        ];

        categoriesToTest.forEach((category) => {
            const result = determinePageLayout({
                deviceType: DeviceTypes.MOBILE,
                category: category as Categories,
            });
            expect(result).toBe(PageLayout.TwoColumns);
        });
    });

    it('should return OneColumn for mobile without specific categories', () => {
        const result = determinePageLayout({
            deviceType: DeviceTypes.MOBILE,
            category: { l1Category: 'business-services' } as Categories,
        });
        expect(result).toBe(PageLayout.OneColumn);
    });
});
describe('determineGaLabel()', () => {
    it('should return OneColumnCardView for viewportWidth < 768 and PageLayout.OneColumn', () => {
        const result = determineGaLabel(500, PageLayout.OneColumn);
        expect(result).toBe('OneColumnCardView');
    });

    it('should return TwoColumnGridView for viewportWidth < 768 and PageLayout.TwoColumns', () => {
        const result = determineGaLabel(500, PageLayout.TwoColumns);
        expect(result).toBe('TwoColumnGridView');
    });

    it('should return ListView for viewportWidth >= 768 and PageLayout.OneColumn', () => {
        const result = determineGaLabel(800, PageLayout.OneColumn);
        expect(result).toBe('ListView');
    });

    it('should return TwoColumnGridView for viewportWidth >= 768 and PageLayout.TwoColumns', () => {
        const result = determineGaLabel(800, PageLayout.TwoColumns);
        expect(result).toBe('TwoColumnGridView');
    });

    it('should return ThreeColumnGridView for viewportWidth >= 768 and PageLayout.ThreeColumns', () => {
        const result = determineGaLabel(800, PageLayout.ThreeColumns);
        expect(result).toBe('ThreeColumnGridView');
    });

    it('should return undefined for viewportWidth < 768 and PageLayout.ThreeColumns', () => {
        const result = determineGaLabel(500, PageLayout.ThreeColumns);
        expect(result).toBeUndefined();
    });
});
