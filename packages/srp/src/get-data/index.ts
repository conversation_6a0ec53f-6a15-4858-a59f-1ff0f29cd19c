import queryString from 'query-string';
import fetchBuyerData from '@gumtree/web-bff/src/data/buyer/fetch';
import { HTTP_BAD_REQUEST, HTTP_GONE } from '@gumtree/middleware/src/utils/http-status-code';
import { v4 as uuidv4 } from 'uuid';
import type { GrowthBookType } from '@gumtree/shared/src/custom-typings/global';

import transform from './transform';

export default async (
    request,
    isRenderingJSON,
    growthBook: GrowthBookType,
    abortControllerForNonCorePromises?: AbortController
) => {
    const { BUYER_URL, GT_GET_MODEL } = process.env;
    const pathname = request.path;
    const qs = queryString.stringify(request.query);
    const url = `${BUYER_URL}${pathname}${qs === '' ? '' : `?${qs}`}`;

    request.headers['x-gt-get-model'] = GT_GET_MODEL!;

    const { headers: requestHeaders, remoteAddress: _remoteAddress, cookies: _cookies } = request;

    const { data, responseHeaders, statusCode } = await fetchBuyerData({
        url,
        requestHeaders,
        growthBook,
    });

    if (abortControllerForNonCorePromises) {
        abortControllerForNonCorePromises.abort();
    }

    const canBeTransformed =
        statusCode !== HTTP_GONE &&
        statusCode !== HTTP_BAD_REQUEST &&
        typeof data === 'object' &&
        data != null;

    if (canBeTransformed) {
        data.requestId = uuidv4();
    }

    if (isRenderingJSON) {
        return { data, responseHeaders, statusCode };
    }

    const transformed = canBeTransformed ? transform(data, request, growthBook) : null;

    return {
        responseHeaders,
        statusCode,
        data: {
            ...(transformed || {}),
            searchBar: {
                ...(transformed || {}).searchBar,
                isOpen: true,
            },
        },
    };
};
