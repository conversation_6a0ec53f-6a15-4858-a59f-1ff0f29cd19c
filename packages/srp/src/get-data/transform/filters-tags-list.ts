import { safeGetData as get } from '@gumtree/ui-library/src/utils/data-service';
import { toTitleCase } from '@gumtree/ui-library/src/utils/string-service';
import getBaseConfig from '@gumtree/web-bff/src/data/common/transform/base-config';
import { numberToCurrency } from '@gumtree/ui-library/src/utils/currency-service';

import type { Filter, FilterKey, RefinePanelModel } from '../../types';

export type FiltersTagType = {
    id: FilterKey;
    label: string;
    value: string;
};

export const isPropertyRentCategory = (
    l2Category: string | undefined | null,
    l3Category: string | undefined | null
): boolean => {
    return (
        l2Category === 'property-to-rent' ||
        l3Category === 'commercial-property-to-rent' ||
        l2Category === 'property-to-share' ||
        l3Category === 'garage-parking-to-rent' ||
        l2Category === 'holiday-rentals'
    );
};

const getGeneraicLabel = (
    refinePanelModelFilters: RefinePanelModel['filters'],
    tagKey: string,
    tagValue: string
) => {
    const firstFilterData = refinePanelModelFilters[tagKey]?.filterData[0];
    if (firstFilterData?.type === 'boolean') {
        return refinePanelModelFilters[tagKey]?.label;
    } else {
        return firstFilterData?.values.find((item) => item.value === tagValue)?.label || '';
    }
};

export const getTagLabel = (
    tagKey: string,
    tagValue: string,
    isPropertyRent: boolean,
    refinePanelModelFilters: RefinePanelModel['filters']
): string => {
    switch (tagKey) {
        case 'min_price':
            return `Price: from ${numberToCurrency(tagValue)}${isPropertyRent ? ' pwc' : ''}`;
        case 'max_price':
            return `Price: to ${numberToCurrency(tagValue)}${isPropertyRent ? ' pwc' : ''}`;
        case 'min_salary':
            return `Salary: from ${numberToCurrency(tagValue)}`;
        case 'max_salary':
            return `Salary: to ${numberToCurrency(tagValue)}`;
        case 'min_pet_dob':
            return `Age: from ${
                refinePanelModelFilters.pet_dob?.filterData[0].minValues.find(
                    (item) => item.value === tagValue
                )?.label
            }`;
        case 'max_pet_dob':
            return `Age: to ${
                refinePanelModelFilters.pet_dob?.filterData[0].maxValues.find(
                    (item) => item.value === tagValue
                )?.label
            }`;
        case 'pet_ready_to_leave_date':
            return `Ready to leave: ${getGeneraicLabel(refinePanelModelFilters, tagKey, tagValue)}`;
        case 'min_property_number_beds':
            return `Beds: from ${tagValue}`;
        case 'max_property_number_beds':
            return `Beds: to ${tagValue}`;
        case 'property_type':
            return `Property type: ${toTitleCase(tagValue)}`;
        case 'property_room_type':
            return `Room type: ${tagValue}`;
        case 'seller_type':
            return `Landlord type: ${toTitleCase(tagValue)}`;
        default:
            return getGeneraicLabel(refinePanelModelFilters, tagKey, tagValue);
    }
};

const getFiltersTagsList = (data): FiltersTagType[] => {
    const attributeRefinementsAsMap = get(
        'model.searchResultModel.searchCriteria.attributeRefinementsAsMap',
        data
    ) as Filter;

    const {
        category: { l2Category, l3Category },
    } = getBaseConfig(data);

    const isPropertyRent = isPropertyRentCategory(l2Category, l3Category);

    const refinePanelModelFilters = get(
        'model.refinePanelModel.filters',
        data
    ) as RefinePanelModel['filters'];

    return Object.entries(attributeRefinementsAsMap)
        .filter(([_, value]) => value !== null && value !== undefined)
        .map(([key, value]) => ({
            id: key as FilterKey,
            label: getTagLabel(key, value.toString(), isPropertyRent, refinePanelModelFilters),
            value: value.toString(),
        }));
};

export default getFiltersTagsList;
