import styled from "@emotion/styled";
import { css } from "@emotion/react";
import {
    breakpoints,
    colors,
    colorVariables,
    fontSizes,
    fontWeights,
    gutterSizes,
    mediaQuery,
} from "@gumtree/ui-library/src/base/theme";

export const DialogContainer = styled.div`
    .search-alert-dialog {
        border-radius: 8px;
        padding: 0;

        ${mediaQuery.until(breakpoints.xsmall)} {
            min-width: 290px;
        }

        ${mediaQuery.between(breakpoints.xsmall, breakpoints.small)} {
            min-width: 290px;
        }

        .dialog-title {
            font-size: ${fontSizes.header};
            margin: 20px 0 20px 20px;
        }
        .dialog-content {
            border-top: 1px solid ${colorVariables.borderLight};
            display: flex;
        }
        .dialog-close {
            height: 42px;
            width: 42px;
            margin-top: 13px;
            margin-right: 17px;
            color: ${colors.bark80};
        }

        button {
            height: 38px;
        }
    }
`;

export const LargeDialogContainer = styled.div`
    .search-alert-dialog {
        max-width: 620px;
        width: 100%;

        .dialog-content {
            flex-direction: column;

            > span {
                border-bottom: 1px solid ${colorVariables.borderLight};
                padding: 20px;
                margin-bottom: 20px;
            }
        }
        .alerts-list-href {
            color: ${colors.blue};
            cursor: pointer;
        }
    }
`;

export const SmallDialogContainer = styled.div`
    .search-alert-dialog {
        width: 320px;
        border-radius: 8px;
        padding: 0;

        ${mediaQuery.until(breakpoints.xsmall)} {
            max-width: 290px;
        }

        ${mediaQuery.until(breakpoints.msmall)} {
            max-width: 320px;
        }

        .dialog-title {
            font-size: ${fontSizes.header};
            margin: 20px 0 20px 20px;
            width: 234px;

            ${mediaQuery.until(breakpoints.xsmall)} {
                width: 200px;
            }
        }
        .dialog-content {
            border-top: 1px solid ${colorVariables.borderLight};
            display: flex;
            padding: 20px;

            a {
                color: ${colorVariables.mainTertiary};
                font-weight: ${fontWeights.lightBold};

                &:hover {
                    cursor: pointer;
                    text-decoration: underline;
                }
            }

            > span {
                margin: 0 ${gutterSizes.small};
            }
        }
    }
`;

export const ErrorDialogButton = styled.div`
    display: flex;
    justify-content: flex-end;
    width: 100%;
`;

export const DialogButtons = styled.div`
    display: flex;
    justify-content: flex-end;
    margin: 0 ${gutterSizes.large} ${gutterSizes.large} 0;
`;

export const manageAlertButtonCss = (theme) => css`
    background-color: ${theme.palette.primary.main};
    border-radius: 2px;
    color: ${theme.palette.primary.mainContrastText};
    display: flex;
    font-size: ${fontSizes.base};
    align-items: center;
    justify-content: center;
    height: 38px;
    margin-left: ${gutterSizes.base};
    padding: 0 ${gutterSizes.large};
    transition: 222ms ease-in-out;

    &:hover {
        background-color: ${theme.palette.primary.dark};
        color: ${theme.palette.primary.darkContrastText};
    }
`;
