import React from 'react';

import { render, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';

import KeywordSuggestions, { onClick } from '.';

// Mocks
jest.mock('@gumtree/shared/src/util/track-ga-event', () => ({
    trackV2: jest.fn(),
}));

jest.mock('@gumtree/shared/src/util/ga4-shared', () => ({
    trackGA4PreNav: jest.fn(),
}));

// Helper to mock window.location.href changes
const mockWindowLocation = (newLocation) => {
    delete window.location;
    window.location = {
        href: newLocation,
        pathname: '/search',
        search: '?some_irrelevant_attribute=123',
    };
};

jest.mock('@gumtree/shared/src/util/url-params', () => ({
    updateUrlWithKeyValue: jest.fn((url, key, value) => `${url}&${key}=${value}`),
}));

describe('onClick function', () => {
    // Setup a mock window object for each test
    let mockWindow;

    beforeEach(() => {
        mockWindow = {
            location: {
                href: '',
                pathname: '',
                search: '',
            },
            gumtreeDataLayer: {},
        };
    });

    afterEach(() => {
        // Clear all mocks after each test
        jest.clearAllMocks();
    });

    it('appends query parameters to /search path correctly', async () => {
        mockWindow.location.pathname = '/search';
        mockWindow.location.search = '?existing=param';

        const mockIndex = 0;

        const suggestion = { name: 'testName', categorySeoName: 'testCategory' };

        await onClick(suggestion, mockIndex, mockWindow);

        expect(mockWindow.location.href).toBe(
            '/search?existing=param&q=testName&search_category=testCategory'
        );
    });

    it('creates correct URL when not on /search path', async () => {
        mockWindow.location.pathname = '/not-search';
        mockWindow.location.search = ''; // Assuming no existing search parameters for simplicity

        const mockIndex = 0;

        const suggestion = { name: 'testName', categorySeoName: 'testCategory' };

        await onClick(suggestion, mockIndex, mockWindow);

        expect(mockWindow.location.href).toBe('/search&q=testName&search_category=testCategory');
    });
});

describe('KeywordSuggestions Component', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('renders and triggers onClick events correctly', async () => {
        const alternativeKeywordSuggestions = [
            { name: 'Suggestion1', categorySeoName: 'Category1' },
            { name: 'Suggestion2', categorySeoName: 'Category2' },
        ];

        mockWindowLocation('http://localhost');

        const { getByText } = render(
            <KeywordSuggestions alternativeKeywordSuggestions={alternativeKeywordSuggestions} />
        );

        expect(getByText('Suggestion1')).toBeInTheDocument();
        expect(getByText('Suggestion2')).toBeInTheDocument();

        await fireEvent.click(getByText('Suggestion1'));

        // Assert URL change
        expect(window.location.href).toBe(
            '/search?some_irrelevant_attribute=123&q=Suggestion1&search_category=Category1'
        );

        // Verify tracking calls
        expect(require('@gumtree/shared/src/util/track-ga-event').trackV2).toHaveBeenCalledWith(
            'ChipClick0',
            'Suggestion1',
            {}
        );
    });

    it('tracks visibility changes on load', () => {
        const alternativeKeywordSuggestions = [
            { name: 'Suggestion1', categorySeoName: 'Category1' },
            { name: 'Suggestion2', categorySeoName: 'Category2' },
        ];

        render(
            <KeywordSuggestions alternativeKeywordSuggestions={alternativeKeywordSuggestions} />
        );

        // Since visibility tracking is more about the integration with the StyledChipWithVisibilityObserver component,
        // and we do not have access to its internal workings or the StyledWithChildrenVisibilityDetector onLoad prop handling,
        // we focus on the fact that the component renders and the onClick functionality is testable.
        // Further testing would require either detailed mocking of the visibility behavior or integration tests.
    });
});
