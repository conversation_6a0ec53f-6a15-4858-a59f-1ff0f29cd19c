import {
    convertToSimplifiedSuggestions,
    removeSuggestion,
    SimplifiedSuggestion,
} from '@gumtree/ui-library/src/utils/suggestions-service';
import {
    BUSINESS_ID,
    getNewInstance as getNewG5SInstance,
} from '@gumtree/ui-library/src/utils/suggestions-service-g5s';
import type { ISuggestionsService } from '@gumtree/ui-library/src/utils/suggestions-service-interface';
import generateG5SSignatureServerSide from '@gumtree/ui-library/src/utils/g5s-signature-generator';
import { withTimeout } from '@gumtree/shared/src/util/with-timeout';

import logger from '../../logger';

const get58SearchService = (): ISuggestionsService => {
    const timeStamp = Date.now().toString();
    const g5sSignature = generateG5SSignatureServerSide(
        BUSINESS_ID,
        process.env.G5S_SIGNATURE_KEY || '',
        timeStamp
    );

    return getNewG5SInstance(BUSINESS_ID, timeStamp, g5sSignature);
};

export const searchWithAbortController = async (
    query: string | undefined,
    abortSignal?: AbortSignal
): Promise<SimplifiedSuggestion[]> => {
    return new Promise((resolve) => {
        if (abortSignal?.aborted) {
            logger.info(`58 Search API aborted before fetch began`);
            resolve([]);
            return;
        }

        if (!query) {
            resolve([]);
            return;
        }

        let resolved = false;

        const handleAbort = () => {
            if (!resolved) {
                logger.info(`58 Search API aborted during fetch`);
                resolved = true; // Prevent the original promise from resolving after abort
                resolve([]); // Resolve with an empty array upon abort
            }
        };

        if (abortSignal) {
            abortSignal.addEventListener('abort', handleAbort);
        }

        const suggestionsService = get58SearchService();

        withTimeout(suggestionsService.search(query), 800)
            .then((result) => {
                if (!resolved) {
                    resolved = true; // Mark as resolved to avoid handling abort after completion
                    const simplifiedSuggestions = convertToSimplifiedSuggestions(result);
                    const finalResult = removeSuggestion(simplifiedSuggestions, query);
                    resolve(finalResult);
                }
            })
            .catch((error) => {
                logger.warn(`58 Search API: ${error}`);
                resolve([]); // Resolve with an empty array in case of error
            })
            .finally(() => {
                // Cleanup: Remove abort event listener
                if (abortSignal && typeof abortSignal.removeEventListener === 'function') {
                    abortSignal.removeEventListener('abort', handleAbort);
                }

                if (!resolved) {
                    // In case the promise was neither resolved nor rejected
                    resolved = true;
                    resolve([]); // This is a fallback, ideally should not reach here.
                }
            });
    });
};
