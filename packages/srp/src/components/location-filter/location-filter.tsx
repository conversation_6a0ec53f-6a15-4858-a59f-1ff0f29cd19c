import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { components } from 'react-select';
import { Button, Field, Icon } from '@gumtree/ui-library';
import { redirectToUrl } from '@gumtree/ui-library/src/utils/browser-service';
import { getNonFalsyUrlSearchParams } from '@gumtree/shared/src/util/url-params';
import { DeviceTypes } from '@gumtree/shared/src/types/client-data';
import {
    locationFieldAriaLabel,
    locationFieldPlaceholderText,
} from '@gumtree/shell/src/header/search-bar/constants';

import {
    LocationIcon,
    LocationTitle,
    SearchLocation,
    StyledDialogBody,
    StyledDialog,
    StyledIcon,
    StyledInputLabel,
    StyledLocationBlock,
    StyledOptionContainer,
    StyledSearchResults,
    StyledSelect,
    StyledLocationItem,
    modalButtonsCss,
} from './location-filter.style';
import { SrpState, SrpUseDispatch } from '../../reducers';
import { updateLocationListThunk } from '../../reducers/location-list';

const CustomOption = (props) => (
    <StyledOptionContainer>
        {props.isSelected && <StyledIcon type="check" />}
        <components.Option {...props} />
    </StyledOptionContainer>
);

const CustomDropdownIndicator = (props) => (
    <components.DropdownIndicator {...props}>
        <Icon type="spinner" />
    </components.DropdownIndicator>
);

export const LocationFilter = ({ className }: { className?: string }) => {
    const dispatch = useDispatch() as SrpUseDispatch;
    const {
        searchBar,
        resultsPage: { currentFilterStates, locationTitle, distanceFilter },
        baseConfig,
        locationList: { locationList },
    } = useSelector((data: SrpState) => data);

    const [isDialogOpened, setIsDialogOpened] = useState(false);
    const [selectedDistance, setSelectedDistance] = useState<{
        value: number;
        label: string;
    } | null>(null);
    const [locationValue, setLocationValue] = useState(searchBar.location.value);
    const [isLocationList, setIsLocationList] = useState(false);

    useEffect(() => {
        const { value, displayValue } =
            distanceFilter.find((item) => item.selected) ?? distanceFilter[0];

        setSelectedDistance({ value, label: displayValue });
    }, [distanceFilter]);

    useEffect(() => {
        !locationValue && setIsLocationList(false);
    }, [locationValue]);

    const isMobile = baseConfig.device.type === DeviceTypes.MOBILE;

    const handleCloseDialog = () => {
        const { value, displayValue } =
            distanceFilter.find((item) => item.selected) ?? distanceFilter[0];

        setSelectedDistance({ value, label: displayValue });
        setLocationValue(searchBar.location.value); // reset location value to search bar value when dialog is closed
        setIsLocationList(false);
        setIsDialogOpened(false);
    };

    const handleOpenDialog = () => {
        setIsDialogOpened(true);
    };

    const distanceSelectOptions = distanceFilter.map(({ value, displayValue }) => ({
        value,
        label: displayValue,
    }));

    const handleDistanceChange = (option) => setSelectedDistance(option);
    const isDisabled = (option) =>
        distanceFilter.find((item) => item.value === option.value)?.disabled ?? false;

    const handleRunLocationSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        setLocationValue(value);

        if (value.length >= 2) {
            dispatch(updateLocationListThunk(value));
            setIsLocationList(true);
        }
    };

    const handleSelectLocation = (locationName: string) => () => {
        setLocationValue(locationName);
        setIsLocationList(false);
    };

    const handleRunFilter = () => {
        const url = getNonFalsyUrlSearchParams(currentFilterStates);

        url.set('search_location', locationValue);
        url.set('distance', selectedDistance?.value.toString()!);

        redirectToUrl(`/search?${url.toString()}`);
    };

    return (
        <>
            {isDialogOpened && (
                <StyledDialog
                    isMobile={isMobile}
                    isOpen={isDialogOpened}
                    onClose={handleCloseDialog}
                    title="Change location"
                >
                    <StyledDialogBody>
                        {!isMobile && <StyledInputLabel>Location</StyledInputLabel>}
                        <StyledLocationBlock>
                            <Icon type="beacon" size="medium" className="location-input-icon" />
                            <Field
                                aria-label={locationFieldAriaLabel}
                                placeholder={locationFieldPlaceholderText}
                                defaultValue={locationValue}
                                value={locationValue}
                                onInput={handleRunLocationSearch}
                            />
                        </StyledLocationBlock>

                        {isLocationList ? (
                            <StyledSearchResults>
                                {locationList.slice(0, 3).map((item) => (
                                    <StyledLocationItem
                                        key={item.name}
                                        onClick={handleSelectLocation(item.name)}
                                    >
                                        <Icon
                                            type="beacon"
                                            size="medium"
                                            className="location-search-icon"
                                        />
                                        {item.name}
                                    </StyledLocationItem>
                                ))}
                            </StyledSearchResults>
                        ) : null}

                        <StyledInputLabel>Radius</StyledInputLabel>
                        <StyledSelect
                            classNamePrefix="react-select"
                            components={{
                                Option: CustomOption,
                                DropdownIndicator: CustomDropdownIndicator,
                            }}
                            value={selectedDistance}
                            options={distanceSelectOptions}
                            onChange={handleDistanceChange}
                            isOptionDisabled={isDisabled}
                            menuPosition="fixed"
                            isSearchable={false}
                        />
                        {isMobile && (
                            <Button
                                display="primary"
                                label="Update"
                                className="full-update-button"
                                onClick={handleRunFilter}
                            />
                        )}
                    </StyledDialogBody>
                    {!isMobile && (
                        <div css={modalButtonsCss}>
                            <Button
                                display="secondary"
                                label="Close"
                                className="close-button"
                                onClick={handleCloseDialog}
                            />
                            <Button
                                display="primary"
                                label="Update"
                                className="update-button"
                                onClick={handleRunFilter}
                            />
                        </div>
                    )}
                </StyledDialog>
            )}

            <SearchLocation className={className} onClick={handleOpenDialog}>
                <LocationIcon>
                    <Icon type="beacon" />
                </LocationIcon>
                <LocationTitle>{locationTitle}</LocationTitle>
            </SearchLocation>
        </>
    );
};
