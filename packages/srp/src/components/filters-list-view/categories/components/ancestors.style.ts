import styled from "@emotion/styled";
import { Icon } from "@gumtree/ui-library";
import {
    colors,
    colorVariables,
    fontWeights,
    gutterSizes,
} from "@gumtree/ui-library/src/base/theme";

const shouldForwardProp = (p: string) => !["isSelected"].includes(p);

export const StyledIcon = styled(Icon)`
    position: relative;
    top: -2px;
    color: ${colors.darkGrey};
    cursor: pointer;
`;

export const StyledCheckIcon = styled(StyledIcon)`
    margin-left: auto;
`;

export const StyledCategoryName = styled("a", {
    shouldForwardProp,
})<{
    isSelected: boolean;
}>`
    width: 100%;
    margin: 0;
    font-weight: ${fontWeights.lightBold};

    &:hover {
        cursor: pointer;
    }

    ${(props) => `
        color: ${props.isSelected ? colorVariables.mainSecondary : colorVariables.mainTertiary};
    `};
`;

export const StyledCategory = styled.div`
    display: flex;
`;

export const StyledChevronIcon = styled(Icon)`
    color: ${colorVariables.mainTertiary};
`;

export const StyledCategories = styled("div")`
    display: flex;
    flex-direction: column;
    grid-gap: ${gutterSizes.medium};
    color: ${colorVariables.mainTertiary};
`;
