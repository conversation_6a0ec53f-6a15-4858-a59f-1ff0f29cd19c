import React from 'react';
import { useDispatch } from 'react-redux';
import { qaAttribute } from '@gumtree/ui-library/src/utils/qa-service';
import { redirectToUrl } from '@gumtree/shared/src/util/browser-service';

import type { SrpUseDispatch } from '../../../../reducers';
import type { FilterItemWithState } from '../../../filters/util';
import { trackGA4FiltersDiffThunk } from '../../../../reducers/current-filter-states';
import {
    StyledAbundance,
    StyledCategoryName,
    StyledListViewSubCategories,
    StyledSubCategory,
} from './descendents.style';

const shouldShowSeoLink = (abundance: number) => {
    const minAbundanceRequiredForSeo = 4;
    return abundance >= minAbundanceRequiredForSeo;
};

const Descendents = ({
    descendentCategoriesProcessed,
}: {
    descendentCategoriesProcessed: FilterItemWithState[];
}) => {
    const dispatch = useDispatch() as SrpUseDispatch;

    return (
        <StyledListViewSubCategories
            isSubCategoriesItems={Boolean(descendentCategoriesProcessed.length)}
            {...qaAttribute('sub-category-list')}
        >
            {descendentCategoriesProcessed.map(
                ({ label, value, abundance, uri, isChecked, isVisible }) => {
                    return (
                        <StyledSubCategory
                            key={value}
                            isVisible={isVisible}
                            {...qaAttribute('sub-category-list-item')}
                        >
                            <StyledCategoryName
                                {...qaAttribute('sub-category-list-item-link')}
                                isSelected={isChecked}
                                onClick={() => {
                                    dispatch(
                                        trackGA4FiltersDiffThunk({
                                            updatedSearchFilters: { listingSubCategory: value },
                                            searchResults: abundance,
                                            searchFilters: { search_category: value },
                                        })
                                    );
                                    redirectToUrl(uri);
                                }}
                                href={shouldShowSeoLink(abundance) ? uri : undefined}
                            >
                                {label}
                            </StyledCategoryName>
                            <StyledAbundance>{abundance}</StyledAbundance>
                        </StyledSubCategory>
                    );
                }
            )}
        </StyledListViewSubCategories>
    );
};

export default Descendents;
