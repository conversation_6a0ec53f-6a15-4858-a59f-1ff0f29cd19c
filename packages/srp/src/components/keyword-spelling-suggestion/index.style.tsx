import React, { ReactEventHandler } from 'react';

import { css } from '@emotion/react';

import { colorVariables, gutterSizes } from '@gumtree/ui-library/src/base/theme';

export const SpellingSuggestion = ({
    children,
    onClick,
}: React.PropsWithChildren & { onClick: ReactEventHandler }) => {
    const styles = css`
        font-weight: 500;
        font-size: 16px;
        line-height: 28px;
        text-decoration: underline;
        border: none;
        padding: 0;
        margin: 0;
        margin-bottom: ${gutterSizes.base};
        background-color: transparent;
        cursor: pointer;
        text-align: left;
        color: ${colorVariables.textPrimary};
    `;

    return (
        <button type="button" onClick={onClick} css={[styles]}>
            {children}
        </button>
    );
};

export const H1Bold = ({ children }: React.PropsWithChildren) => {
    const styles = css`
        font-weight: 700;
    `;

    return <span css={[styles]}>{children}</span>;
};

export const H1Minor = ({ children }: React.PropsWithChildren) => {
    const styles = css`
        font-weight: 400;
    `;

    return <span css={[styles]}>{children}</span>;
};
