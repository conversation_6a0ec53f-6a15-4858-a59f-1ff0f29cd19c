import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { useSelector } from 'react-redux';
import { redirectToUrl } from '@gumtree/ui-library/src/utils/browser-service';

import KeywordSpellingSuggestion from '.';
import { CORRECTED_KEYWORD_FOR_REDO, CORRECTED_KEYWORD_FOR_UNDO } from './constants';

jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
}));

jest.mock('@gumtree/ui-library/src/utils/browser-service', () => ({
    redirectToUrl: jest.fn(),
}));

describe('KeywordSpellingSuggestion', () => {
    const mockState = {
        resultsPage: {
            keywordCorrection: {},
        },
        searchBar: {
            location: {
                value: 'test-location',
            },
        },
    };

    beforeEach(() => {
        (useSelector as jest.Mock).mockImplementation((selector) => selector(mockState));
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('renders nothing when there is no spelling suggestion', () => {
        render(<KeywordSpellingSuggestion />);
        expect(screen.queryByText(/Search instead for/)).not.toBeInTheDocument();
    });

    it('renders spelling suggestion for initial auto-correction', () => {
        const autoCorrectState = {
            ...mockState,
            resultsPage: {
                keywordCorrection: {
                    autoCorrectInitial: {
                        correctedKeyword: 'corrected',
                        originalKeyword: 'originalKeyword',
                    },
                },
            },
        };
        (useSelector as jest.Mock).mockImplementation((selector) => selector(autoCorrectState));

        render(<KeywordSpellingSuggestion />);
        expect(screen.getByText(/Search instead for/)).toBeInTheDocument();
        expect(screen.getByText('originalKeyword')).toBeInTheDocument();
    });

    it('renders spelling suggestion for undone auto-correction', () => {
        const undoneState = {
            ...mockState,
            resultsPage: {
                keywordCorrection: {
                    autoCorrectUndone: { correctedKeyword: 'corrected' },
                },
            },
        };
        (useSelector as jest.Mock).mockImplementation((selector) => selector(undoneState));

        render(<KeywordSpellingSuggestion />);
        expect(screen.getByText(/Search instead for/)).toBeInTheDocument();
        expect(screen.getByText('corrected')).toBeInTheDocument();
    });

    it('redirects to the correct URL when suggestion is clicked for initial auto-correction', () => {
        const autoCorrectState = {
            ...mockState,
            resultsPage: {
                keywordCorrection: {
                    autoCorrectInitial: {
                        correctedKeyword: 'corrected',
                        originalKeyword: 'originalKeyword',
                    },
                },
            },
        };
        (useSelector as jest.Mock).mockImplementation((selector) => selector(autoCorrectState));

        render(<KeywordSpellingSuggestion />);
        fireEvent.click(screen.getByText(/Search instead for/));

        expect(redirectToUrl).toHaveBeenCalledWith(
            `/search?search_location=test-location&q=originalKeyword&${CORRECTED_KEYWORD_FOR_UNDO}=corrected`
        );
    });

    it('redirects to the correct URL when suggestion is clicked for undone auto-correction', () => {
        const undoneState = {
            ...mockState,
            resultsPage: {
                keywordCorrection: {
                    autoCorrectUndone: { correctedKeyword: 'corrected' },
                },
            },
        };
        (useSelector as jest.Mock).mockImplementation((selector) => selector(undoneState));

        render(<KeywordSpellingSuggestion />);
        fireEvent.click(screen.getByText(/Search instead for/));

        expect(redirectToUrl).toHaveBeenCalledWith(
            `/search?search_location=test-location&q=corrected&${CORRECTED_KEYWORD_FOR_REDO}=corrected`
        );
    });
});
