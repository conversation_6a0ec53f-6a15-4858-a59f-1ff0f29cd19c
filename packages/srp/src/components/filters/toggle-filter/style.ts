import styled from '@emotion/styled';
import {
    colorVariables,
    gutterSizes,
    colors,
    fontWeights,
} from '@gumtree/ui-library/src/base/theme';

export const StyledToggleFilterContainer = styled.div`
    margin-bottom: ${gutterSizes.large};

    & > input {
        & + label {
            padding-left: 27px;
            font-weight: ${fontWeights.normal};
            position: relative;
            cursor: pointer;
            &:before {
                width: ${gutterSizes.large18};
                height: ${gutterSizes.large18};
                box-sizing: border-box;
                content: '';
                position: absolute;
                left: 0;
                top: 100%;
                margin-top: -${gutterSizes.large18};
                border-radius: ${gutterSizes.small};
                border-width: ${gutterSizes.xsmall};
                border-color: ${colors.bark60};
                border-style: solid;

                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
        &:checked {
            & + label {
                &:before {
                    background-color: ${colors.blue};
                    border-color: ${colors.blue};
                    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='3' stroke-linecap='butt' stroke-linejoin='miter' xmlns='http://www.w3.org/2000/svg'%3E%3Cpolyline points='6 12 10 16 18 8'%3E%3C/polyline%3E%3C/svg%3E");
                    background-repeat: no-repeat;
                    background-position: center;
                    background-size: 18px 18px;
                }
            }
        }
    }
`;

export const StyledToggle = styled.input``;

export const StylesLabel = styled.label``;

export const StylesQuantityLabelContainer = styled.span`
    display: inline-block;
    color: ${colorVariables.textSecondary};
    margin-left: ${gutterSizes.base};
`;
