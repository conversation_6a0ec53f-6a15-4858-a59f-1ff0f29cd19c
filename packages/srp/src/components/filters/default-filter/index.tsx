import React, { useState } from 'react';
import { useDispatch } from 'react-redux';

import { redirectToUrl } from '@gumtree/ui-library/src/utils/browser-service';

import { FilterItemWithState, FilterProcessor } from '../util';

import RadioButtonList from './components/radio-button-list';
import AbbreviationToggle from './components/abbreviation-toggle';
import { trackGA4FiltersDiffThunk, setFilterThunk, setSingleFilterState } from '../../../reducers/current-filter-states';
import { updateSearchResultAbundance } from '../../../reducers/search-result-abundance';
import { SrpUseDispatch } from '../../../reducers';
import type { FilterItem, FilterKey, FilterValue } from '../../../types';

export const onLabelLinkClick =
    (filterKey: FilterKey, dispatch: SrpUseDispatch, isStateful: boolean) =>
    (item: FilterItemWithState) =>
    () => {
        if (isStateful) {
            dispatch(updateSearchResultAbundance(item.abundance));
            dispatch(setFilterThunk({ key: filterKey, value: item.value }));
        } else {
            dispatch(setSingleFilterState({ key: filterKey, value: item.value }));
            dispatch(trackGA4FiltersDiffThunk({
                searchFilters: {
                    [filterKey]: item.value
                }
            }));
            redirectToUrl(item.uri);
        }
    };

const DefaultFilter = ({
    filterKey,
    shortListLength,
    isStateful,
    filterItemsRaw,
    selectedFilterValue,
    onExpandToggle = () => {},
}: {
    filterKey: FilterKey;
    shortListLength: number;
    isStateful: boolean;
    filterItemsRaw: FilterItem[];
    selectedFilterValue: FilterValue;
    onExpandToggle?: (newToggleState: boolean) => void;
}) => {
    const dispatch = useDispatch() as SrpUseDispatch;

    const [isShowFullList, setIsShowFullList] = useState(false);

    const totalItemsSize = filterItemsRaw.length;

    const filterItemsProcessed = new FilterProcessor(filterItemsRaw)
        .markAsChecked(selectedFilterValue)
        .putIsCheckedInFront()
        .hideOverflow(shortListLength, isShowFullList)
        .getItems();

    return totalItemsSize ? (
        <>
            <RadioButtonList
                filterItemsProcessed={filterItemsProcessed}
                filterKey={filterKey}
                onLabelLinkClick={onLabelLinkClick(filterKey, dispatch, isStateful)}
            />
            <AbbreviationToggle
                totalItemsSize={totalItemsSize}
                isDisplayShowLess={isShowFullList}
                setIsShowFullList={(newToggleState) => {
                    onExpandToggle(newToggleState);
                    setIsShowFullList(newToggleState);
                }}
                shortListLength={shortListLength}
            />
        </>
    ) : (
        <></>
    );
};

export default DefaultFilter;
