
import { css } from '@emotion/react';

import { colorVariables, gutterSizes } from '@gumtree/ui-library/src/base/theme';

export const StyledContainerStyle = css`
    display: flex;
    align-items: start;
    gap: ${gutterSizes.medium};
`;

export const StyledLabelStyle = css``;

export const StyledQuantityLabelContainerStyle = css`
    color: ${colorVariables.textSecondary};
    padding-right: ${gutterSizes.small};
`;
