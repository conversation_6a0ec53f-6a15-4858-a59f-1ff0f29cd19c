// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FrameAccordion default opened state is based on EXPANDED_FILTERS 1`] = `
<DocumentFragment>
  <div
    class="css-fnhdbv-accordion ec57kz1"
  >
    <div
      class="css-wkvfjm-accordion ec57kz0"
    >
      <div>
        Test Title
      </div>
      <span
        aria-hidden="true"
        class="icon icon--chevron-u css-0 eom5h670"
      />
    </div>
    <div
      class="filter-content"
    >
      <form
        class="css-gozvwd-frame e1fshcm00"
      >
        <div>
          Test Child
        </div>
      </form>
    </div>
  </div>
</DocumentFragment>
`;

exports[`FrameAccordion renders FrameAccordion with children 1`] = `
<DocumentFragment>
  <div
    class="css-fnhdbv-accordion ec57kz1"
  >
    <div
      class="css-wkvfjm-accordion ec57kz0"
    >
      <div>
        Test Title
      </div>
      <span
        aria-hidden="true"
        class="icon icon--chevron-d css-0 eom5h670"
      />
    </div>
  </div>
</DocumentFragment>
`;
