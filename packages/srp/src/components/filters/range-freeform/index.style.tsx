import styled from '@emotion/styled';
import { colors, fontSizes, gutterSizes, lineHeights } from '@gumtree/ui-library/src/base/theme';

import Select from '../select';

export const StyledSelect = styled(Select)`
    padding: 3px;
    margin-bottom: ${gutterSizes.medium};
    &:focus-within {
        padding: 1px;
        border: 2px solid ${colors.gtallBlue};
        border-radius: 8px;
    }
`;

export const StyledText = styled.div`
    font-size: ${fontSizes.small};
    line-height: ${lineHeights.small};
`;

export const StyledTo = styled(StyledText)`
    font-size: ${fontSizes.base};
    line-height: ${lineHeights.base};
    margin-bottom: ${gutterSizes.medium};
`;
