import React from 'react';
import { useDispatch } from 'react-redux';

import { SelectOptionType } from '@gumtree/shared/src/constants/price-ranges';
import { isANumber } from '@gumtree/ui-library/src/utils/currency-service';

import { setSingleFilterState } from '../../../reducers/current-filter-states';

import Select from '../select';
import { StyledTo } from './style';
import type { FilterData, FilterKey, FilterValue } from '../../../types';

const DEFAULT_STATE = '';

const RangeFilter = ({
    filterKey,
    initialMinValue,
    initialMaxValue,
    minKey,
    maxKey,
    minValues,
    maxValues,
    minCount,
    maxCount,
    isStateful,
}: {
    filterKey: FilterKey;
    initialMinValue: string;
    initialMaxValue: string;
    minKey: NonNullable<FilterData['minKey']>;
    maxKey: NonNullable<FilterData['maxKey']>;
    minValues: NonNullable<FilterData['minValues']>;
    maxValues: NonNullable<FilterData['maxValues']>;
    minCount: string;
    maxCount: string;
    isStateful: boolean;
}) => {
    const dispatch = useDispatch();

    const isDisabledForOption = (
        option: SelectOptionType,
        initialValue: string,
        compareValue: string,
        isMin: boolean
    ) => {
        if (option.value === initialValue || !isANumber(option.value) || !isANumber(compareValue)) {
            return false;
        } else {
            return isMin
                ? parseInt(option.value) > parseInt(compareValue)
                : parseInt(option.value) < parseInt(compareValue);
        }
    };

    const updateCount = (initialValue: string, key: FilterKey, value: FilterValue) => {
        if (value === initialValue) {
            dispatch(setSingleFilterState({ key, value: DEFAULT_STATE }));
        } else {
            dispatch(setSingleFilterState({ key, value }));
        }
    };

    const onMinChange = (option: SelectOptionType) =>
        updateCount(initialMinValue, minKey, option.value);
    const onMaxChange = (option: SelectOptionType) =>
        updateCount(initialMaxValue, maxKey, option.value);

    const currentMin = minValues.find((value) => value.value === minCount);
    const currentMax = maxValues.find((value) => value.value === maxCount);

    return (
        <>
            <Select
                id={`filter-${filterKey}-min`}
                label={isStateful ? 'Min.' : ''}
                value={{
                    value: currentMin?.value as string,
                    label: currentMin?.label as string,
                }}
                options={minValues}
                onChange={onMinChange}
                isOptionDisabled={(option: SelectOptionType) =>
                    isDisabledForOption(option, initialMinValue, maxCount, true)
                }
            />
            {isStateful && <StyledTo>to</StyledTo>}
            <Select
                id={`filter-${filterKey}-max`}
                label={isStateful ? 'Max.' : ''}
                value={{
                    value: currentMax?.value as string,
                    label: currentMax?.label as string,
                }}
                options={maxValues}
                onChange={onMaxChange}
                isOptionDisabled={(option: SelectOptionType) =>
                    isDisabledForOption(option, initialMaxValue, minCount, false)
                }
            />
        </>
    );
};

export default RangeFilter;
