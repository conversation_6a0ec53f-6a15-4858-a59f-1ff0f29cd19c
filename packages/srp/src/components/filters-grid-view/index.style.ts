import styled from "@emotion/styled";
import { css } from "@emotion/react";
import { Button } from "@gumtree/ui-library";
import {
    colorVariables,
    gutterSizes,
    mediaQuery,
    breakpoints,
    colors,
    fontSizes,
    zIndexes,
    fontWeights,
} from "@gumtree/ui-library/src/base/theme";

const shouldForwardProp = (p: string) => !["isDialogOpened"].includes(p);

export const FilterButtonContainer = styled("div", { shouldForwardProp })<{
    isDialogOpened: boolean;
}>`
    margin-right: ${gutterSizes.base};

    ${({ isDialogOpened }) => {
        return isDialogOpened && `width: 90px;`;
    }}

    .filter-dialog {
        ${mediaQuery.from(breakpoints.large)} {
            min-width: 620px;
            padding: 0;
            border-radius: ${gutterSizes.base};
        }

        ${mediaQuery.until(breakpoints.large)} {
            height: 100vh;
            min-width: 100%;
            padding: 0 0 77px 0;
        }

        ${mediaQuery.until(breakpoints.xsmall)} {
            min-width: 320px;
        }

        .dialog-title {
            border-bottom: 1px solid ${colorVariables.borderLight};
            font-size: ${fontSizes.xlarge};
            padding: 20px 0 20px ${gutterSizes.large};
            margin: 0;

            ${mediaQuery.from(breakpoints.large)} {
                margin: ${gutterSizes.large} ${gutterSizes.xxlarge};
            }
        }

        .dialog-close {
            height: 36px;
            width: 36px;
            margin: ${gutterSizes.large} ${gutterSizes.large} 0 0;

            ${mediaQuery.from(breakpoints.large)} {
                margin: ${gutterSizes.xxlarge} 40px 0 0;
            }

            .icon {
                color: ${colors.bark80};
                font-size: ${fontSizes.medium};
                font-weight: ${fontWeights.normal};
            }
        }
    }

    .dialog-container {
        transition: transform 0.2s;
        transform-origin: left;
        transition-timing-function: linear;

        ${mediaQuery.from(breakpoints.large)} {
            position: relative;
            top: ${gutterSizes.xxxlarge};
            width: max-content;
            height: calc(100vh - 2 * ${gutterSizes.xxxlarge});
            margin: auto;
        }
    }
`;

export const filterButtonCss = (theme) => css`
    && {
        .icon {
            color: ${theme.palette.iconBtn.icon};
        }

        border-color: ${theme.palette.secondary.mainContrastText};
        border-radius: 4px;
        height: 38px;

        ${mediaQuery.until(breakpoints.msmall)} {
            .icon {
                display: none;
            }
        }

        &:hover {
            background: ${theme.palette.secondary.dark};
            color: ${theme.palette.secondary.darkContrastText};
        }
    }
`;
export const StyledFilterButton = styled(Button)``;

export const SearchContainer = styled.div`
    position: relative;
    z-index: ${zIndexes.high};
    background-color: ${colors.white};
    border-top: 1px solid ${colors.bark20};

    .button--tertiary {
        color: ${colors.blue};
        background-color: ${colors.white};
        border-color: transparent;

        &:hover {
            color: ${colors.blue};
            background: ${colors.white};
            text-decoration: underline;
        }

        &:active {
            background-color: ${colors.white};
        }
    }

    ${mediaQuery.until(breakpoints.large)} {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding: ${gutterSizes.large};
        z-index: ${zIndexes.high + 3};
    }

    ${mediaQuery.from(breakpoints.large)} {
        display: flex;
        justify-content: end;
        padding: ${gutterSizes.large} ${gutterSizes.xxlarge};

        .button--primary {
            width: 182px;
        }
    }
`;

export const StyledContentContainer = styled.div`
    ${mediaQuery.from(breakpoints.large)} {
        height: 590px;
        max-height: calc(100vh - 300px);
        overflow: auto;
        margin: 0 ${gutterSizes.xxlarge};

        & > div:last-child {
            border-bottom: 0;
        }
    }
`;
