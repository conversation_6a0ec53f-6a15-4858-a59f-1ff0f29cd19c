import styled from '@emotion/styled';
import { fontSizes, gutterSizes, colors, fontWeights } from '@gumtree/ui-library/src/base/theme';

export const StyledRangeFilterFreeform = styled.div`
    display: flex;
    align-items: flex-end;
    grid-gap: ${gutterSizes.medium};
    margin-top: -${gutterSizes.base};

    .field {
        margin-bottom: 0;
    }

    .icon {
        color: ${colors.bark80};
    }

    & input {
        width: 100%;
        height: 38px;
        border-radius: 4px;
        font-size: ${fontSizes.medium};
        border-color: ${colors.bark40};
    }

    & label {
        font-size: ${fontSizes.small};
        font-weight: ${fontWeights.normal};
        margin-bottom: ${gutterSizes.xsmall};
    }

    .form-element {
        .button {
            height: 100%;
        }

        padding: 3px;
        &:focus-within {
            padding: 1px;
            border: 2px solid ${colors.gtallBlue};
            border-radius: 4px;
        }
    }
`;
