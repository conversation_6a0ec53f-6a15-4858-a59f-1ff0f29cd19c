import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import Ancestors from './ancestors';

describe('Ancestors Component', () => {
    const mockCategories = [
        { label: 'Category 1', value: 'cat1', isChecked: false },
        { label: 'Category 2', value: 'cat2', isChecked: true },
        { label: 'Category 3', value: 'cat3', isChecked: false },
    ];

    const mockOnCategorySelect = jest.fn();

    it('renders correctly with categories', () => {
        const { asFragment } = render(
            <Ancestors categories={mockCategories} onCategorySelect={mockOnCategorySelect} />
        );
        expect(asFragment()).toMatchSnapshot();
    });

    it('displays the correct number of categories', () => {
        const rendered = render(
            <Ancestors categories={mockCategories} onCategorySelect={mockOnCategorySelect} />
        );
        expect(
            rendered.container.querySelectorAll('[data-q="ancestor-category-name"]')
        ).toHaveLength(mockCategories.length);
    });

    it('shows check icon for checked categories', () => {
        const rendered = render(
            <Ancestors categories={mockCategories} onCategorySelect={mockOnCategorySelect} />
        );
        const checkedCategories = rendered.container.querySelectorAll(
            '[data-q="ancestor-category-name-check-icon"]'
        );
        expect(checkedCategories).toHaveLength(
            mockCategories.filter((cat) => cat.isChecked).length
        );
    });

    it('calls onCategorySelect on category click', () => {
        const rendered = render(
            <Ancestors categories={mockCategories} onCategorySelect={mockOnCategorySelect} />
        );
        const firstCategory = rendered.container.querySelector('[data-q="ancestor-category-name"]');
        fireEvent.click(firstCategory);
        expect(mockOnCategorySelect).toHaveBeenCalledWith(mockCategories[0].value);
    });
});
