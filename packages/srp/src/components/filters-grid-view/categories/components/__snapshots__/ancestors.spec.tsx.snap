// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Ancestors Component renders correctly with categories 1`] = `
<DocumentFragment>
  <div
    class="css-9wr74w-components e16ibf3p3"
  >
    <div
      class="css-1rjmcz7-components e16ibf3p0"
    >
      <a
        class="css-1kf18jt-components e16ibf3p1"
        data-q="ancestor-category-name"
      >
        Category 1
      </a>
    </div>
    <div
      class="css-1rjmcz7-components e16ibf3p0"
    >
      <a
        class="css-1uykij4-components e16ibf3p1"
        data-q="ancestor-category-name"
      >
        Category 2
      </a>
      <span
        aria-hidden="true"
        class="icon icon--check css-65ugiv-components-components e16ibf3p2"
        data-q="ancestor-category-name-check-icon"
      />
    </div>
    <div
      class="css-1rjmcz7-components e16ibf3p0"
    >
      <a
        class="css-1kf18jt-components e16ibf3p1"
        data-q="ancestor-category-name"
      >
        Category 3
      </a>
    </div>
  </div>
</DocumentFragment>
`;
