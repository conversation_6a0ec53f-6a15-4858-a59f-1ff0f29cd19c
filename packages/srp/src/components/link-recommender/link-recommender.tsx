import React from 'react';
import { shallowEqual, useSelector } from 'react-redux';
import { LinkList } from '@gumtree/ui-library';
import { Container } from './link-recommender.style';
import type { SrpState } from '../../reducers';

export function LinkRecommender() {
    const links = useSelector(
        ({ resultsPage: { linkRecommenderList } }: SrpState) =>
            Array.isArray(linkRecommenderList) ? linkRecommenderList : [],
        shallowEqual
    );
    const mappedLinks = links.map(({ url, name }) => ({ text: name, href: url }));

    if (!mappedLinks.length) {
        return null;
    }
    return (
        <Container data-testid="link-recommender">
            <LinkList items={mappedLinks} />
        </Container>
    );
}
