import winston, { LeveledLogMethod } from 'winston';
import formatter from './formatter';
import getConsoleLevel from './level';

export interface RichContextLogger {
    warn: (message: string, sourceFileName?: string, error?: Error) => void;
    debug: (message: string, sourceFileName?: string, error?: Error) => void;
    info: (message: string, sourceFileName?: string, meta?: any) => void;
    error: (message: string, sourceFileName?: string, error?: Error, cookie?: string) => void;
}

const callLogger = (
    logger: LeveledLogMethod,
    message: string,
    sourceFileName?: string,
    error?: Error,
    cookie?: string
) => {
    if (error && error.hasOwnProperty('message') && error.hasOwnProperty('stack')) {
        logger(
            `[${sourceFileName}] Error message: ${message} \n Stack trace: ${error.stack}${
                cookie ? `\n Experiments cookie: ${cookie}` : ''
            }`
        );
    } else {
        logger(
            `${sourceFileName ? `[${sourceFileName}] ` : ''}${message}${
                cookie ? `\n Experiments cookie: ${cookie}` : ''
            }`
        );
    }
};

/**
 * Configure winston logger for only console output
 */
export default (name: string): RichContextLogger => {
    const logger = winston.createLogger({
        level: getConsoleLevel(process.env),
        format: formatter(name),
        exitOnError: true,
        transports: [new winston.transports.Console({ handleExceptions: true })],
    });

    return {
        warn: (message, sourceFileName, error) => {
            callLogger(logger.warn, message, sourceFileName, error);
        },
        debug: (message, sourceFileName, error) => {
            callLogger(logger.debug, message, sourceFileName, error);
        },
        info: (message, sourceFileName, error) => {
            callLogger(logger.info, message, sourceFileName, error);
        },
        error: (message, sourceFileName, error, cookie = 'none') => {
            callLogger(logger.error, message, sourceFileName, error, cookie);
        },
    };
};
