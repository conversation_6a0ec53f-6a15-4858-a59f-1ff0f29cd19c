import type { AnyAction } from 'redux';
import type { ThunkDispatch } from 'redux-thunk';
import commonReducers, { updateCommonData } from '@gumtree/shell/src/reducers/common';
import page, { updatePage } from '@gumtree/shell/src/reducers/page';
import buyerReducers, { updateBuyerData } from '@gumtree/shell/src/reducers/buyer';
import type { KeyedReturnTypes } from '@gumtree/shared/src/model/generic.model';
import { hideSearchBar } from '@gumtree/shell/src/header/search-bar/search-bar-actions';
import type { SyiData } from '../get-data';
import syi, { updateSyi } from './syi';
import { updateCategoryNav } from '@gumtree/shell/src/reducers/common/category-nav';

export const showPage =
    ({
        title,
        description,
        pageData,
        postAdPageType,
        draftExists,
        postadURL,
        initialFetchURL,
        saveFetchURL,
        ...data
    }: SyiData) =>
    (dispatch) => {
        dispatch(updateCommonData(data as any));
        dispatch(updateBuyerData(data as any));
        dispatch(hideSearchBar());
        dispatch(updatePage({ title, description }));
        dispatch(
            updateSyi({
                context: {
                    ...pageData,
                    postAdPageType,
                    draftExists,
                    postadURL,
                    initialFetchURL,
                    saveFetchURL,
                },
            })
        );
        dispatch(
            updateCategoryNav({
                ...data.categoryNav,
                display: false,
            })
        );
    };

const index = {
    ...commonReducers,
    ...buyerReducers,
    page,
    syi,
    persistedStateData,
};

export type SyiState = KeyedReturnTypes<typeof index>;

export type SyiUseDispatch = ThunkDispatch<SyiState, any, AnyAction>;

export default index;

/**
 * 🚧 Appears to come from message-centre in legacy.
 * > frontend/src/seller/message-centre/services/store-setup.js
 */
function persistedStateData(state = {}, _action) {
    return state;
}
