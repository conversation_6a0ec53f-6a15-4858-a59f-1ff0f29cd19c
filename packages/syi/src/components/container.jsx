import React from 'react';
import PropTypes from 'prop-types';

import allComponents from './all-components';
import { helperFactory } from '../services/dtl-syi';
import { PanelWrapper } from '../styles';

export default function PostAdContainer({ panels, jobsConfig }) {
    return (
        <PanelWrapper data-name="post-ad-container">
            {panels.map(({ name, data }) => {
                if (!allComponents[name]) {
                    return <></>;
                }

                const { suppPanel = '' } = data;
                const dataWithJobsConfig = { ...data, jobsConfig };
                const panel = { component: allComponents[name] || allComponents[suppPanel] };
                return (
                    <panel.component key={name} {...dataWithJobsConfig} Helpers={helperFactory} />
                );
            })}
        </PanelWrapper>
    );
}

PostAdContainer.propTypes = {
    panels: PropTypes.array.isRequired,
    jobsConfig: PropTypes.object,
};

PostAdContainer.defaultProps = {
    jobsConfig: {},
};
