export const PanelNameMapping = {
    AD_TITLE: 'ad-title',
    ATTRIBUTE_PANEL: 'attribute-panel',
    PET_ADVICE_PANEL: 'pet-advice-panel',
    SELLER_SKILLS: 'seller-skills',
    BRANDS: 'brands',
    BUMP: 'bump',
    CATEGORY: 'category',
    CONFIRMATION: 'confirmation',
    CONTACT_DETAILS: 'contact-details',
    CONTACT_DETAILS_PRO: 'contact-details-pro',
    CONTINUE: 'continue',
    DESCRIPTION: 'description',
    LEGAL: 'legal',
    LOCATION: 'location',
    PROPERTY_DETAILS: 'property-details',
    AQUARIUM_DETAILS: 'aquarium-details',
    IMAGES: 'images',
    INSERTION: 'insertion',
    OVERALL_PRICE: 'overall-price',
    PETS_BIRTHDAY: 'pets-birthday',
    MO<PERSON>LE_PHONES_ATTRIBUTES_PANEL: 'mobile-phones-attributes-panel',
    PRICE: 'price',
    ADDITIONAL_FEATURES: 'additional-features',
    SELLER_TYPE: 'seller-type',
    VEHICLE_SPECIFICATIONS: 'vehicle-specifications',
    WEBSITE_LINK: 'website-link',

    // common
    CURRENCY_INPUT: 'currency-input',
    DATE_INPUT: 'date-input',
    DROPDOWN_INPUT: 'dropdown-input',
    RADIO_INPUT: 'radio-input',
    TEXTFIELD_INPUT: 'textfield-input',
    NUMBERFIELD_INPUT: 'numberfield-input',
} as const;

export type PanelName = typeof PanelNameMapping[keyof typeof PanelNameMapping];
