import React from 'react';
import { useSelector } from 'react-redux';
import { render } from '@testing-library/react';
import { mocked } from 'jest-mock';
import { L3CategoryIds } from '@gumtree/shared/src/types/categories';

import AquariumDetails from '../aquarium-details';

jest.mock('react-redux', () => ({
    useDispatch: jest.fn(() => () => {}),
    connect: jest.fn(() => (c) => c),
    useSelector: jest.fn(() => ({
        expandCategory: false,
        radioCategoryType: null,
        radioShowAlert: false,
        context: {
            postAdPageType: null,
        },
    })),
}));
const mockedUseSelector = mocked(useSelector);

it('renders when categoryId matches', () => {
    const { asFragment } = render(
        <AquariumDetails form={{ categoryId: L3CategoryIds.AQUARIUM }} />
    );
    expect(asFragment()).toMatchSnapshot();
});

it('render component when showalert is true', () => {
    mockedUseSelector.mockImplementation(() => ({
        expandCategory: true,
        radioCategoryType: null,
        radioShowAlert: true,
        context: {
            postAdPageType: null,
        },
    }));
    const { asFragment } = render(
        <AquariumDetails form={{ categoryId: L3CategoryIds.AQUARIUM }} />
    );
    expect(asFragment()).toMatchSnapshot();
});

it('does not render component when pageType is EditAd', () => {
    mockedUseSelector.mockImplementation(() => ({
        expandCategory: true,
        radioCategoryType: null,
        radioShowAlert: false,
        context: {
            postAdPageType: 'edit',
        },
    }));
    const { asFragment } = render(
        <AquariumDetails form={{ categoryId: L3CategoryIds.AQUARIUM }} />
    );
    expect(asFragment()).toMatchSnapshot();
});
