import React from 'react';
import cx from 'classnames';
import { carListingOptionsCss } from '../../styles';
import { useDispatch, useSelector } from 'react-redux';
import { SyiState, SyiUseDispatch } from '../../reducers';
import { selectListingTypeThunk } from '../../reducers/syi';
import { qaAttribute } from '@gumtree/ui-library/src/utils/qa-service';
import ThemeAwareLink from '@gumtree/ui-library/src/theme-aware-link/theme-aware-link';
import {
    ActionListingDetails,
    CreateDealerAuctionLinkContainer,
    IconInCircle,
    LinkBetweenItems,
    ListItem,
    PartnershipHeader,
    StyledLinkLabel,
} from './car-listing-options.style';
import ConfirmationLegalLinks from './confirmation-legal-links';

import DealerAuctionIcon from './img/dealer-auction-link.svg';
import { trackV2 } from '@gumtree/shell/src/common/common-actions';
import { trackGA4Event } from '@gumtree/shared/src/util/ga4-shared';

import CarAuctionInstantValuation from './img/car-auction-instant-valuation.svg';
import CarAuctionDealerBid from './img/car-auction-dealers-bid.svg';
import CarAuctionFinalise from './img/car-auction-finalise.svg';
import { computeListingDetails } from '../../services/syi-model';

const CarListingOptions = () => {
    const dispatch = useDispatch() as SyiUseDispatch;

    const { baseConfig, expandCategory, selectedListingType, appViewDevice } = useSelector(
        ({
            baseConfig,
            syi: { expandCategory, selectedListingType },
            page: { appViewDevice },
        }: SyiState) => ({
            expandCategory,
            selectedListingType,
            baseConfig,
            appViewDevice,
        })
    );

    const carWowUrl =
        'https://carwow-uk.pxf.io/c/5973247/2253616/15470?subId1=newlp&u=https%3A%2F%2Fwww.carwow.co.uk%2Fsell-my-car-gumtree';

    const updateSelectedListingType = (type) => {
        dispatch(selectListingTypeThunk(type));
    };

    return (
        <div
            className="survey-panel"
            css={carListingOptionsCss}
            {...qaAttribute('car-listing-options')}
        >
            <div
                className={cx(
                    { 'is-closed': expandCategory },
                    `panel ${
                        selectedListingType !== 'dealer' ? 'has-border' : ''
                    } car-listing-options-panel`
                )}
                data-validation-group="car-listing-options"
            >
                <h2 className="panel-title">
                    <span>
                        <span className="panel-validation" />
                        {''}How do you want to sell your car on Gumtree?
                    </span>
                    <span className="panel-summary hide-visually" />
                </h2>
                <div className="panel-content">
                    <div className="radio-button-wrapper form-element">
                        <div className="form-element">
                            <input
                                type="radio"
                                id="regular"
                                name="attributes[car_listing_options]"
                                defaultValue="regular"
                                autoComplete="false"
                                className={cx('radio-switch')}
                                required
                                data-validation-group-channel="car-listing-options"
                                defaultChecked={selectedListingType === 'regular'}
                                onClick={() => {
                                    trackV2('RegularListingSelect');
                                    trackGA4Event<GA4.PostAdInteractionEvent>({
                                        event: 'ad_posted_form_interactions',
                                        formName: 'syi-form',
                                        formStep: 'change sell car[Gumtree][selected]',
                                        ...computeListingDetails(),
                                    });
                                    updateSelectedListingType('regular');
                                }}
                                {...qaAttribute('car-listing-regular-option')}
                            />
                            <label htmlFor="regular">Regular listing</label>
                            <p className="label-description">
                                Gumtree users can view your listing and contact you directly
                            </p>
                        </div>
                        <div className="form-element dealer-listing-form-element">
                            <input
                                type="radio"
                                id="dealer"
                                name="attributes[car_listing_options]"
                                defaultValue="dealer"
                                autoComplete="false"
                                className={cx('radio-switch')}
                                required
                                data-validation-group-channel="car-listing-options"
                                defaultChecked={selectedListingType === 'dealer'}
                                onClick={() => {
                                    trackV2('DealerAuctionSelect');
                                    trackGA4Event<GA4.PostAdInteractionEvent>({
                                        event: 'ad_posted_form_interactions',
                                        formName: 'syi-form',
                                        formStep: 'change sell car[CarWow][selected]',
                                        ...computeListingDetails(),
                                    });
                                    updateSelectedListingType('dealer');
                                }}
                                {...qaAttribute('car-listing-dealer-option')}
                            />
                            <label htmlFor="dealer">Sell to a Dealer (New!)</label>
                            <p className="label-description">
                                A network of verified dealers compete to offer you their best price.
                                No fees. No commitment required. Your journey will be completed
                                using our partner Carwow
                            </p>
                            {selectedListingType === 'dealer' && (
                                <ActionListingDetails>
                                    <PartnershipHeader>
                                        Gumtree in partnership with Carwow
                                    </PartnershipHeader>
                                    <ListItem>
                                        <IconInCircle>
                                            <CarAuctionInstantValuation />
                                        </IconInCircle>
                                        <div>Get an instant valuation</div>
                                    </ListItem>
                                    <LinkBetweenItems />
                                    <ListItem>
                                        <IconInCircle>
                                            <CarAuctionDealerBid />
                                        </IconInCircle>
                                        <div>Advertise to thousands of dealers</div>
                                    </ListItem>
                                    <LinkBetweenItems />
                                    <ListItem>
                                        <IconInCircle>
                                            <CarAuctionFinalise />
                                        </IconInCircle>
                                        <div>Free collection, fast payment</div>
                                    </ListItem>
                                </ActionListingDetails>
                            )}
                        </div>
                    </div>
                    {selectedListingType === 'dealer' && (
                        <>
                            <CreateDealerAuctionLinkContainer>
                                <div className="panel has-border" />
                                <ThemeAwareLink
                                    {...qaAttribute('create-dealer-auction-listing-link')}
                                    href={carWowUrl}
                                    label={
                                        <StyledLinkLabel>
                                            Sell your car <DealerAuctionIcon />
                                        </StyledLinkLabel>
                                    }
                                    type="primary"
                                    {...(!!appViewDevice && { target: '_blank' })}
                                    onLinkClicked={() => {
                                        trackV2('DealerAuctionCreate');
                                        trackGA4Event<GA4.PostAdInteractionEvent>({
                                            event: 'ad_posted_form_interactions',
                                            formName: 'syi-form',
                                            formStep: 'change carwow external',
                                            ...computeListingDetails(),
                                        });
                                    }}
                                />
                            </CreateDealerAuctionLinkContainer>
                            <ConfirmationLegalLinks buyerDomain={baseConfig.buyerUrl} />
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default CarListingOptions;
