import React from 'react';
import { useSelector } from 'react-redux';
import cx from 'classnames';
import InfoBox from '@gumtree/ui-library/src/info-box/info-box';
import { SyiState } from '../../reducers';
import { L1CategoryIds } from '@gumtree/shared/src/types/categories';

const OverallPriceAdvice = ({
    insertionFee,
    hasAddtlCatgeoryPanels,
    sellerType,
    l1CategoryId
}: {
    insertionFee?: number;
    hasAddtlCatgeoryPanels: boolean;
    sellerType?: string;
    l1CategoryId?: number
}) => {
    const { expandCategory } = useSelector(({ syi: { expandCategory } }: SyiState) => ({
        expandCategory,
    }));

    return (
        <InfoBox
            classNames={cx('panel price-info-box', {
                'is-closed': expandCategory,
                'has-border': l1CategoryId !== L1CategoryIds.SERVICES && (!hasAddtlCatgeoryPanels || (sellerType && hasAddtlCatgeoryPanels)),
            })}
        >
            There is a £{insertionFee} fee for posting in this category
        </InfoBox>
    );
};
export default OverallPriceAdvice;
