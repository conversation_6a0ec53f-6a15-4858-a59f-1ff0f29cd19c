import React from 'react';
import { useTheme } from '@emotion/react';
import { render } from '@testing-library/react';
import { mocked } from 'jest-mock';

import PetAdvicePanel from '../pet-advice-panel';

jest.mock('react-redux', () => ({
    useSelector: jest.fn(() => ({
        l1Category: 'pets',
        expandCategory: false,
    })),
}));

jest.mock('@emotion/react', () => ({
    ...jest.requireActual('@emotion/react'),
    useTheme: jest.fn(),
}));

mocked(useTheme).mockImplementation(() => ({
    palette: {
        primary: 'primary',
        secondary: 'secondary',
        action: {
            active: 'active',
        },
    },
}));

describe('PetAdvicePanel Component', () => {
    const renderPetAdvicePanel = () => {
        const defaultProps = { form: { categoryId: 2526 } };

        return render(<PetAdvicePanel {...defaultProps} />);
    };

    it('renders', () => {
        const { asFragment } = renderPetAdvicePanel();
        expect(asFragment()).toMatchSnapshot();
    });
});
