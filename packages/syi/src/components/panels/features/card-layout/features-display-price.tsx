import React from 'react';
import { FeatureOptions } from '../../../../services/dtl-syi';
import {
    getFeaturesPriceAmount,
    getFeaturesPriceDays,
} from '../../../../services/features-price-utils';
import { unescapeHTMLEntities } from '../../../../services/string-service';

const FeaturesDisplayPrice = ({
    featureOpts,
    isCardLayout,
}: {
    featureOpts: FeatureOptions;
    isCardLayout: boolean;
}) => {
    if (!featureOpts) return <></>;

    const decodedDisplayValue =
        featureOpts!.prices && unescapeHTMLEntities(featureOpts.prices![0].displayValue);

    return (
        <>
            {featureOpts.prices ? (
                isCardLayout ? (
                    <>
                        {getFeaturesPriceDays(decodedDisplayValue)}-
                        <span className="price-amount">
                            {getFeaturesPriceAmount(decodedDisplayValue)}
                        </span>
                    </>
                ) : (
                    <div>{decodedDisplayValue}</div>
                )
            ) : (
                <span>{featureOpts.expiryDescription}</span>
            )}
        </>
    );
};

export default FeaturesDisplayPrice;
