import React from 'react';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import cx from 'classnames';

import PropertyDetailsText from './property-details-text';
import { CategoryChangePanel } from '../../../styles';

const PropertyCategoryChangeAdvice = ({ propertyCategoryId }) => {
    const { expandCategory } = useSelector(({ syi: { expandCategory } }) => ({
        expandCategory,
    }));

    return (
        <CategoryChangePanel
            className={cx('panel space-pbn is-valid category-change-panel', {
                'is-closed': expandCategory,
            })}
            data-testid="property-category-change-advice"
        >
            <span className="panel-validation space-mls" />
            <div className="category-change-panel-text panel-content">
                <h4 className="category-change-panel-text-title">We've changed your ad category</h4>
                <PropertyDetailsText propertyCategoryId={propertyCategoryId} />
            </div>
        </CategoryChangePanel>
    );
};

PropertyCategoryChangeAdvice.propTypes = {
    propertyCategoryId: PropTypes.number.isRequired,
};

export default PropertyCategoryChangeAdvice;
