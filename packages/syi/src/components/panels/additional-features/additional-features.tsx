import React, { useState, useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import escape from 'lodash.escape';
import cx from 'classnames';
import Dialog from '@gumtree/ui-library/src/dialog/dialog';
import { onEnterPressed } from '@gumtree/ui-library/src/utils/key-events-service';

import FeatureMapper from './feature-mapper';
import { additionalFeaturesCss } from '../../../styles';
import { SyiState } from '../../../reducers';
import VehicleFeatures from './vehicle-features';
import { trackV2 } from '../../../services/common-utils';

const isSmallViewport = () => window.matchMedia('(max-width: 768px)').matches;

interface PanelAttribute {
    label: string;
    attributes: Record<string, any>; // Adjust based on actual shape
}
interface AdditionalFeaturesProps {
    form?: any;
    panelAttributes?: Record<string, PanelAttribute>;
    panelName?: string;
}

const AdditionalFeatures = ({
    panelName = '',
    panelAttributes,
    form
}: AdditionalFeaturesProps) => {
    const panelData = panelAttributes && panelAttributes[panelName];

    const label = panelData?.label ?? "";
    const origMetas = panelData?.attributes ?? {};
    const { attributes: initialFormAttributes } = form;

    const [features, setFeatures] = useState(
        Object.keys(origMetas)
            .filter((key) => key !== 'additional_features_available')
            .map((key) => ({
                key,
                label: origMetas[key].label,
                selected: !!initialFormAttributes[key],
            }))
    );

    const [totalFeatures] = useState(features.length);
    const selectedFeatures = useMemo(() => features.filter((x) => x.selected), [features]);
    const selectedCount = selectedFeatures.length;
    const selectedSome = selectedCount > 0;
    const mobilePreview = selectedFeatures
        .slice(0, 3)
        .map((x) => x.label)
        .join(', ');
    const sortedFeatures = useMemo(
        () => features.slice().sort((a, b) => (a.label < b.label ? -1 : 1)),
        [features]
    );

    const toggleAttrib = (key) =>
        setFeatures(features.map((f) => (f.key === key ? { ...f, selected: !f.selected } : f)));
    const clearFeatures = () => setFeatures(features.map((f) => ({ ...f, selected: false })));

    // Responsive
    const [isMobile, setIsMobile] = useState(isSmallViewport());
    useEffect(() => {
        const onResize = () => setIsMobile(isSmallViewport());
        window.addEventListener('resize', onResize);
        return () => window.removeEventListener('resize', onResize);
    }, []);

    const [modalOpen, setModalOpen] = useState(false);
    const openModal = () => {
        !modalOpen && trackV2('open-vehicle-features');
        setModalOpen(true);
    };

    const { expandCategory } = useSelector(({ syi: { expandCategory } }: SyiState) => ({
        expandCategory,
    }));

    return (
        <div
            className={cx('panel additional-features has-border', {
                'is-valid': selectedSome,
                'is-closed': expandCategory,
            })}
            data-validation-group="additional-features"
            data-toggler="channel:category-picker,selfBroadcast:false,className:is-closed,isOpen:false"
            css={additionalFeaturesCss}
        >
            <h2 className="panel-title">
                <span className="panel-validation" />
                {escape(label)}
            </h2>

            {isMobile ? (
                <div className="panel-content">
                    <button
                        type="button"
                        className="mobile-modal-trigger"
                        onClick={openModal}
                        onKeyDown={(event) => {
                            onEnterPressed(event, openModal);
                        }}
                    >
                        <div className="select-features">
                            Select features
                            <div className="mobile-preview-container">
                                <div className="mobile-preview">{mobilePreview}</div>
                                <i className="icon icon--chevron-r" />
                            </div>
                        </div>
                    </button>
                    <div className="mobile-subtitle">
                        <span className="mobile-count">{selectedCount}</span> features added
                    </div>
                </div>
            ) : (
                <div className="panel-content">
                    <div className="grid-col-12 desktop-feature-preview">
                        <FeatureMapper
                            features={features}
                            checkboxIdPrefix="preview-"
                            toggleAttrib={toggleAttrib}
                        />
                        <section className="desktop-preview-lower">
                            <div className="desktop-preview-summary">
                                {selectedCount} features added
                            </div>
                            <button
                                type="button"
                                className="btn-link txt-link"
                                data-q="txt-link-preview-show-features"
                                onClick={openModal}
                            >
                                Click here to see all {totalFeatures} features
                            </button>
                        </section>
                    </div>
                </div>
            )}

            <Dialog
                isOpen={modalOpen}
                title={isMobile ? 'Vehicle Features' : 'Vehicle standard features'}
                onClose={(e) => {
                    e.preventDefault();
                    setModalOpen(false);
                }}
                closeIconType={isMobile ? 'close' : undefined}
                displayAsSheetOnMweb
            >
                <div className="close-features">
                    <div>
                        <button
                            type="button"
                            className="btn-primary"
                            data-q="btn-modal-done"
                            onClick={(e) => {
                                e.preventDefault();
                                setModalOpen(false);
                            }}
                        >
                            Done
                        </button>
                    </div>
                </div>

                <div className="modal-feature-container">
                    <VehicleFeatures
                        sortedFeatures={sortedFeatures}
                        toggleAttrib={toggleAttrib}
                        isMobile={isMobile}
                        clearFeatures={clearFeatures}
                    />
                    <div className="modal-final-spacing">&nbsp;</div>
                </div>
            </Dialog>
        </div>
    );
};

export default AdditionalFeatures;

AdditionalFeatures.defaultProps = {
    panelName: null,
    panelAttributes: {},
    form: null,
};
