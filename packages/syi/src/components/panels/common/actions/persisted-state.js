import keyMirror from 'keymirror';

export const names = keyMirror({
    SET_CATEGORY_NAME: null,
    SET_CATEGORY_ID: null,
    SHOW_ALERT: null,
    CLEAR_DATA: null,
    HIDE_TOOLTIP: null,
    HIDE_SOLD: null,
});

export const setCategoryName = (category) => ({
    type: names.SET_CATEGORY_NAME,
    category,
});

export const setCategoryId = (categoryId) => ({
    type: names.SET_CATEGORY_ID,
    categoryId,
});

export const setShowAlert = (showAlert) => ({
    type: names.SHOW_ALERT,
    showAlert,
});

export const clearData = () => ({
    type: names.CLEAR_DATA,
});

export const hideTooltip = () => ({
    type: names.HIDE_TOOLTIP,
});

export const hideSold = () => ({
    type: names.HIDE_SOLD,
});
