/* eslint-disable max-len */
import React from 'react';

const getIsChecked = (data, attributeValue) => {
    return data.form.attributes[data.inputAttributes.id] === attributeValue;
};

const RadioButton = (data) => {
    const hasChecked = data.inputAttributes.values.some((attribute) => {
        return data.form.attributes[data.inputAttributes.id] === attribute.value;
    });

    return (
        <div className="radio-button-container">
            {data.inputAttributes.values.map((attribute, index) => {
                let isChecked = getIsChecked(data, attribute.value);

                if (!hasChecked && index + 1 === data.inputAttributes.values.length) {
                    isChecked = true;
                }

                return (
                    <div key={attribute.value} className="grid-col-6 grid-col-m-4 form-element">
                        <input
                            className="radio-switch"
                            type="radio"
                            id={`${data.inputAttributes.id}_${index}`}
                            name={`attributes[${data.inputAttributes.id}]`}
                            defaultValue={attribute.value}
                            data-broadcaster={
                                data.inputAttributes.priceSensitive
                                    ? 'channel:form.syi.submit.instant'
                                    : undefined
                            }
                            disabled={data.inputAttributes.disabled}
                            defaultChecked={isChecked}
                            autoComplete="false"
                        />
                        <label htmlFor={`${data.inputAttributes.id}_${index}`}>
                            {attribute.displayValue}
                        </label>
                    </div>
                );
            })}
        </div>
    );
};

export default RadioButton;
