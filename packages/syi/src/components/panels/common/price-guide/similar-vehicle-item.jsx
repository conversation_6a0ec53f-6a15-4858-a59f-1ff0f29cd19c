import React from 'react';
import PropTypes from 'prop-types';

const SimilarVehicleItem = ({ assetPath, id, mileage, primaryImageURL, price, sellerType }) => {
    return (
        <div
            id={`svItem_${id}`}
            data-testid={`similar-vehicle-item-${id}`}
            className="similar-item"
        >
            <div className="image">
                <img src={primaryImageURL || assetPath} alt="similar vehicle" />
            </div>
            <div className="spec">
                <div className="price">{price}</div>
                <div className="mileage">{mileage}</div>
                <div className="seller">{sellerType}</div>
            </div>
        </div>
    );
};

// BE required to be able to pass null rather than undefined.
SimilarVehicleItem.defaultProps = {
    primaryImageURL: '',
};

SimilarVehicleItem.propTypes = {
    assetPath: PropTypes.string.isRequired,
    id: PropTypes.number.isRequired,
    mileage: PropTypes.string.isRequired,
    price: PropTypes.string.isRequired,
    primaryImageURL: PropTypes.string,
    sellerType: PropTypes.string.isRequired,
};

export default SimilarVehicleItem;
