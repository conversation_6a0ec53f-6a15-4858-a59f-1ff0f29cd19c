 
import React, { useEffect, useState } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import cx from 'classnames';
import { qaAttribute } from '@gumtree/ui-library/src/utils/qa-service';
import { useTheme } from '@emotion/react';

import { trackV2 } from '@gumtree/shell/src/common/common-actions';
import InfoBox from '@gumtree/ui-library/src/info-box/info-box';
import type { SyiState } from '../../../reducers';
import { ContactDetailWrapperCss } from '../../../styles';
import { updateSyi } from '../../../reducers/syi';
import ContactDetailsPreview from './contact-details-preview';
import { useRefs } from './ref-provider';
import ContactDetailsEdit from './contact-details-edit';
import { Props } from './new-contact-details';
import NewLocationPanel from './new-location-panel';
import SafetyLightbox from './safety-lightbox';

const NewContactDetailsPanel = (props: Props) => {
    const { Helpers } = props;

    const helper = Helpers(props);
    const dispatch = useDispatch();

    const theme = useTheme();

    const { emailRef, phoneRef, emailInputRef, phoneInputRef, contactNameInputRef } = useRefs();

    const [useField, setUseField] = useState({
        useEmail: props.form.useEmail,
        usePhone: props.form.usePhone,
        useUrl: props.form.useUrl,
    });

    function handleUseField(type, value) {
        setUseField((useField) => ({
            ...useField,
            [type]: value,
        }));
    }

    const { expandCategory, enableEditContactDetails, userType } = useSelector(
        ({ syi: { expandCategory, enableEditContactDetails }, userType }: SyiState) => ({
            expandCategory,
            enableEditContactDetails,
            userType,
        }),
        shallowEqual
    );

    const isProUser = userType === 'PRO';

    const [emailValue, setEmailValue] = useState(
        userType !== 'PRO' && props.form.previousContactEmail
            ? props.form.previousContactEmail
            : props.form.contactEmail ?? ''
    );
    const [phoneValue, setPhoneValue] = useState(props.form.contactTelephone ?? '');
    const [contactNameValue, setContactNameValue] = useState(props.form.contactName);
    const [bottomMessage, setBottomMessage] = useState('');
    const [openLightbox, setOpenLightbox] = useState(false);

    const contactValid =
        helper.printError('contactEmail') ||
        helper.printError('contactTelephone') ||
        helper.printError('contactName');

    function updateBottomMessage() {
        if (emailRef.current!.checked && phoneRef.current!.checked) {
            setBottomMessage(allMessage);
        } else if (emailRef.current!.checked) {
            setBottomMessage(emailMessage);
        } else if (phoneRef.current!.checked) {
            setBottomMessage(phoneMessage);
        } else {
            setBottomMessage('');
        }
    }

    useEffect(() => updateBottomMessage(), []);

    const themedStyles = React.useMemo(() => ContactDetailWrapperCss(theme), [theme]);

    return (
        <>
            <div
                css={themedStyles}
                className={cx('panel has-border new-contact-details', {
                    'is-closed': expandCategory,
                    'is-invalid': contactValid,
                })}
                data-validation-group="contact"
                // data-toggler="channel:category-picker,selfBroadcast:false,className:is-closed,isOpen:false"
            >
                <h2 className="panel-title">
                    <div>
                        <span className="panel-validation" />
                        {/* */}Contact Details <sup>*</sup>
                        <span
                            className="panel-summary hide-fully-from-m"
                            data-radio-content="channel:form.syi.contactEmail"
                        />
                    </div>
                    <button
                        type="button"
                        className="btn-link"
                        {...qaAttribute('edit-contact-details-btn')}
                        onClick={() => {
                            setPhoneValue(phoneInputRef.current?.value ?? '');
                            setEmailValue(emailInputRef.current?.value ?? '');
                            setContactNameValue(contactNameInputRef.current?.value || '');
                            dispatch(
                                updateSyi({
                                    enableEditContactDetails: !enableEditContactDetails,
                                })
                            );
                            !enableEditContactDetails
                                ? trackV2('EditContactDetailsSelect')
                                : trackV2('SaveContactDetailsSelect');
                        }}
                    >
                        {enableEditContactDetails ? 'Save changes' : 'Edit'}
                    </button>
                </h2>

                <div className={cx('panel-content', { 'contact-details-pro': isProUser })}>
                    <ContactDetailsPreview
                        emailValue={emailValue}
                        phoneValue={phoneValue}
                        contactName={contactNameValue}
                        useField={useField}
                        handleUseField={handleUseField}
                        updateBottomMessage={updateBottomMessage}
                        {...props}
                    />

                    <ContactDetailsEdit
                        emailValue={emailValue}
                        phoneValue={phoneValue}
                        setEmailValue={setEmailValue}
                        setPhoneValue={setPhoneValue}
                        useField={useField}
                        {...props}
                    />

                    {bottomMessage && bottomMessage.length > 0 && (
                        <InfoBox dataQ="contact-details-bottom-message">
                            {bottomMessage}
                            <button
                                type="button"
                                className="txt-btn"
                                onClick={() => setOpenLightbox(true)}
                            >
                                Learn more
                            </button>
                        </InfoBox>
                    )}
                </div>
            </div>

            <NewLocationPanel {...props} />

            <SafetyLightbox isOpen={openLightbox} onClose={() => setOpenLightbox(false)} />
        </>
    );
};

const emailMessage = 'We will never share your email address with users.';
const phoneMessage =
    'Please note, if you choose to be contacted by phone, your phone number will be visible to other Gumtree users.';
const allMessage =
    'Please note, if you choose to be contacted by phone, your phone number will be visible to other Gumtree users. We will never share your email address with users.';

export default NewContactDetailsPanel;
