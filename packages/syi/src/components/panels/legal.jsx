/* eslint-disable max-len, react/no-danger */
import React from 'react';
import { useDispatch } from 'react-redux';
import cx from 'classnames';
import { useTheme } from '@emotion/react';
import { Dialog } from '@gumtree/ui-library';
import { agreeLegalTermsThunk } from '../../reducers/syi';
import { legalCss } from '../../styles';
import { useMediaQuery } from 'react-responsive';

const Legal = (data) => {
    const dispatch = useDispatch();
    const isMobile = useMediaQuery({ maxWidth: '767px' });

    const theme = useTheme();
    const themedStyles = legalCss(theme);

    return (
        <Dialog
            isOpen
            hideCloseBtn
            className={cx('mfp-hide lightbox box box-padding', { 'is-popup': isMobile })}
            css={themedStyles}
        >
            <span dangerouslySetInnerHTML={{ __html: data.legal.message }} />
            <div className="form-element">
                <input
                    id="legal-popup-check"
                    type="checkbox"
                    className="checkbox-switch"
                    // "termsAgreed" hidden input is in category-browser
                    name="termsAgreedCheckbox"
                    onClick={(e) => e.currentTarget.checked && dispatch(agreeLegalTermsThunk())}
                />
                <label
                    htmlFor="legal-popup-check"
                    dangerouslySetInnerHTML={{ __html: data.legal.label }}
                />
            </div>
        </Dialog>
    );
};

export default Legal;
