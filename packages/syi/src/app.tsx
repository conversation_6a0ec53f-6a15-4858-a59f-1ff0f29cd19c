import React from 'react';
import { useSelector, shallowEqual } from 'react-redux';

import type { SyiState } from './reducers/index';
import SyiFtl from './components/syi-ftl';
import CategoryPage from './components/category-page';

import './app.scss';
import { SyiPageContainer } from './styles';
import DtlSyiInit from './components/dtl-syi-init';
import SyiMetrics from '@gumtree/web-bff/src/metrics/modules/syi-metrics';

export default function App() {
    const { context, page } = useSelector(
        ({ syi: { context }, page }: SyiState) => ({
            context,
            page,
        }),
        shallowEqual
    );

    React.useEffect(() => {
        if (context.postAdPageType === 'category') {
            const pageContainer = document.querySelector('.view-container') as HTMLDivElement;
            pageContainer.style.background = 'white';
            try {
                const urlParams = new URLSearchParams(window.location.search);
                const appVersion = urlParams.get('appVersion') || '';
                localStorage.setItem('appVersion', appVersion);
            } catch (e) {
                console.error(e);
            }
        }

        SyiMetrics.registerPageViewEvent(context.postAdPageType, page.appViewDevice);
    }, []);

    return (
        <SyiPageContainer fullHeight={!!page.appViewDevice}>
            <DtlSyiInit />
            {context.postAdPageType === 'category' && <CategoryPage />}
            {context.postAdPageType === 'create' && <SyiFtl />}
            {context.postAdPageType === 'edit' && <SyiFtl />}
            {context.postAdPageType === 'repost' && <SyiFtl />}
        </SyiPageContainer>
    );
}

// TEMPFIX 🚧
if (typeof window !== 'undefined') {
    window.Gum = {
        React: {},
        domain: { assets: '', tracking: {} },
        RADIO: { on() {}, off() {}, trigger() {} },
        util: { openPopupWithReactComponent() {} },
    };
}
