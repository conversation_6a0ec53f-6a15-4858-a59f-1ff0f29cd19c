/*
	map an array into a new array with pairs

	Example:

	mapArrayIntoPairs([1, 2, 3, 4, 5, 6, 7])
	output: [[1, 2], [3, 4], [5, 6], [7]]
*/
export const mapArrayIntoPairs = (items) => {
    return items.reduce((pairedItems, item, index) => {
        if (index % 2 === 0) {
            const nextItem = items[index + 1];

            if (nextItem) {
                pairedItems.push([item, nextItem]);
            } else {
                pairedItems.push([item]);
            }
        }

        return pairedItems;
    }, []);
};
