/**
 * - form.js
 * - submitButton.js
 */

import { trackGA4Event } from '@gumtree/shared/src/util/ga4-shared';
import { SyiUseDispatch } from '../reducers';
import { updateSyi } from '../reducers/syi';
import { deepSerializeForm } from './deep-serialize-form';
import { computeListingDetails, postAdPageTypeToAdEventName } from './syi-model';
import { dtlSyi } from './dtl-syi';
import {
    FormElement,
    FormElementCheckbox,
    FormElementEmail,
    FormElementRadio,
    FormElementSelect,
    FormElementType,
    UnderlyingFormElement,
} from './form-elements';
import { trackV2 } from './common-utils';

type UnderlyingSubmitElement = HTMLButtonElement | HTMLAnchorElement | HTMLSpanElement;

export class SubmitButton {
    /** Flag to say if the form button has been setup with loader etc */
    buttonSetup = false;

    dataSubmitIgnore: boolean;
    dataSubmitExternal: boolean;

    constructor(public el: UnderlyingSubmitElement, public form: Form) {
        this.dataSubmitIgnore = this.el.hasAttribute('data-submit-ignore');
        this.dataSubmitExternal = this.el.hasAttribute('data-submit-external');
    }

    // _addSubscriptions() {
    //   this.form.RADIO.on( 'form.ready form.reset form.invalid', this.ready );
    //   this.form.RADIO.on( 'form.processing', this.waiting );
    //   //Allows manage button state from other components (not only form)
    //   Gum.RADIO.on( this.form.name + '.submitButton.ready', this.ready );
    //   Gum.RADIO.on( this.form.name + '.submitButton.waiting', this.waiting );
    // }

    _setupButton() {
        if (this.el) {
            if (!this.dataSubmitIgnore && !this.dataSubmitExternal && !this.buttonSetup) {
                // Flag a button setup
                this.buttonSetup = true;

                // Get the computed styles for more accurate width
                const computedStyle = window.getComputedStyle(this.el);

                //TODO: way spinner and form is implemented requires this check, refactor of this components
                //is needed
                if (!this.el.style.height && this.el.offsetHeight !== 0) {
                    this.el.style.height = `${this.el.offsetHeight}px`;
                }

                // If it's a whole number then replace else don't
                if (parseInt(computedStyle.width.replace('px', ''), 10) % 1 !== 0) {
                    this.el.style.width = `${this.el.offsetWidth}px`;
                }

                // Get the current contents of the button
                const currentContents = this.el.innerHTML;
                const loaderContents = `
          <div class="loader-bg-overlay"></div>
          <div class="loader-spinner"></div>
          <span class="button-contents">
            ${currentContents}
          </span>
        `;

                // Add the loader contents
                this.el.innerHTML = loaderContents;
            }
        }
    }

    ready() {
        if (this.el) {
            (this.el as HTMLInputElement).disabled = false;
            this.el.classList.remove('is-waiting');
        }
    }

    waiting() {
        if (this.el) {
            if (!this.buttonSetup) {
                this._setupButton();
            }

            if (!this.dataSubmitExternal) {
                (this.el as HTMLInputElement).disabled = true;
                this.el.classList.add('is-waiting');
            }
        }
    }
}

export class Form {
    DEFAULTS = {
        /** Used to delay submission until callback from analytics to say it has tracked the events */
        defer: false,
        /** Do we want to validate this form client side */
        hasValidation: true,
        /** Override the rest to a custom clear */
        clear: false,
        /** Do we want to submit this form with aJax */
        ajaxSubmit: false,
        /** Do we want to submit this form with RADIO */
        radioSubmit: false,
        /** How long to wait for an ajax request before it fails */
        ajaxTimeout: 5000,
        /** The event name that form actions will be appended to and pushed to the data layer */
        analyticsTag: undefined,
        /** Inserts a tracking pixel to notify a form has been submitted */
        trackingPixelEvent: undefined,
        /** Should it allow submit on enter when within form fields? */
        disableEnter: false,
        /** Is the user logged in, used for tracking */
        userLoggedIn: undefined as undefined | boolean,
        /** Do we want to submit if validation is passed */
        disableSubmitOnValid: false,

        jsvalidate: false,
        radioSubmitChannel: undefined as undefined | string,
        dontUseFormJS: false,
    };

    cbTimer?: number;
    el: HTMLFormElement;
    elements: FormElementType[] = [];
    loaderButtons = [] as SubmitButton[];
    name!: string;
    options: Form['DEFAULTS'];
    preventSubmit?: boolean;
    submitButton?: SubmitButton;
    submitInstant?: boolean;
    unique: string;

    /** tmp array of element names */
    _elementNames = [] as string[];

    /**
     * A list of fields that will not have element class instantiated against it
     * @type array
     */
    ignoreFields = ['submit', 'reset', 'hidden', 'fieldset', 'button'] as const;

    //#region event handling
    onChange = {} as Record<string, ((value?: string | boolean) => void)[]>;
    onCount = {} as Record<string, ((value: number) => void)[]>;
    onIsValid = [] as (() => void)[];
    onSelectAll = {} as Record<string, ((value: boolean) => void)[]>;
    onSelectAllChild = {} as Record<string, ((cb?: () => void) => void)[]>;
    onServerErrors = [] as ((resp: any) => void)[];
    onSync = {} as Record<string, ((value: boolean) => void)[]>;
    onValue = {} as Record<string, ((value?: string | boolean) => void)[]>;
    //#endregion

    validationGroup = {} as { [key: string]: FormElementType[] };

    get formJson() {
        const serializable = deepSerializeForm(this.el);
        if (this.submitInstant) {
            serializable.submitForm = false;
            this.submitInstant = false;
        }
        return serializable as Record<string, string | number | boolean | Record<string, any>> & {
            submitForm?: boolean;
        };
    }

    constructor(options: Partial<Form['DEFAULTS']>, private dispatch: SyiUseDispatch) {
        this.el = document.getElementById('syi-form') as HTMLFormElement;
        this.options = Object.assign({}, this.DEFAULTS, options);
        this.unique = 'syi';

        if (this.el.querySelector('[type=submit]')) {
            // 🚧 This never seems to run for SYI
            this.submitButton = new SubmitButton(
                this.el.querySelector('[type=submit]') as UnderlyingSubmitElement,
                this
            );
        }
    }

    addElementGroup(details: { name: string; element: FormElement }) {
        if (!this.validationGroup[details.name]) {
            this.validationGroup[details.name] = [];
        }
        if (!details.element.isDisabled()) {
            // 🚧 can we avoid adding this cleanup?
            this.validationGroup[details.name] = this.validationGroup[details.name].filter(
                (x) => x.el !== details.element.el
            );
            this.validationGroup[details.name].push(details.element);
        }
    }

    bindReset(e: Event) {
        this.clear();
        e.preventDefault();
    }

    clear(event?: Event) {
        if (event && event.preventDefault) {
            event.preventDefault();
        }

        // this.RADIO.trigger( 'clearFields', false );
        this.elements.forEach((x) => x.clear());
    }

    init() {
        if (this.options.dontUseFormJS) {
            return;
        }

        // this.name = this.el.name;
        // This is not a good way, cause it would target a form element with name=name
        this.name = this.el.getAttribute('name')!;

        // Check the form has a name
        if (!this.name) {
            throw new Error('All forms need a unique name');
        }

        // Load Input Elements
        this.loadElements();

        // Check to see if we can disable enter pressed within inputs
        // this.options.disableEnter = this.el.data( "disableEnter" ) ? true : false;

        /**
         * Set the method that is ran when form is submitted
         */
        this.el.onsubmit = this.validate.bind(this);

        /**
         * Lets check to see if we are overriding the default reset
         */
        this.el.onreset = this.options.clear !== false ? this.bindReset : this.el.onreset;

        this.onIsValid.push(this.submit.bind(this));
    }

    isValid() {
        // We skip the validation if the jsValidation is turned off
        if (!this.options.hasValidation) {
            return true;
        }

        const inValidInputs = this.elements.filter(
            (element) => !element.isValid().valid && !element.el.disabled
        );

        if (inValidInputs.length === 0) {
            // Whoop this is valid
            return true;
        }
        // Not valid
        return false;
    }

    getElementByName(name: string) {
        return this._elementNames.includes(name)
            ? this.elements.find((element) => element.el.name === name)
            : undefined;
    }

    loadElements() {
        this._elementNames = [];
        this.elements = [];
        this.loaderButtons = [];

        const underlyingFormElements = Array.from(this.el.elements).filter(
            (el) => !!el.closest('.form-element')
        );

        underlyingFormElements.forEach((el) => this._addFormElement(el as UnderlyingFormElement));
        this.elements.forEach((instance) => instance.init());

        Array.from(this.el.querySelectorAll('.has-loader')).forEach((loaderEl) =>
            this.loaderButtons.push(new SubmitButton(loaderEl as UnderlyingSubmitElement, this))
        );

        // ❗️ Requested from BA team to not send validation events on page load
        // this.validateAllGroups();
    }

    logEvent(stats) {
        clearTimeout(this.cbTimer);

        // Check to see if we have an event
        if (stats.event) {
            // Check to see if there is a callback
            if (stats.callback) {
                // Gum.RADIO.once( 'analytics.callback', stats.callback );
                this.cbTimer = window.setTimeout(function () {
                    stats.callback();
                    // Gum.RADIO.trigger( 'analytics.callback' );
                }, 500);
            }
            // Push the event to the data layer
            trackV2(stats.event);
        } else if (stats.pixel) {
            this.insertPixel(stats);
        } else {
            stats.callback?.();
        }
    }

    insertPixel(stats) {
        const trackingPixelSrc = this.el.getAttribute('data-trackingpixel');
        let trackingPixel;

        if (this.options.trackingPixelEvent === true && trackingPixelSrc) {
            trackingPixel = document.createElement('img');
            trackingPixel.src = trackingPixelSrc;
            trackingPixel.height = '0';
            trackingPixel.width = '0';
            trackingPixel.style.border = '0';

            document.body.appendChild(trackingPixel);
        }
        stats.callback?.();
    }

    scrollToFirstField() {
        const target = document.querySelector('.scroll-top-ref') as HTMLElement;
        target?.scrollIntoView({ behavior: 'smooth' });
    }

    removeElementGroup(details: { name: string; element: FormElement }) {
        this.validationGroup[details.name] = this.validationGroup[details.name].filter(
            (element) => element.el !== details.element.el
        );

        this.validateElementGroup(details.name);
    }

    showSavingIndicator() {
        this.dispatch(updateSyi({ isPostingAd: true }));
    }

    hideSavingIndicator() {
        this.dispatch(updateSyi({ isPostingAd: false }));
    }

    async submit() {
        if (this.preventSubmit) {
            this.preventSubmit = false;
            return;
        }

        // tell everything that is listening to the form that we are processing
        // this.RADIO.trigger( 'form.processing' );
        this.submitButton?.waiting();
        this.loaderButtons.forEach((button) => button.waiting());

        // Log an attempt with the stats
        this.logEvent({
            event: this.options.analyticsTag ? `${this.options.analyticsTag}Attempt` : undefined,
            pixel: this.options.trackingPixelEvent,
        });

        try {
            this.showSavingIndicator();
            return await dtlSyi.submitSYIForm(this.formJson);
        } catch (e) {
            throw e;
        } finally {
            this.hideSavingIndicator();
        }
    }

    updateElements(data: { [x: string]: any }) {
        this.elements.forEach((el) => el.setValue(data));

        const hiddenVars = Array.from(
            this.el.querySelectorAll('input[type=hidden]')
        ) as HTMLInputElement[];

        hiddenVars.forEach((element) => {
            element.value = data[element.name] ? data[element.name] : element.value;
        });
    }

    /**
     * Main function that deals with the validation of the form, if its valid that it will
     * continue with the submit flow.
     */
    validate() {
        dtlSyi.triggerTracking(dtlSyi.getBaseTrackingData(this.formJson), [
            'FORM_FREE',
            'FORM_PAID',
        ]);

        if (this.isValid()) {
            this.markAsValid();
        } else {
            this.markAsInvalid();
        }

        // Stop the form submitting normally - this is needed
        return false;
    }

    markAsValid() {
        // Run the submit - this method is selected
        // Gum.RADIO.trigger( `form.${  this.name  }.isValid` ); // 🚧
        this.onIsValid.forEach((cb) => cb());

        this.el.classList.add('is-valid-form');
        this.el.classList.remove('is-invalid-form');
    }

    markAsInvalid() {
        // Only used by create-account-lightbox (won't migrate)
        // Gum.RADIO.trigger( `form.${  this.name  }.isInvalid` );
        this.submitButton?.ready();
        this.loaderButtons.forEach((x) => x.ready());

        this.scrollToFirstField();
        this.el.classList.add('is-invalid-form');
        this.el.classList.remove('is-valid-form');
        if (this.options.analyticsTag) {
            this.logEvent({
                event: `${this.options.analyticsTag}Fail`,
            });
        }

        try {
            const errorKVPairs = Object.values(this.validationGroup).reduce((agg, formEls) => {
                formEls.forEach(({ state: { name: key, errors } }) =>
                    errors.forEach((value) => agg.push({ key, value }))
                );
                return agg;
            }, [] as { key: string; value: string }[]);

            const eventName = postAdPageTypeToAdEventName();
            eventName &&
                trackGA4Event<GA4.PostAdEvent>({
                    event: eventName,
                    formName: 'syi-form',
                    formStep: 'submit',
                    formValidation: 'failure',
                    formErrors: errorKVPairs.map(({ key, value }) =>
                        JSON.stringify(`${key}: ${value}`).replace(/,/g, '')
                    ),
                    ...computeListingDetails(),
                });
        } catch (e) {
            console.error('form.validate tracking', e);
        }
    }

    validateAllGroups() {
        for (const key of Object.keys(this.validationGroup)) {
            this.validateElementGroup(key, true);
        }
    }

    validateElementGroup(details: string, elementValidation?: boolean) {
        let zeroResults = true;
        let elementState: FormElement['state'];

        const groupState = {
            touched: false,
            pristine: true,
            valid: undefined as undefined | boolean,
            submitted: false,
        };

        const pristineTest = [] as FormElement['state'][];

        if (this.validationGroup[details]) {
            for (let v = 0; v < this.validationGroup[details].length; v++) {
                zeroResults = false;

                elementState = this.validationGroup[details][v].isValid(true, elementValidation);

                groupState.pristine = !elementState.pristine ? false : groupState.pristine;
                groupState.touched = elementState.touched ? true : groupState.touched;
                groupState.submitted = elementState.submitted ? true : groupState.submitted;
                groupState.valid =
                    elementState.valid === false || groupState.valid === false
                        ? false
                        : elementState.valid;

                if (elementState.pristine) {
                    pristineTest.push(elementState);
                }
            }

            const groupEl = this.el.querySelectorAll(`[data-validation-group="${details}"]`);

            groupEl.forEach((el) => {
                el.classList.remove('is-valid');
                el.classList.remove('is-invalid');
                el.classList.remove('is-beingedited');
            });

            if (!zeroResults) {
                if (
                    groupState.valid === false &&
                    (groupState.submitted || pristineTest.length === 0)
                ) {
                    groupEl.forEach((el) => el.classList.add('is-invalid'));
                } else if (groupState.valid === false && !groupState.pristine) {
                    groupEl.forEach((el) => el.classList.add('is-beingedited'));
                } else if (groupState.valid) {
                    groupEl.forEach((el) => el.classList.add('is-valid'));
                }
            }
        }
    }

    /**
     * @function
     * Initialise all relevant inputs and controls and add them to a main collection
     * @param {element} element of the form inputs
     */
    _addFormElement(element: UnderlyingFormElement) {
        let elementType = element.dataset.type || element.getAttribute('type');

        if (!elementType) {
            elementType = element.type;
        }
        if (elementType === 'select-one' || elementType === 'select-multi') {
            // The above are the possible values of `HTMLSelectElement.type`
            elementType = 'select';
        }
        if (this.ignoreFields.includes(elementType as any) || !element.name) {
            return;
        }

        switch (elementType) {
            case 'checkbox': {
                this.elements.push(
                    new FormElementCheckbox(
                        element as HTMLInputElement & { type: 'checkbox' },
                        this,
                        undefined,
                        this.dispatch
                    )
                );
                break;
            }
            case 'radio': {
                if (!this._elementNames.includes(element.name)) {
                    this.elements.push(
                        new FormElementRadio(
                            element as HTMLInputElement & { type: 'radio' },
                            this,
                            undefined,
                            this.dispatch
                        )
                    );
                }
                break;
            }
            case 'email': {
                this.elements.push(
                    new FormElementEmail(
                        element as HTMLInputElement & { type: 'email' },
                        this,
                        undefined,
                        this.dispatch
                    )
                );
                break;
            }
            case 'select': {
                this.elements.push(
                    new FormElementSelect(
                        element as HTMLSelectElement,
                        this,
                        undefined,
                        this.dispatch
                    )
                );
                break;
            }
            default:
                this.elements.push(new FormElement(element, this, undefined, this.dispatch));
                break;
        }

        this._elementNames.push(element.name);
    }
}

export const COOKIE_EXPIRY_TIME = 1 / 48; // 15 minutes from now
