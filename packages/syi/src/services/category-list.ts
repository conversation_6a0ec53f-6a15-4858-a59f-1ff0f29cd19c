import { MetricsOptions } from '@gumtree/web-bff/src/data/common/fetch';
import type { CategoryItem, Props as ComponentProps } from '../components/panels/category-listings';
import type { SyiUseDispatch } from '../reducers';
import { getPageStateThunk } from '../reducers/syi';
import { dtlSyi, fetchOrThrow } from './dtl-syi';

/**
 * Originally category-list.js
 */
export class CategoryList {
    catChildrenData: null | CategoryChildrenData;
    catDropdownData: null | CategoryChildrenData;
    categoryLevel: null | ComponentProps['categoryLevel'];

    initial?: boolean;
    /** Has the parent category been specified? */
    specified!: boolean;

    selectedCategoryId = null as null | number;

    private dispatch!: SyiUseDispatch;
    public options!: CategoryListOption;

    get hiddenInput() {
        return this.type === 'category' ? dtlSyi.hiddenCategoryInput : dtlSyi.hiddenLocationInput;
    }

    constructor(
        private getParentList: () => CategoryList[],
        public listIndex: number,
        private type: 'category' | 'location'
    ) {
        this.catChildrenData = null;
        this.catDropdownData = null;
        this.categoryLevel = null;
    }

    init(options: CategoryListOption, dispatch: SyiUseDispatch) {
        this.options = options;
        this.dispatch = dispatch;

        // this.continueButton = document.querySelector('button#continueButton')!;
        this.specified = !!this.options.initial;

        // Originally init()
        // this.model = this.options.model || "CategoryModel";
        this.initial = this.options.initial ? true : undefined;
        // this._super();   // 🚧
    }

    /**
     * This is where the magic comes from when clicking a category
     */
    action(category: CategoryItem) {
        if (this.selectedCategoryId === category.id) {
            // We just toggled
            this.selectedCategoryId = null;
            this.getNextCat()?.clear();
            this.hiddenInput.value = '';
            this.checkContinueButton();
            dtlSyi.renderComponent();
            return;
        }

        this.selectedCategoryId = category.id;

        if (category.children) {
            this.getNextCat()?.getChildren(category.id);
            dtlSyi.disableContinueButton(this.type);

            // if (this.options.anyloc) {// 🚧 Any location means UK?
            //     this.updateForm(category.id);
            //     this.checkContinueButton();
            //     return;
            // }
        } else {
            this.getNextCat()?.clear();
            this.updateForm(category.id);
            dtlSyi.enableContinueButton(this.type);
        }

        dtlSyi.renderComponent();
    }

    checkContinueButton() {
        // 🚧 check if data source has children instead
        const catListEls = Array.from(
            document.querySelectorAll('.category-lists-container .category-list')
        );
        const leafCatSelected = catListEls.some((el) =>
            el.querySelector('.is-selected:not(.is-parent)')
        );

        if (leafCatSelected) {
            dtlSyi.enableContinueButton(this.type);
        } else {
            dtlSyi.disableContinueButton(this.type);
        }
    }

    clear() {
        if (!this.specified && this.options.forward) {
            this.getNextCat()?.clear();
        }
        this.specified = false;
        this.selectedCategoryId = null;

        // 🚧 careful about this
        this.catChildrenData = null;
        this.catDropdownData = null;
        this.categoryLevel = null;
        dtlSyi.renderComponent();
    }

    // displayAll removed

    async displayData(data: CategoryChildrenData) {
        this.categoryLevel = {
            listings: data,
            selected: this.options.selected, // 🚧
            initial: this.options.initial,
            allLocAllowed: this.options.anyloc ? true : false,
            listIndex: this.listIndex,
        };

        this.options.selected = undefined;

        // this._injectTemplate(info);
        await this.getNextCat()?.getChildren();
        dtlSyi.renderComponent();
    }

    // getCategoryTree removed

    async getChildren(categoryId = this.options.initial) {
        if (categoryId === this.options.initial) {
            // Prevent future initialisation
            this.options.initial = undefined;
        }

        dtlSyi.disableContinueButton(this.type);
        this.clear();

        if (this.catChildrenData === null && categoryId) {
            const { baseConfig } = this.dispatch(getPageStateThunk());
            const url =
                this.type === 'category'
                    ? `${baseConfig.sellerUrl}/ajax/category/children?${new URLSearchParams({
                          id: `${categoryId}`,
                          postAd: 'true',
                      })}`
                    : `${baseConfig.buyerUrl}/ajax/location/children?${new URLSearchParams({
                          id: `${categoryId}`,
                      })}`;

            const metricsLoggingRequest = {
                serviceName: this.type === 'category' ? 'seller' : 'buyer',
                path:
                    this.type === 'category'
                        ? '/ajax/category/children'
                        : '/ajax/location/children',
                method: 'GET',
            } as MetricsOptions;

            const resp = await fetchOrThrow(url, undefined, metricsLoggingRequest);

            this.catChildrenData = await resp.json();
            await this.displayData(this.catChildrenData!);
        }
    }

    getNextCat(): CategoryList | undefined {
        const nextCatIndex = this.getParentList().findIndex((x) => x === this) + 1;
        return this.getParentList()[nextCatIndex];
    }

    /**
     * Update the form with the selected items
     */
    updateForm(categoryId: number) {
        this.hiddenInput.value = `${categoryId}`;
        // clear any fields that need to empty on submit
        if (this.options.clearField === 'postcode') {
            const locationVisibleOnMap = document.getElementById('location_visible_on_map');
            const postAdPostcode = document.getElementById('post-ad_postcode');
            if (locationVisibleOnMap instanceof HTMLInputElement) {
                locationVisibleOnMap.checked = false;
            }
            if (postAdPostcode instanceof HTMLInputElement) {
                postAdPostcode.value = '';
            }
        }
    }
}

export interface CategoryListOption {
    anyloc?: 'true';
    channel: 'cat-lvl-2' | 'cat-lvl-3' | 'cat-lvl-4' | 'cat-lvl-5';
    clearField?: 'postcode';
    trigger?: 'category-l1';
    forward?: 'cat-lvl-3' | 'cat-lvl-4' | 'cat-lvl-5';
    field: 'categoryId';
    button: 'continueButton';
    initial?: number | boolean;
    selected?: number;
    model?: string;
}

export interface CategoryChildrenData {
    children: boolean;
    childrenItems: CategoryChildrenData[];
    drilled: boolean;
    id: number;
    /** e.g. `For Sale` */
    name: string;
    selected: boolean;
    /** e.g. `for-sale` */
    seoName: string;
}
