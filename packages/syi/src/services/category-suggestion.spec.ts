import CategorySuggestion from './category-suggestion';
import { mocked } from 'jest-mock';
import { updateSyi } from '../reducers/syi';
import { fetchOrThrow } from './dtl-syi';

jest.mock('../reducers/syi');
jest.mock('./dtl-syi');

describe('category-suggestion', () => {
    const mockDispatch = jest.fn();

    const categorySuggestion = new CategorySuggestion(mockDispatch);

    const mockedUpdateSyi = mocked(updateSyi);
    const mockedFetchOrThrow = mocked(fetchOrThrow);

    describe('getSuggestions', () => {
        describe('The catList should be nullified when', () => {
            beforeEach(() => {
                mockedUpdateSyi.mockReturnValue({
                    type: 'UPDATE_SYI',
                    payload: { l1Category: 'yes' },
                });
                mockedFetchOrThrow.mockResolvedValue({
                    json: jest.fn().mockResolvedValue({ catList: null }),
                } as any);
            });

            it('the searchtext is less than 3 characters', async () => {
                await categorySuggestion.getSuggestions('ab');
                expect(mockedUpdateSyi).toBeCalledWith({ catList: null });
            });

            it('the searchtext contains a character other than a letter, number, space or hyphen', async () => {
                await categorySuggestion.getSuggestions('abc123<');
                expect(mockedUpdateSyi).toBeCalledWith({ catList: null });
            });
        });

        describe('The category suggest endpoint should be called when', () => {
            beforeEach(() => {
                mockDispatch.mockReturnValue({
                    baseConfig: { sellerUrl: 'https://unit-test' },
                });

                mockedFetchOrThrow.mockResolvedValue({
                    json: jest.fn().mockResolvedValue({
                        categories: [
                            { id: 1, name: 'Electronics' },
                            { id: 2, name: 'Home Appliances' },
                        ],
                    }),
                } as any);
                window.gumtreeDataLayer = [];
            });

            afterEach(() => {
                delete window.gumtreeDataLayer;
            });

            ['abc', 'iphone7', 'roller-skates'].forEach((testParam) => {
                it(`valid search text has been entered - e.g. ${testParam}`, async () => {
                    await categorySuggestion.getSuggestions(testParam);
                    expect(mockedFetchOrThrow).toHaveBeenCalledWith(
                        `https://unit-test/api/category/suggest?input=${testParam}`,
                        undefined,
                        { method: 'GET', path: '/api/category/suggest', serviceName: 'seller' }
                    );
                });
            });

            it('valid search text has been entered - e.g. xbox 360', async () => {
                await categorySuggestion.getSuggestions('xbox 360');
                expect(mockedFetchOrThrow).toHaveBeenCalledWith(
                    'https://unit-test/api/category/suggest?input=xbox+360',
                    undefined,
                    { method: 'GET', path: '/api/category/suggest', serviceName: 'seller' }
                );
            });
        });
    });
});
