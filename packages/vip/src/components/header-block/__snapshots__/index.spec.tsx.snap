// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Order details page components does not render price when empty 1`] = `
<DocumentFragment>
  <div
    class="css-1oubcos-header-block e1pt9h6u7"
  >
    <h1
      class="css-1utqs9u-header-block e1pt9h6u2"
      data-q="vip-title"
    >
      test ad
    </h1>
    <div
      class="css-cyvij8-header-block e1pt9h6u6"
    >
      <span
        class="css-cffsh2-header-block e1pt9h6u4"
      >
        <h4
          class="css-1l3ecjo-header-block e1pt9h6u1"
          data-q="ad-location"
        >
          Putney, London
        </h4>
      </span>
      <span />
    </div>
  </div>
</DocumentFragment>
`;

exports[`Order details page components renders snapshot 1`] = `
<DocumentFragment>
  <div
    class="css-1oubcos-header-block e1pt9h6u7"
  >
    <h1
      class="css-1utqs9u-header-block e1pt9h6u2"
      data-q="vip-title"
    >
      test ad
    </h1>
    <div
      class="css-cyvij8-header-block e1pt9h6u6"
    >
      <span
        class="css-cffsh2-header-block e1pt9h6u4"
      >
        <h4
          class="css-1l3ecjo-header-block e1pt9h6u1"
          data-q="ad-location"
        >
          Putney, London
        </h4>
      </span>
      <span>
        <h3
          class="css-1e2uwmb-header-block e1pt9h6u0"
          data-q="ad-price"
        >
          £10
        </h3>
      </span>
    </div>
  </div>
</DocumentFragment>
`;
