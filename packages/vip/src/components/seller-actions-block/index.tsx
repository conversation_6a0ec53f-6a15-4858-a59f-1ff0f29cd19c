import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import classnames from 'classnames';

import { Button, Icon } from '@gumtree/ui-library';
import { colors } from '@gumtree/ui-library/src/base/theme';
import { track } from '@gumtree/shared/src/actions';
import {
    PermutiveEventNames,
    trackPermutiveCustomEvent,
} from '@gumtree/shared/src/util/permutive/track-event';
import { toggleSavedAdThunk } from '@gumtree/shared/src/reducers/saved-ads';
import { qaAttribute } from '@gumtree/ui-library/src/utils/qa-service';
import type { VipState, VipUseDispatch } from '../../reducers';
import ReportSellerBlock from './report-seller-block';

import { Container } from './index.style';
import { ButtonGroup } from '../../app.style';

const SellerActionsBlock: React.FC = () => {
    const { savedAds, advertId, token, sendReportSuccess } = useSelector(selectState);

    const favourited = savedAds[advertId];
    const [isReportOpen, setReportOpen] = React.useState(false);
    const toggleClass = classnames({ 'rotate-toggle': isReportOpen });
    const isSaved = classnames({ 'is-saved': favourited });

    const dispatch = useDispatch() as VipUseDispatch;
    const toggleFavourite = useCallback(() => {
        if (!favourited) {
            track('WatchlistAddAttempt');
            trackPermutiveCustomEvent(window, PermutiveEventNames.WATCHLIST_ADD_ATTEMPT, {});
        }

        // VIP already has listingDetails
        const { listingDetails } = (window.gumtreeDataLayer[0] || {}) as GA4.PageDeclarationEvent;

        const trackEventPayload = {
            listName: 'VIP',
            clickListingDetails: (listingDetails ?? {}) as GA4.GA4ListingDetails,
        };

        dispatch(
            toggleSavedAdThunk({ advertId, token, isAlreadySaved: favourited, trackEventPayload })
        );
    }, [favourited]);

    const toggleReportOpen = useCallback(() => {
        if (!sendReportSuccess && !isReportOpen) {
            track('ReportAdBegin');
        }
        setReportOpen(!isReportOpen);
    }, [isReportOpen, sendReportSuccess]);

    const closeReport = useCallback(() => setReportOpen(false), []);

    return (
        <Container>
            <ButtonGroup>
                <Button
                    display="secondary"
                    className={`favourite ${isSaved}`}
                    label="Favourite"
                    onClick={toggleFavourite}
                    icon={<Icon color={favourited ? 'pink' : colors.navy} type="full-heart" />}
                    {...qaAttribute(isSaved ? 'full-heart' : 'empty-heart')}
                />
                <Button
                    display="secondary"
                    label="Report"
                    className="reporter"
                    onClick={toggleReportOpen}
                    secondIcon={
                        <Icon
                            type="dropdown"
                            size="smallest"
                            color={colors.navy}
                            className={toggleClass}
                            data-testid={toggleClass}
                        />
                    }
                    icon={<Icon type="warning" color={colors.navy} />}
                />
            </ButtonGroup>
            <ReportSellerBlock
                csrfToken={token}
                id={advertId}
                isOpen={isReportOpen}
                stateClick={closeReport}
            />
        </Container>
    );
};

export function selectState({
    baseConfig: { buyerUrl },
    adDetails: { id: advertId, token, leafCategoryId, l2CategoryId },
    sellerActions: { savedAds, sendReportSuccess },
}: VipState) {
    return {
        buyerUrl,
        savedAds,
        sendReportSuccess,
        advertId,
        token,
        leafCategoryId,
        l2CategoryId,
    };
}

export default SellerActionsBlock;
