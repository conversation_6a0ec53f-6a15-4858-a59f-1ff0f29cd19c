import styled from "@emotion/styled";
import { css } from "@emotion/react";
import {
    gutterSizes,
    colors,
    colorVariables,
    fontSizes,
    breakpoints,
    mediaQuery,
} from "@gumtree/ui-library/src/base/theme";
import { newLayout } from "../../app.style";

export const Title = styled.h2<{ isMobileCardsLayout?: boolean }>`
    padding-top: ${gutterSizes.base};
    margin-bottom: 0;
    color: ${colorVariables.textPrimary};

    ${mediaQuery.between(breakpoints.medium, breakpoints.large)} {
        padding-top: 0;
    }

    ${({ isMobileCardsLayout }) =>
        isMobileCardsLayout &&
        css`
            padding: 0 0 ${gutterSizes.base} 0;
        `}
`;

export const DescriptionBlock = styled.div<{ isMobileCardsLayout?: boolean }>`
    ${({ isMobileCardsLayout }) =>
        isMobileCardsLayout
            ? newLayout
            : css`
                  width: 100%;
              `}

    h2 {
        font-weight: 600;
        font-size: ${fontSizes.xlarge};
        line-height: 32px;

        ${mediaQuery.from(breakpoints.large)} {
            font-size: ${fontSizes.pgLarge};
        }
    }
`;

export const BottomContainer = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: ${gutterSizes.base};
`;

export const ShowMoreLink = styled.div<{ showMore: boolean }>`
    color: ${colors.blue};
    margin-left: auto; // Ensures the second child is pushed to the right
    user-select: none;
    display: flex;
    align-items: center;

    .icon {
        display: flex;
        width: 24px;
        justify-content: center;
    }

    ${({ showMore }) =>
        showMore &&
        css`
            .icon {
                transform: rotate(180deg);
                padding-right: ${gutterSizes.small};
            }
        `}
`;

export const DescriptionText = styled.p<{ linesToClamp?: number }>`
    max-width: 75ch;
    margin: ${gutterSizes.large} 0 0 0;

    ${mediaQuery.until(breakpoints.large)} {
        margin: ${gutterSizes.base} 0 0 0;
    }

    ${({ linesToClamp }) =>
        linesToClamp &&
        css`
            display: -webkit-box;
            -webkit-line-clamp: ${linesToClamp};
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            overflow: hidden;
        `}
`;
