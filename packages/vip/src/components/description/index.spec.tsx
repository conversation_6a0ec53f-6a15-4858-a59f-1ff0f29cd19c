import '@testing-library/jest-dom';
import React from 'react';
import { useSelector } from 'react-redux';
import { screen, fireEvent, render } from '@testing-library/react';
import { renderWithThemeRender } from '@gumtree/shared/src/test-utils/mock-theme';
import Description from '.';

jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
    useDispatch: jest.fn(),
    shallowEqual: jest.fn(),
}));

jest.mock('@gumtree/shared/src/util/track-ga-event', () => ({
    trackV2: jest.fn(),
}));

describe('Description Component', () => {
    const mockProps = {
        isMobileCardsLayout: false,
        isForSale: true,
        isMotors: true,
        isPetsOrPhones: true,
        useToggle: true,
        linesToShow: 3,
        showDescriptionTitle: true,
        showIcon: true,
    };

    const mockState = {
        adDetails: {
            attributes: [{ key: 'Posted', value: '2021-09-01' }],
            description: ['Line 1', 'Line 2'],
            id: '12345',
            vatNumber: 'VAT123',
            l1Category: 'pets',
            leafCategoryId: 'l3cat',
            isFromMotorsFeed: true,
        },
        baseConfig: {
            bffUrl: 'http://example.com',
        },
    };

    beforeEach(() => {
        useSelector.mockImplementation((selector) => selector(mockState));
        jest.clearAllMocks();
    });

    it('renders the description with the correct text', () => {
        renderWithThemeRender(render, <Description {...mockProps} />);
        const description = screen.getByText(/Line 1.*Line 2/);
        expect(description).toBeInTheDocument();
    });

    it('renders the description title when showDescriptionTitle is true', () => {
        renderWithThemeRender(render, <Description {...mockProps} showDescriptionTitle />);
        expect(screen.getByText('Description')).toBeInTheDocument();
    });

    it('does not render the description title when showDescriptionTitle is false', () => {
        renderWithThemeRender(render, <Description {...mockProps} showDescriptionTitle={false} />);
        expect(screen.queryByText('Description')).toBeNull();
    });

    it('toggles the description visibility when the toggle is clicked', () => {
        renderWithThemeRender(render, <Description {...mockProps} useToggle />);
        const toggleButton = screen.getByRole('button', { name: /Show more/i });
        expect(toggleButton).toBeInTheDocument();

        fireEvent.click(toggleButton);
        expect(screen.getByRole('button', { name: /Show less/i })).toBeInTheDocument();
    });

    it('shows the SellOneLink component when isForSale is true', () => {
        renderWithThemeRender(render, <Description {...mockProps} isForSale />);
        const toggleButton = screen.getByRole('button', { name: /Show more/i });
        fireEvent.click(toggleButton);
        expect(screen.getByText('Sell one like this')).toBeInTheDocument();
    });

    it('renders the MotorsDisclaimer when isMotors is true', () => {
        renderWithThemeRender(render, <Description {...mockProps} isMotors />);
        const toggleButton = screen.getByRole('button', { name: /Show more/i });
        fireEvent.click(toggleButton);
        expect(
            screen.getByText(
                /Exact specification may vary from the details on this page. Please contact the seller to reconfirm any details before purchasing. See/
            )
        ).toBeInTheDocument();
    });

    it('displays the VAT number and ad ID when available', () => {
        renderWithThemeRender(render, <Description {...mockProps} />);
        const toggleButton = screen.getByRole('button', { name: /Show more/i });
        fireEvent.click(toggleButton);
        expect(screen.getByText('VAT123')).toBeInTheDocument();
        expect(screen.getByText('12345')).toBeInTheDocument();
    });

    it('displays the correct number of lines based on linesToShow prop', () => {
        renderWithThemeRender(render, <Description {...mockProps} linesToShow={1} />);
        const description = screen.getByText(/Line 1.*Line 2/);
        expect(description).toBeInTheDocument();
    });

    it('calls trackV2 when toggle button is clicked', () => {
        const { trackV2 } = require('@gumtree/shared/src/util/track-ga-event');
        renderWithThemeRender(render, <Description {...mockProps} useToggle />);
        const toggleButton = screen.getByRole('button', { name: /Show more/i });

        fireEvent.click(toggleButton);
        expect(trackV2).toHaveBeenCalledWith('ReadMoreClick');

        fireEvent.click(toggleButton);
        expect(trackV2).toHaveBeenCalledWith('ReadLessClick');
    });
});
