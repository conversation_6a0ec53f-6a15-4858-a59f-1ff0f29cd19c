import React from 'react';
import styled from '@emotion/styled';
import { fontSizes, colors } from '@gumtree/ui-library/src/base/theme';

const SmallerP = styled.p`
    font-size: ${fontSizes.smaller};
    max-width: 75ch;

    a {
        color: ${colors.blue};
        text-decoration: none;
    }
`;

const MotorsDisclaimer: React.FC = () => (
    <SmallerP>
        Exact specification may vary from the details on this page. Please contact the seller to
        reconfirm any details before purchasing. See <a href="/termsofuse">Terms & Conditions</a>{' '}
        for further information.
    </SmallerP>
);

export default MotorsDisclaimer;
