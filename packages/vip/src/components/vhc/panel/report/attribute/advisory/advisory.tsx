import React from 'react';
import { AdvisoryResult, AdvisoryNotice, AttributeName } from '../../../../common/style';

interface Props {
    name: string;
    children: JSX.Element[] | JSX.Element | string;
}

const Advisory: React.FC<Props> = ({ name, children }) => (
    <>
        <div>
            <AttributeName>{name}</AttributeName>
            <AdvisoryResult>Advisory</AdvisoryResult>
        </div>
        <AdvisoryNotice>{children}</AdvisoryNotice>
    </>
);

export default Advisory;
