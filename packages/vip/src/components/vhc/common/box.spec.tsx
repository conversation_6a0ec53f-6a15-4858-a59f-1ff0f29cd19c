import React from 'react';
import { render } from '@testing-library/react';

import Box from './box';

const renderBox = (children) => {
    const SUT = render(<Box>{children}</Box>);
    return { ...SUT };
};

describe('Box component', () => {
    it('renders box component', () => {
        const children = <div>Box child</div>;
        const SUT = renderBox(children);
        expect(SUT.asFragment()).toMatchSnapshot();
    });
});
