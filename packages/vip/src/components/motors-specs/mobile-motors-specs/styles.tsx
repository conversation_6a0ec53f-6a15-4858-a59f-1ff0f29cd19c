import styled from '@emotion/styled';
import { css, keyframes } from '@emotion/react';
import {
    colors,
    colorVariables,
    fontSizes,
    gutterSizes,
    mediaQuery,
    breakpoints,
    fontWeights,
} from '@gumtree/ui-library/src/base/theme';

export const StyleContainer = styled.section`
    position: relative;
    scroll-snap-align: start;

    /* to overwite the carousel classes */
    .carousel-inner {
        margin-top: 8px;
        margin-block-end: auto;
    }

    .next {
        color: ${colors.berry};
        position: absolute;
        right: 16px;
        bottom: 32px;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: ${colors.white};
        opacity: 1;
        box-shadow: 0 0 6px rgba(0, 0, 0, 0.34);
        transition: opacity 500ms ease;
    }

    .next.disabled {
        opacity: 0;
        pointer-events: none;
    }

    .prev {
        display: none;
    }

    /* to prevent text highlighting on scroll */
    ${mediaQuery.until(breakpoints.large)} {
        user-select: none;
    }
`;

export const Spacer = styled.div`
    padding: 0 ${gutterSizes.base};
    width: 100%;
`;

export const Card = styled.section<{ longerCard?: boolean }>`
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    width: 100%;
    height: ${({ longerCard }) => (longerCard ? 335 : 280)}px;
    overflow: auto;
    margin-bottom: 10px; /* To allow for shadow to show */
`;

export const Ul = styled.ul`
    list-style-type: none;
    padding: 0;
`;

export const Li = styled.li<{ keyOnly?: boolean }>`
    font-size: ${fontSizes.medium};
    line-height: 22px;

    ${({ keyOnly }) =>
        !keyOnly
            ? css`
                  border-bottom: 1px solid ${colorVariables.borderLighter};
              `
            : css`
                  &:first-of-type {
                      padding-top: ${gutterSizes.base};
                  }
              `}
    :last-of-type:not(:first-of-type) {
        border-bottom: none;
    }
`;

export const LiInner = styled.div<{ keyOnly?: boolean }>`
    ${({ keyOnly }) =>
        keyOnly
            ? css`
                  padding: ${gutterSizes.xsmall} ${gutterSizes.large};
              `
            : css`
                  padding: ${gutterSizes.base} ${gutterSizes.large};
              `}
    display: flex;
    justify-content: space-between;
`;

export const Name = styled.div<{ keyOnly?: boolean }>`
    text-align: left;
    color: ${colorVariables.textNeutral};

    ${({ keyOnly }) =>
        !keyOnly &&
        css`
            font-size: ${fontSizes.base};
            color: ${colorVariables.textNeutral};
            max-width: 168px;
            white-space: pre-wrap;
        `}
`;

export const Value = styled.div`
    font-weight: ${fontWeights.lightBold};
    font-size: ${fontSizes.base};
    text-align: right;
    color: ${colorVariables.textPrimary};
    white-space: pre-wrap;
`;

const transitionDuration = 500;

const tabAnimation = keyframes`
0%{
  color: ${colorVariables.textNeutral};
  font-weight: normal;
}
100%{
    color: ${colorVariables.textPrimary};
    font-weight: 600;

}
`;

export const TabTitle = styled.h2<{ tabActive: boolean; preventAnimation?: boolean }>`
    /* Any changes to padding will need to be replicated in the component */
    padding: ${gutterSizes.base};
    white-space: nowrap;
    margin: 0;
    text-align: center;
    color: ${colorVariables.textNeutral};
    font-size: ${fontSizes.header};
    font-weight: ${fontWeights.lightBold};
    transition: all ${transitionDuration}ms ease;
    cursor: pointer;

    :first-of-type {
        padding-left: 16px;

        ${({ preventAnimation }) =>
            preventAnimation &&  
            css`
                &.tab-active {
                    color: ${colorVariables.textPrimary};
                    font-weight: 600;
                }
            `}
    }

    &.tab-active {
        animation: ${tabAnimation} ${transitionDuration}ms normal both ease;
        animation-iteration-count: 1;
    }
`;

export const TabContainerOuter = styled.div`
    overflow: hidden;
    height: 44px;
    width: 100%;
`;

export const TabContainerInner = styled.div<{ slideWidths: number }>`
    display: flex;
    width: 100%;
    height: 100%;
    transform: ${({ slideWidths }) => `translateX(calc(${slideWidths}px))`};
    align-items: center;
    transition: all ${transitionDuration}ms ease;
`;

export const AdditionalFeaturesContainer = styled.section`
    text-align: left;
    padding: ${gutterSizes.base} ${gutterSizes.large};
`;
