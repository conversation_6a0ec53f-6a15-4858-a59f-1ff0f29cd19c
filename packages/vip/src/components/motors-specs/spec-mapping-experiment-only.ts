const lookup = {
    // overview
    vehicle_registration_year: { group: 'overview', vipOrder: 1 },
    vehicle_mileage: { group: 'overview', vipOrder: 2 },
    vehicle_body_type: { group: 'overview', vipOrder: 3 },
    vehicle_transmission: { group: 'overview', vipOrder: 4 },
    vehicle_colour: { group: 'overview', vipOrder: 5 },
    vehicle_seats: { group: 'overview', vipOrder: 6 },
    vehicle_doors: { group: 'overview', vipOrder: 7 },
    vehicle_previous_keepers: { group: 'overview', vipOrder: 8 },
    vehicle_luggage_capacity: { group: 'overview', vipOrder: 9 },
    //performance
    vehicle_fuel_type: { group: 'performance', vipOrder: 1 },
    vehicle_engine_power: { group: 'performance', vipOrder: 2 },
    vehicle_engine_size: { group: 'performance', vipOrder: 3 },
    vehicle_brochure_engine_size: { group: 'performance', vipOrder: 4 },
    vehicle_top_speed: { group: 'performance', vipOrder: 5 },
    vehicle_acceleration_0_62: { group: 'performance', vipOrder: 6 },
    //running costs
    vehicle_average_mpg: { group: 'runningCosts', vipOrder: 1 },
    vehicle_fuel_capacity: { group: 'runningCosts', vipOrder: 2 },
    vehicle_urban_mpg: { group: 'runningCosts', vipOrder: 3 },
    vehicle_extra_urban_mpg: { group: 'runningCosts', vipOrder: 4 },
    vehicle_insurance_group: { group: 'runningCosts', vipOrder: 5 },
    vehicle_emission: { group: 'runningCosts', vipOrder: 6 },
    vehicle_euro_emission: { group: 'runningCosts', vipOrder: 7 },
    //safety
    electronic_stability_program: { group: 'safety', vipOrder: 1 },
    traction_control: { group: 'safety', vipOrder: 2 },
    child_proof_rear_door_locks: { group: 'safety', vipOrder: 3 },
    passenger_airbags: { group: 'safety', vipOrder: 4 },
    side_airbags: { group: 'safety', vipOrder: 5 },
    dual_front_airbags_package: { group: 'safety', vipOrder: 6 },
    airbag_knee_driver: { group: 'safety', vipOrder: 7 },
    safety_belt_pretensioners: { group: 'safety', vipOrder: 8 },
    anti_lock_braking: { group: 'safety', vipOrder: 9 },
    curtain_airbags: { group: 'safety', vipOrder: 10 },
    alarm_system_remote_anti_theft: { group: 'safety', vipOrder: 11 },
    engine_immobiliser: { group: 'safety', vipOrder: 12 },
    central_locking_remote_control: { group: 'safety', vipOrder: 13 },
    //driver aids
    cruise_control: { group: 'driverAids', vipOrder: 1 },
    power_assisted_steering: { group: 'driverAids', vipOrder: 2 },
    adjustable_steering_wheel: { group: 'driverAids', vipOrder: 3 },
    multi_function_steering_wheel: { group: 'driverAids', vipOrder: 4 },
    parking_sensors: { group: 'driverAids', vipOrder: 5 },
    automatic_stop_start: { group: 'driverAids', vipOrder: 6 },
    navigation_system: { group: 'driverAids', vipOrder: 8 },

    //interior
    leather_steering_wheel: { group: 'interior', vipOrder: 1 },
    leather_seats: { group: 'interior', vipOrder: 2 },
    split_folding_rear_seat: { group: 'interior', vipOrder: 3 },
    air_conditioning: { group: 'interior', vipOrder: 4 },
    automatic_air_climate_control: { group: 'interior', vipOrder: 5 },
    bluetooth_connectivity: { group: 'interior', vipOrder: 6 },
    android_auto: { group: 'interior', vipOrder: 7 },
    trip_computer: { group: 'interior', vipOrder: 8 },
    heated_seats: { group: 'interior', vipOrder: 9 },
    power_front_seats: { group: 'interior', vipOrder: 10 },
    power_windows: { group: 'interior', vipOrder: 11 },
    apple_car_play: { group: 'interior', vipOrder: 12 },
    aux_usb_input_socket: { group: 'interior', vipOrder: 13 },
    radio_with_speakers: { group: 'interior', vipOrder: 14 },
    // exterior
    daytime_running_lights: { group: 'exterior', vipOrder: 1 },
    automatic_headlights_with_dusk_sensor: { group: 'exterior', vipOrder: 2 },
    alloy_wheels: { group: 'exterior', vipOrder: 3 },
    tinted_glass: { group: 'exterior', vipOrder: 4 },
    sunroof: { group: 'exterior', vipOrder: 5 },
    led_headlights: { group: 'exterior', vipOrder: 6 },
    power_mirrors: { group: 'exterior', vipOrder: 7 },
    heated_door_mirrors: { group: 'exterior', vipOrder: 8 },
};

const specMapping = (attributeGroups) => {
    const buckets = {
        overview: [],
        performance: [],
        runningCosts: [],
        safety: [],
        driverAids: [],
        interior: [],
        exterior: [],
    };

    const newAttributeList = attributeGroups.reduce((acc, group) => {
        if (group?.attributes) {
            return [...acc, ...group.attributes];
        }
        return acc;
    }, []);
    const bucketsGrouped = newAttributeList.reduce((acc, attribute) => {
        const key = lookup[attribute.key];
        if (!key) {
            return acc;
        }
        const { group, vipOrder } = key;
        return {
            ...acc,
            [group]: [...acc[lookup[attribute.key].group], { ...attribute, vipOrder }],
        };
    }, buckets);

    const compareOrder = (objA, objB) => +objA.vipOrder - +objB.vipOrder;

    const overview = {
        key: 'overview',
        label: 'Overview',
        attributes: bucketsGrouped.overview.sort(compareOrder),
    };
    const performance = {
        key: 'performance',
        label: 'Performance',
        attributes: bucketsGrouped.performance.sort(compareOrder),
    };
    const runningCosts = {
        key: 'runningCosts',
        label: 'Running Costs',
        attributes: bucketsGrouped.runningCosts.sort(compareOrder),
    };
    const safetyAndSecurity = {
        key: 'safetyAndSecurity',
        label: 'Safety & Security',
        attributes: bucketsGrouped.safety.sort(compareOrder),
        keyOnly: true,
    };
    const driverAids = {
        key: 'driving aids',
        label: 'Driving Convenience',
        attributes: bucketsGrouped.driverAids.sort(compareOrder),
        keyOnly: true,
    };
    const interior = {
        key: 'interior',
        label: 'Interior',
        attributes: bucketsGrouped.interior.sort(compareOrder),
        keyOnly: true,
    };
    const exterior = {
        key: 'exteriror',
        label: 'Exterior',
        attributes: bucketsGrouped.exterior.sort(compareOrder),
        keyOnly: true,
    };

    return [overview, performance, runningCosts, safetyAndSecurity, driverAids, interior, exterior];
};

export default specMapping;
