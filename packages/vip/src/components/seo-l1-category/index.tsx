 
import * as React from 'react';
import { useSelector } from 'react-redux';
import { VipState } from '../../reducers';
import { Category } from './style';

const SeoL1Category: React.FC = () => {
    const seoL1Category = useSelector(
        ({ adDetails: { seoL1Category } }: VipState) => seoL1Category
    );

    return <Category data-l1-category="true">{seoL1Category}</Category>;
};

export default SeoL1Category;
