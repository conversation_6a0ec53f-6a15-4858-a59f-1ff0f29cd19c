import styled from '@emotion/styled';
import { Button } from '@gumtree/ui-library';
import { gutterSizes } from '@gumtree/ui-library/src/base/theme';

export const StyledButton = styled(Button, {
    shouldForwardProp(propName: PropertyKey) {
        return propName !== 'gridColumn';
    },
})<{ gridColumn: string } & React.ComponentProps<typeof Button>>`
    && {
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        grid-column: ${({ gridColumn }) => gridColumn};
        margin-bottom: ${gutterSizes.medium};
    }
`;
