/**
 * Show sticky header when window.scrollY sufficiently large.
 */
export const stickyScroll = (isMobile: boolean, isMCUFeed: boolean) => {
    if (typeof window !== 'undefined') {
        window.addEventListener('scroll', () => {
            const sellerInfoBlock: any = document.querySelector('.seller-info-block-container');
            const stickyHeader = document.querySelector('[data-q="sticky-banner"]');
            const header = document.querySelector('.header');
            const pushdown = document.querySelector('.banner');
            const targetLine =
                sellerInfoBlock.offsetTop +
                (header && pushdown ? header.clientHeight + pushdown.clientHeight : null) +
                sellerInfoBlock.clientHeight;

            if (stickyHeader) {
                if (sellerInfoBlock && !isMobile) {
                    stickyHeader.classList.toggle('is-shown', window.scrollY > targetLine);
                } else {
                    if (isMCUFeed) {
                        stickyHeader.id = 'is-shown-bottom';
                    }
                    stickyHeader.classList.toggle('is-shown', window.scrollY > 0);
                }
            }
        });
    }
};
