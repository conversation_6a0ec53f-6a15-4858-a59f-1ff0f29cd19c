import styled from '@emotion/styled';

import { css } from '@emotion/react';
import {
    mediaQuery,
    breakpoints,
    gutterSizes,
    fontSizes,
    zIndexes,
    colors,
} from '@gumtree/ui-library/src/base/theme';

export const StickyHeaderContainer = styled.div`
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    position: fixed;
    top: 0;
    z-index: ${zIndexes.high};
    width: 100%;
    max-width: ${breakpoints.xlarge};
    padding: ${gutterSizes.base} ${gutterSizes.large};
    left: 0;
    right: 0;
    margin: auto;
    background-color: ${colors.white};
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);

    /** Sticky header + make offer panel */
    transform: translateY(calc(-150% - 144px));
    transition: 0.5s ease;

    &.bottom-sticky {
        transform: translateY(calc(100%));
        bottom: 0;
    }

    &.is-shown {
        transform: translateY(0);
    }

    &#is-shown-bottom {
        top: auto;
        bottom: 0;
    }

    ${mediaQuery.until(breakpoints.medium)} {
        padding: ${gutterSizes.large} ${gutterSizes.base} ${gutterSizes.base};
    }
`;

export const PostingLength = styled.span<{ hasRating: boolean }>`
    ${({ hasRating }) =>
        hasRating &&
        css`
            padding-top: ${gutterSizes.small};
            display: 'inline-block';
        `}
`;

const H2 = css`
    line-height: 0.8;
    margin-bottom: 0;
`;

export const H2Small = styled.h2`
    ${H2}
    ${mediaQuery.from(breakpoints.medium)} {
        display: none;
    }
`;

export const H2Large = styled.h2`
    ${H2}
    ${mediaQuery.until(breakpoints.medium)} {
        display: none;
    }
`;

export const StickyHeaderTxtRow = styled.div`
    margin-bottom: ${gutterSizes.small};

    h2 {
        margin-bottom: ${gutterSizes.base};
    }

    ${mediaQuery.until(breakpoints.medium)} {
        span {
            font-size: ${fontSizes.small};
            color: ${colors.bark60};
            margin-top: ${gutterSizes.small};
        }
    }
`;

export const StickyHeaderFnRow = styled.div<{ pro: boolean }>`
    ${mediaQuery.until(breakpoints.medium)} {
        flex-basis: 100%;

        .seller-phone {
            margin: ${gutterSizes.base} 0;
        }
    }

    ${mediaQuery.from(breakpoints.medium)} {
        display: flex;

        .seller-phone {
            margin-right: ${gutterSizes.base};
        }

        ${({ pro }) =>
            pro &&
            css`
                justify-content: space-between;

                .seller-phone {
                    margin-top: ${gutterSizes.base};
                    margin-right: ${gutterSizes.large};
                }
            `}
    }
`;

interface StickyButtonsProps {
    isMobile: boolean;
    canCall: boolean;
    canSms: boolean;
    canOffer: boolean;
    canEmail: boolean;
}

export const StickyButtonsContainer = styled.div<StickyButtonsProps>`
    display: grid;
    grid-gap: ${gutterSizes.small};
    grid-template-columns: 1fr 1fr;

    ${({ canCall, canSms, canOffer, canEmail }) => {
        if (canEmail) {
            if (canCall && canSms && canOffer) {
                return css`
                    grid-template-areas:
                        'reply-email reply-phone reply-sms'
                        'make-offer make-offer make-offer';
                    grid-template-columns: 1fr 1fr 1fr;
                `;
            }
            if (canCall && canOffer) {
                return css`
                    grid-template-areas: 'reply-email reply-phone make-offer';
                    grid-template-columns: 1fr 1fr 1fr;
                `;
            }
            if (canCall && canSms) {
                return css`
                    grid-template-areas: 'reply-email reply-phone reply-sms';
                    grid-template-columns: 1fr 1fr 1fr;
                `;
            }
            if (canCall) {
                return css`
                    grid-template-areas: 'reply-email reply-phone';
                    grid-template-columns: 1fr 1fr;
                `;
            }
            if (canOffer) {
                return css`
                    grid-template-areas: 'reply-email make-offer';
                    grid-template-columns: 1fr 1fr;
                `;
            }
            return css`
                grid-template-areas: 'reply-email reply-email';
                grid-template-columns: 1fr 1fr;
            `;
        } else {
            if (canCall && canSms && canOffer) {
                return css`
                    grid-template-areas:
                        'reply-phone reply-sms'
                        'make-offer make-offer';
                    grid-template-columns: 1fr 1fr;
                `;
            }
            if (canCall && canOffer) {
                return css`
                    grid-template-areas: 'reply-phone make-offer';
                    grid-template-columns: 1fr 1fr;
                `;
            }
            if (canCall && canSms) {
                return css`
                    grid-template-areas: 'reply-phone reply-sms';
                    grid-template-columns: 1fr 1fr;
                `;
            }
            if (canCall) {
                return css`
                    grid-template-areas: 'reply-phone';
                    grid-template-columns: 1fr;
                `;
            }
            if (canOffer) {
                return css`
                    grid-template-areas: 'make-offer';
                    grid-template-columns: 1fr;
                `;
            }
            return css``; // fallback layout when no actions are available
        }
    }}

    ${mediaQuery.from(breakpoints.medium)} {
        /** Legacy mobile doesn't support sticky header ≥ medium */
        display: ${({ isMobile }) => isMobile && 'none'};

        grid-template-areas: 'reply-email make-offer';
        grid-template-columns: 160px;
        grid-template-columns: ${({ canOffer }) => canOffer && '160px 160px'};
    }

    ${mediaQuery.from(breakpoints.large)} {
        grid-template-columns: 250px;
        grid-template-columns: ${({ canOffer }) => canOffer && '250px 250px'};
    }
`;
