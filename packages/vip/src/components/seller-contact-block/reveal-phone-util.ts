import { type BaseConfigState as BaseConfigState } from '@gumtree/shell/src/reducers/common/base-config';
import type { VipUseDispatch } from '../../reducers';
import { type State as AdDetailsState } from '../../reducers/ad-details';
import {
    revealNumberThunk,
    type State as SellerContactDetailsState,
} from '../../reducers/seller-contact-details';
import { trySetCookiePhoneRevealBegin } from './phone-reveal-tracker';

export default function revealPhoneUtil(
    e: React.MouseEvent | null,
    dispatch: VipUseDispatch,
    sellerContactDetails: SellerContactDetailsState,
    adDetails: AdDetailsState,
    baseConfig: BaseConfigState,
    redirectToApp = ''
) {
    e?.preventDefault();

    const {
        revealSellerTelephoneNumberToken,
        userLoggedIn,
        showRevealNumber,
        isLoginRequiredToContactForVipCategory,
    } = sellerContactDetails;

    const { id, isFromMotorsFeed } = adDetails;
    const { domain } = baseConfig;

    const shouldPhoneRevealBeginFunc = () =>
        trySetCookiePhoneRevealBegin(
            userLoggedIn,
            id,
            showRevealNumber,
            isLoginRequiredToContactForVipCategory,
            domain
        );

    dispatch(
        revealNumberThunk({
            advertId: id,
            revealSellerTelephoneNumberToken,
            shouldPhoneRevealBeginFunc,
            redirectToApp,
            isFromMotorsFeed,
        })
    );
}
