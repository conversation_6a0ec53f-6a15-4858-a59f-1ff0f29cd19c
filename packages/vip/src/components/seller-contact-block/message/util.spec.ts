import { CONVERSATION_SERVICE_TIMEOUT_MS } from '@gumtree/shared/src/constants/message-centre';
import { sendMessage } from './util';

jest.useFakeTimers();

describe('sendMessage util', () => {
    it('should send a message and return a confirmation URL', async () => {
        const opts = {
            sendReplyUrl: 'https://example.com/send-reply',
            advertId: '123',
            message: 'Hello, world!',
            phoneNumber: '555-1234',
            token: 'abc123',
        };

        const expectedResponse = {
            replyConfirmationUrl: 'https://example.com/reply-confirmation',
            errors: null,
        };

        // Mock the fetch function to return the expected response
        jest.spyOn(global, 'fetch').mockResolvedValueOnce({
            json: jest.fn().mockResolvedValueOnce(expectedResponse),
        } as any);

        const response = await sendMessage(opts);

        expect(response).toEqual(expectedResponse);

        // Verify that fetch was called with the correct arguments
        expect(fetch).toHaveBeenCalledWith(opts.sendReplyUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                accept: 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                X_CSRF_TOKEN: opts.token,
            },
            body: JSON.stringify({
                advertId: opts.advertId,
                message: opts.message,
                phoneNumber: opts.phoneNumber,
            }),
        });
    });

    it('should throw an error if the response contains errors', async () => {
        const opts = {
            sendReplyUrl: 'https://example.com/send-reply',
            advertId: '123',
            message: 'Hello, world!',
            phoneNumber: '555-1234',
            token: 'abc123',
        };

        const expectedResponse = {
            payload: { errors: ['Something went wrong'] },
        };

        // Mock the fetch function to return the expected response
        jest.spyOn(global, 'fetch').mockResolvedValueOnce({
            json: jest.fn().mockResolvedValueOnce(expectedResponse),
        } as any);

        await expect(sendMessage(opts)).rejects.toEqual({ payload: expectedResponse });
    });

    it('should throw an error if the response does not contain a confirmation URL', async () => {
        const opts = {
            sendReplyUrl: 'https://example.com/send-reply',
            advertId: '123',
            message: 'Hello, world!',
            phoneNumber: '555-1234',
            token: 'abc123',
        };

        const expectedResponse = {
            errors: null,
            payload: {},
        };

        // Mock the fetch function to return the expected response
        jest.spyOn(global, 'fetch').mockResolvedValueOnce({
            json: jest.fn().mockResolvedValueOnce(expectedResponse),
        } as any);

        await expect(sendMessage(opts)).rejects.toEqual({
            payload: expectedResponse,
        });
    });

    it('should throw an error if the response times out', async () => {
        const opts = {
            sendReplyUrl: 'https://example.com/send-reply',
            advertId: '123',
            message: 'Hello, world!',
            phoneNumber: '555-1234',
            token: 'abc123',
        };

        // Mock the fetch function to never resolve
        jest.spyOn(global, 'fetch').mockImplementation(() => new Promise(() => {}));

        const promise = sendMessage(opts);

        // Fast-forward time to simulate timeout
        jest.advanceTimersByTime(CONVERSATION_SERVICE_TIMEOUT_MS);

        await expect(promise).rejects.toEqual('Response timed out');

        jest.restoreAllMocks();
    });
});
