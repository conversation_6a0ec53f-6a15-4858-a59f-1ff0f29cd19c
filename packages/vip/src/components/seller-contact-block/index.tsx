import React, { useContext } from 'react';
import { shallowEqual, useSelector } from 'react-redux';
import { Hr, <PERSON>, Ribbon } from '@gumtree/ui-library';
import VerfifiedBadge from '@gumtree/ui-library/src/assets/Shield-checkmark.svg';
import { qaAttribute } from '@gumtree/ui-library/src/utils/qa-service';
import { trackGA4Event } from '@gumtree/shared/src/util/ga4-shared';
import { getDomainFromUrl } from '@gumtree/shared/src/util/url-service';
import { getL1CategoryIdBySeoName } from '@gumtree/shared/src/util/category-mapping';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { GrowthBookFeature } from '@gumtree/shared/src/model/experiment';

import type { VipState } from '../../reducers';
import revealPhoneUtil from './reveal-phone-util';
import ContactButtons from '../contact-buttons/contact-buttons';
import Message from './message/message';
import MakeOffer from './make-offer/make-offer';
import {
    ButtonGroup,
    ContactName,
    Container,
    WebsiteUrlTruncation,
    IconInformation,
} from './style';
import SellerPhoneRevealLogic from './seller-phone-reveal-logic';
import { MCUFeedContext } from '../../vip-context';

export const SellerContactBlock: React.FC = () => {
    const {
        adDetails,
        baseConfig,
        sellerContactDetails: {
            contactName,
            replyPhone,
            replySmsEnabled,
            replyLink,
            replyEmail,
            websiteUrl,
            websiteName,
        },
        isMotors,
    } = useSelector(
        ({ adDetails, baseConfig, sellerContactDetails }: VipState) => ({
            adDetails,
            baseConfig,
            sellerContactDetails,
            isMotors: adDetails.l1CategorySeoName === 'cars-vans-motorbikes',
            isFromMotorsFeed: adDetails.isFromMotorsFeed,
        }),
        shallowEqual
    );

    const makeAnOfferEnabled = useSelector(
        ({ sellerActions }: VipState) => sellerActions.makeAnOfferEnabled
    );
    const isMobile = baseConfig.device?.type === 'mobile';
    const hasButtons = !!replyEmail || !!replyLink || !!replyPhone;

    const showBreederRibbon = false;

    const isMCUFeed = useContext(MCUFeedContext)?.isMCUFeed;
    const hideSMS = useFeatureIsOn(GrowthBookFeature.HIDE_SMS) && adDetails.isFromMotorsFeed;

    return (
        <Container>
            {adDetails.l1Category !== getL1CategoryIdBySeoName('for-sale') ? (
                <>
                    {showBreederRibbon &&
                        adDetails.l1Category === getL1CategoryIdBySeoName('pets') && (
                            <Ribbon isActive className="breeder-ribbon">
                                Licensed breeder
                            </Ribbon>
                        )}
                    {!isMCUFeed && (
                        <ContactName
                            data-q="seller-name"
                            data-testid="seller-name"
                            hasButtons={hasButtons}
                        >
                            Contact <span itemProp="name">{contactName}</span>
                        </ContactName>
                    )}
                </>
            ) : (
                <IconInformation {...qaAttribute('icon-verified')} isEmailVerified>
                    <VerfifiedBadge /> Email address verified
                </IconInformation>
            )}

            {!isMobile && (
                <>
                    {replyPhone && !isMCUFeed && <SellerPhoneRevealLogic />}
                    {isMotors && !!replyEmail && (
                        <>
                            <Message />
                            {makeAnOfferEnabled && <MakeOffer />}
                        </>
                    )}
                </>
            )}

            <ButtonGroup
                canCall={isMobile && !!replyPhone}
                canSms={isMobile && replySmsEnabled && !hideSMS}
                canOffer={!!replyEmail && makeAnOfferEnabled}
            >
                <ContactButtons
                    replyLink={replyLink}
                    replyEmail={replyEmail}
                    replyPhone={replyPhone}
                    replySmsEnabled={replySmsEnabled && !hideSMS}
                    revealPhoneUtil={revealPhoneUtil}
                    isMobile={isMobile}
                    l1CategorySeoName={adDetails.l1CategorySeoName}
                />
            </ButtonGroup>

            {websiteUrl && !isMotors && (
                <>
                    <Hr />
                    <Link
                        openInNewTab
                        noFollow
                        href={websiteUrl}
                        onClick={async () => {
                            trackGA4Event<GA4.ContactSellerExternalEvent>({
                                event: 'contact_seller_external',
                                linkDomain: getDomainFromUrl(websiteUrl),
                                linkURL: websiteUrl,
                                linkText: websiteName || websiteUrl,
                                formValidation: 'success',
                            });
                        }}
                        dataQ="seller-websiteUrl"
                    >
                        <WebsiteUrlTruncation>{websiteName || websiteUrl}</WebsiteUrlTruncation>
                    </Link>
                </>
            )}
        </Container>
    );
};

SellerContactBlock.defaultProps = {
    revealNumberUnmasked: null,
    replyPhone: undefined,
};

export default SellerContactBlock;
