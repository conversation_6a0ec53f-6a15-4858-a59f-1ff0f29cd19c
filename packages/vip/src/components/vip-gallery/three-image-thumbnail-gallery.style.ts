import styled from '@emotion/styled';
import { gutterSizes, colors, mediaQuery, breakpoints } from '@gumtree/ui-library/src/base/theme';
import { Icon } from '@gumtree/ui-library';

export const VipGallery = styled.div`
    height: 250px;
    grid-template-columns: repeat(3, 33.33%);
    display: grid;
    grid-template-rows: repeat(4, 25%);
    position: relative;

    .lightbox-content {
        top: 0;
        padding: 0;

        @include mq($from: s) {
            height: 100%;
            width: 100%;
        }

        .carousel-counter-container {
            top: 0;
            bottom: auto;
        }
    }

    .lightbox {
        background: ${colors.black};

        ${mediaQuery.from(breakpoints.small)} {
            width: 100%;
            max-width: 100%;
            height: 100%;
            border: none;
        }
    }

    .carousel-controls-container {
        ${mediaQuery.until(breakpoints.small)} {
            display: none;
        }
    }
`;

export const VipGalleryImage = styled.div<{ imageNumber: number }>`
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    width: 100%;

    ${(props) =>
        props.imageNumber === 0 && `
        grid-column: 1 / span 2;
        grid-row: 1 / span 4;
    `};

    ${(props) =>
        props.imageNumber === 1 && `
        grid-row: 1 / span 2;
    `};

    ${(props) =>
        props.imageNumber === 2 && `
        grid-row: 3 / span 4;
    `};
`;

export const VipGalleryCounterContainer = styled.div`
    position: absolute;
    bottom: ${gutterSizes.medium};
    display: flex;
    left: ${gutterSizes.medium};
    background: rgba(0, 0, 0, 0.8);
    justify-content: center;
    align-items: center;
    padding: ${gutterSizes.medium};
`;

export const VipGalleryIcon = styled(Icon)`
    padding-right: 6px;

    &::before {
        color: ${colors.white};
    }
`;

export const VipGalleryCounter = styled.p`
    margin: 0;
    color: ${colors.white};
`;
