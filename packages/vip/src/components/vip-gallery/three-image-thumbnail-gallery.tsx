import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import type { ImageSlide, VideoSlide } from '@gumtree/ui-library/src/carousel/slider';

import { VipState } from '../../reducers';
import { openDialog } from '../../reducers/image-dialog';

import CarouselGallery from '../carousel-gallery/index.container';
import {
    VipGallery,
    VipGalleryImage,
    VipGalleryCounterContainer,
    VipGalleryCounter,
    VipGalleryIcon,
} from './three-image-thumbnail-gallery.style';

const ThreeImageThumbnailGallery: React.FC<Props> = ({ mediaLinks, itemsShowing }) => {
    const isDialogOpen = useSelector(({ imageDialog }: VipState) => !!imageDialog.isDialogOpen);
    const dispatch = useDispatch();

    const imageSlice = mediaLinks.length >= 3 ? mediaLinks.slice(0, 3) : mediaLinks;

    return (
        <VipGallery data-testid="three-image-gallery">
            {imageSlice.map((mediaLink, index) => {
                const { link: imageUrl } = mediaLink;
                const backgroundImage = `url(${imageUrl})`;
                return (
                    <VipGalleryImage
                        imageNumber={index}
                        key={imageUrl}
                        onClick={() => {
                            dispatch(openDialog(index + 1));
                        }}
                        style={{ backgroundImage }}
                    />
                );
            })}
            <VipGalleryCounterContainer
                onClick={() => {
                    dispatch(openDialog(1));
                }}
            >
                <span>
                    <VipGalleryIcon type="camera" />
                </span>
                <VipGalleryCounter data-testid="counter">{itemsShowing}</VipGalleryCounter>
            </VipGalleryCounterContainer>
            {isDialogOpen && <CarouselGallery mediaLinks={mediaLinks} hasCounterDots={false} />}
        </VipGallery>
    );
};

interface Props {
    mediaLinks: (ImageSlide | VideoSlide)[];
    itemsShowing: number;
}

export default ThreeImageThumbnailGallery;
