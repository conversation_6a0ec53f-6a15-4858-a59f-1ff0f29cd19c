import React from 'react';
import { render, screen } from '@testing-library/react';
import { useSelector } from 'react-redux';

import { mocked } from 'jest-mock';
import SellerAvatarBlock from './index';

jest.mock('react-redux', () => ({
    useDispatch: jest.fn(),
    useSelector: jest.fn(),
}));

jest.mock('@gumtree/shared/src/util/date-helpers', () => ({
    statusTimestampCheck: jest.fn(),
}));

jest.mock('@growthbook/growthbook-react', () => ({
    useFeatureIsOn: jest.fn(() => false),
}));

const mockUseSelector = mocked(useSelector);

function mockState({ logoUrl, l1Category, isFromMotorsFeed = false }) {
    mockUseSelector.mockImplementation((selector) =>
        selector({
            sellerStats: {
                postingSince: '1+ months',
            },
            adDetails: {
                l1Category,
                logoUrl,
                sellerOtherAds: '/seller-ads',
                postingSince: '3 months',
                isFromMotorsFeed,
            },
            sellerRating: {
                average: 4.3,
                contactName: 'Cat',
                total: 10,
                accountPublicId: '123444',
            },
            images: [],
            vipSellerInfo: {
                lastActiveStatus: '2024-03-07T10:44:50Z',
            },
            sellerContactDetails: {
                websiteUrl: 'https://example.com',
            },
        } as any)
    );
}

const renderSellerAvatarBlock = (extraProps = {}) => render(<SellerAvatarBlock {...extraProps} />);

describe('Seller avatar component', () => {
    it('renders a for-sale VIP', () => {
        mockState({
            logoUrl: '',
            l1Category: 2549,
        });

        const SUT = renderSellerAvatarBlock();
        expect(SUT.asFragment()).toMatchSnapshot();
    });

    it('renders a non for-sale VIP', () => {
        mockState({
            logoUrl: '',
            l1Category: 2551,
        });

        const SUT = renderSellerAvatarBlock();
        expect(SUT.asFragment()).toMatchSnapshot();
        expect(screen.getByText('See all ads')).toBeInTheDocument();
        expect(screen.getByText('See all ads')).toHaveAttribute('href', '/seller-ads');
    });

    it('renders rating stars', () => {
        mockState({
            logoUrl: '',
            l1Category: 2549,
        });
        renderSellerAvatarBlock();
        expect(screen.getAllByTestId('full-star')).toHaveLength(4);
        expect(screen.getAllByTestId('half-star')).toHaveLength(1);
    });

    it('should be wrapped in link if in for sale', () => {
        mockState({
            logoUrl: '',
            l1Category: 2549,
        });
        renderSellerAvatarBlock();
        expect(screen.getByRole('link')).toHaveAttribute('href', '/profile/account/123444');
    });

    it('renders logo on pro accout vip', () => {
        mockState({
            logoUrl: 'https://image.png',
            l1Category: 2549,
        });
        renderSellerAvatarBlock();
        expect(screen.getByAltText('vip logo')).toBeInTheDocument();
        expect(screen.getByAltText('vip logo')).toHaveAttribute('src', 'https://image.png');
    });

    it('should show website link if not from motors feed', () => {
        mockState({
            isFromMotorsFeed: false,
            logoUrl: '',
            l1Category: 2551,
        });

        renderSellerAvatarBlock();
        expect(screen.getByText('Visit website')).toBeInTheDocument();
    });

    it('should hide website link if from motors feed', () => {
        mockState({
            isFromMotorsFeed: true,
            logoUrl: '',
            l1Category: 2551,
        });

        renderSellerAvatarBlock();
        expect(screen.queryByText('Visit website')).not.toBeInTheDocument();
    });
});
