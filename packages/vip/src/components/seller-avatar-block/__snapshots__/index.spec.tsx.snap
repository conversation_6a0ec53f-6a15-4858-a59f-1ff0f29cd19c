// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Seller avatar component renders a for-sale VIP 1`] = `
<DocumentFragment>
  <div
    class="css-1ime87-seller-avatar-block e16ke07a13"
  >
    <a
      data-q="view-profile-link"
      href="/profile/account/123444"
    >
      <div
        class="css-1tncdo9-seller-avatar-block e16ke07a14"
      >
        <div
          class="css-4ds3b6-avatar ewylv3c1"
        >
          C
        </div>
        <div
          class="seller-name-rating-bundle "
        >
          <div
            class="css-1glpen8-seller-avatar-block e16ke07a9"
          >
            <h2
              class="truncate-line seller-rating-block-name"
            >
              Cat
            </h2>
          </div>
          <div
            class="css-11i922j-seller-avatar-block e16ke07a12"
          >
            <div
              class="css-1bo3cuu-seller-avatar-block e16ke07a11"
            >
              <span
                aria-hidden="true"
                class="icon icon--color-blade icon--small icon--full-star rating-stars-icon css-1xauxlx-rating-stars e1atiuzs8"
                data-testid="full-star"
              />
              <span
                aria-hidden="true"
                class="icon icon--color-blade icon--small icon--full-star rating-stars-icon css-1xauxlx-rating-stars e1atiuzs8"
                data-testid="full-star"
              />
              <span
                aria-hidden="true"
                class="icon icon--color-blade icon--small icon--full-star rating-stars-icon css-1xauxlx-rating-stars e1atiuzs8"
                data-testid="full-star"
              />
              <span
                aria-hidden="true"
                class="icon icon--color-blade icon--small icon--full-star rating-stars-icon css-1xauxlx-rating-stars e1atiuzs8"
                data-testid="full-star"
              />
              <span
                aria-hidden="true"
                class="icon icon--color-blade icon--small icon--half-star rating-stars-icon css-1xauxlx-rating-stars e1atiuzs8"
                data-testid="half-star"
              />
              <span
                class="count"
              >
                (10)
              </span>
            </div>
          </div>
        </div>
        <div
          class="seller-stats-component seller-stats-newLayout"
          data-testid="seller-stats-component"
        >
          <div
            class="seller-stats-item newLayout"
          >
            <p
              class="seller-stats-text newLayout"
            >
              Posting for
              <span>
                 1+ months
              </span>
            </p>
          </div>
          <div
            class="seller-stats-active"
          >
            <span>
              |
            </span>
            <div />
          </div>
        </div>
      </div>
    </a>
  </div>
</DocumentFragment>
`;

exports[`Seller avatar component renders a non for-sale VIP 1`] = `
<DocumentFragment>
  <div
    class="css-403p9p-seller-avatar-block e16ke07a13"
  >
    <div
      class="css-1tncdo9-seller-avatar-block e16ke07a14"
    >
      <div
        class="seller-name-rating-bundle "
      >
        <div
          class="css-1glpen8-seller-avatar-block e16ke07a9"
        >
          <h2
            class="truncate-line seller-rating-block-name"
          >
            Cat
          </h2>
        </div>
        <section
          class="posting-length css-1b5ky3n-seller-avatar-block e16ke07a8"
        >
          <div
            class="css-15va8e8-seller-avatar-block e16ke07a7"
          >
            <p
              class="css-14tlj9l-seller-avatar-block e16ke07a4"
            >
              Posting for 3 months
            </p>
            <a
              class="link"
              data-q="seller-see-all-ads"
              href="/seller-ads"
              rel="noopener noreferrer"
              title=""
            >
              See all ads
            </a>
          </div>
          <a
            class="link"
            data-q="seller-websiteUrl"
            href="https://example.com"
            rel="noopener noreferrer nofollow"
            target="_blank"
            title=""
          >
            <div
              class="css-1y72nnh-seller-avatar-block e16ke07a6"
            >
              <svg
                fill="none"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M20.84 3.159C20.736 3.053 20.368 3 20 3h-3.5c-.367 0-.735.053-.841.159-.212.212-.212 1.469 0 1.682.106.106.474.159.84.159h1.087l-5.23 5.23c-.26.26-.482.557-.482.707 0 .3.889 1.189 1.189 1.189.15 0 .447-.222.707-.482v-.001L19 6.414V7.5c0 .367.053.735.159.841.213.212 1.47.212 1.682 0 .106-.106.159-.474.159-.841V4c0-.367-.053-.735-.16-.841zM19 19c0 .367-.053.735-.16.841-.106.106-.474.159-.842.159H5c-.367 0-.735-.053-.841-.159-.106-.107-.16-.474-.16-.841V6c0-.367.054-.735.16-.841.107-.106.475-.159.843-.159H11c.367 0 .735.**************.212.213 1.469 0 1.682-.105.106-.473.159-.84.159H6v11h11v-5c0-.368.053-.735.159-.841.213-.212 1.469-.212 1.682 0 .106.106.159.473.159.841v6z"
                  fill="#007FB0"
                  fill-rule="evenodd"
                />
              </svg>
               
              <span>
                Visit website
              </span>
            </div>
          </a>
        </section>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
