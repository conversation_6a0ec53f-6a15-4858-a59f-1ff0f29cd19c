import styled from "@emotion/styled";
import { StyledAvatar } from "@gumtree/message-centre/src/components/ui-atoms/avatar/style";
import chevron from "@gumtree/ui-library/src/assets/chevron_right.png";
import {
    colors,
    fontSizes,
    fontWeights,
    gutterSizes,
    colorVariables,
    boxSizes,
    mediaQuery,
    breakpoints,
} from "@gumtree/ui-library/src/base/theme";

const shouldForwardProp = (p: string) => !["isMCUFeed"].includes(p);

export const SellerNameRating = styled.div<{ isJobs: boolean }>`
    ${(props) =>
        props.isJobs &&
        `
        display: flex;
        flex-wrap: wrap;

        > div {
            width: 100%;
        }
    `};
`;

export const SellerBlockContainer = styled.div<{
    darkBackground: boolean;
    otherAds: boolean;
    noRating: boolean;
    noRatingCategory: boolean;
    hasLogo: boolean;
    inForSaleCategory: boolean;
}>`
    .seller-rating-block-name {
        color: ${colors.white};
        margin: 0;
        font-size: ${fontSizes.xlarge};
        font-weight: ${fontWeights.bold};
    }

    ${(props) =>
        props.noRating &&
        `
        display: flex;
        justify-content: space-between;
        align-items: center;
    `};

    ${(props) =>
        props.inForSaleCategory &&
        `
        transition: background-color 222ms ease-out;
        display: block;

        .no-rating {
            display: flex;
        }

        &:hover {
            background: ${colorVariables.backgroundLighter};

            .seller-name-rating-bundle {
                flex: 1;
                position: relative;

                &:after {
                    content: url(${chevron});
                    font-size: ${fontSizes.base};
                    position: absolute;
                    right: ${gutterSizes.base};
                    top: 50%;
                    font-weight: ${fontWeights.lightBold};
                }
            }
        }

        .seller-rating-block-name {
            color: ${colors.messageTextColor};
            font-weight: ${fontWeights.bold};
            font-size: ${fontSizes.header};
        }

        .seller-name-rating-bundle {
            padding-left: ${gutterSizes.base};
            width: calc(100% - 56px);
        }

        ${SellerNameRating} {
            display: flex;
            flex-wrap: wrap;
            padding: ${gutterSizes.large} ${gutterSizes.large} 0 ${gutterSizes.large};
        }

        ${StyledAvatar} {
            font-size: ${fontSizes.base};
        }
    `};

    ${(props) =>
        props.darkBackground &&
        `
        background: ${colors.navy};
        padding: ${gutterSizes.medium};
        color: ${colors.white};
    `};

    ${(props) =>
        props.otherAds &&
        `
        padding: ${gutterSizes.medium};

        .seller-rating-block-name {
            color: ${colors.bark};
        }

        .all-ads-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            border-bottom: 1px solid ${colors.bark20};
            padding-bottom: ${gutterSizes.medium};

            .posting-length {
                margin: 0;
            }
        }
    `};

    ${(props) =>
        props.noRating &&
        props.otherAds &&
        `
        display: block;
        padding: ${gutterSizes.medium};
    `};

    ${(props) =>
        props.noRatingCategory &&
        `
        background: ${colors.navy};
        padding: ${gutterSizes.medium};
    `};

    ${(props) =>
        props.hasLogo &&
        `
        padding: ${gutterSizes.medium};
        justify-content: flex-start;

        .vip-cta-logo {
            margin-bottom: ${gutterSizes.medium};
            height: 55px;
            width: 116px;
            border-right: 1px solid ${colors.bark20};
            border-bottom: 1px solid ${colors.bark20};
            position: relative;
            float: left;
            margin-right: ${gutterSizes.large};

            img {
                max-width: 100%;
                max-height: 100%;
                display: block;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
        }
    `};

    ${(props) =>
        props.hasLogo &&
        props.otherAds &&
        `
        padding: 0 ${gutterSizes.medium} 0 0;
        border-bottom: 1px solid ${colors.bark20};
        height: auto;
        overflow: hidden;
        align-items: center;
        display: flex;

        .vip-cta-logo {
            margin-bottom: 0;
            border-bottom: 0;
        }

        .posting-length {
            border: none;

            > div {
                padding: 0;
                flex-wrap: wrap;

                p {
                    width: 100%;
                }
            }
        }

        .all-ads-container {
            border-bottom: none;
        }
    `};
`;

export const RatingStarContainer = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;

    .link {
        color: ${colors.blue40};
    }
`;

export const RatingStarContainerTotal = styled.div`
    display: flex;
    align-items: flex-end;

    .count {
        font-size: ${fontSizes.small};
        line-height: ${fontSizes.small};
    }

    ${mediaQuery.until(breakpoints.medium)} {
        span {
            color: ${colors.blade};
        }
    }
`;

export const RatingStarTotal = styled.span`
    color: ${colors.white};
    padding: 0 ${gutterSizes.small};
`;

export const SellerNameContainer = styled.div`
    display: flex;
    align-items: center;
`;

export const PostingForWithBadges = styled("section", { shouldForwardProp })<{
    isMCUFeed?: boolean;
}>`
    ${(props) =>
        !props.isMCUFeed &&
        `
            border-bottom: 1px solid ${colors.bark20};
        `}
`;

export const PostingForContainer = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    padding-bottom: ${gutterSizes.medium};
`;

export const WebsiteLink = styled.div`
    display: flex;
    gap: ${gutterSizes.small};
    align-items: center;
    margin-bottom: ${gutterSizes.medium};
`;

export const MCUPhoneBlock = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    border-top: 1px solid ${colorVariables.borderLight};
    padding-top: 20px;
    padding-bottom: ${gutterSizes.small};

    .icon {
        margin-right: ${gutterSizes.base};
    }

    p {
        margin: 0;
        font-weight: ${fontWeights.bold};
    }
`;

export const PostingLength = styled.p`
    margin: 0;
`;

export const JobImage = styled.div`
    height: 55px;
    width: auto;
    background-color: ${colors.white};
    float: left;
    margin-right: ${gutterSizes.base};
    position: relative;

    img {
        max-height: 100%;
    }
`;

export const BuyerModuleContainer = styled.div`
    box-sizing: border-box;
    box-shadow: 2px 1px 8px ${colorVariables.shadow};
    border-radius: ${boxSizes.borderRadius};
    position: relative;
    padding: ${gutterSizes.large};
    background: ${colors.white};
    overflow: scroll;
    max-height: 100%;
`;

export const BuyerModuleTitle = styled.h4`
    font-size: ${fontSizes.base};
    font-weight: ${fontWeights.bold};
    width: 100%;
    padding-right: ${gutterSizes.xlarge};
    margin-bottom: 0;

    button {
        background: none;
        border: none;
        color: ${colorVariables.textLink};
        padding: 0;
        cursor: pointer;
    }
`;

export const BuyerTextContainer = styled.div`
    display: flex;
    justify-content: center;
    flex-wrap: nowrap;
    width: 100%;

    .icon {
        font-size: ${fontSizes.xlarge};
        padding-right: ${gutterSizes.medium};
        position: relative;
    }
`;
