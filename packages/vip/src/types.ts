import { VipSellerInfoState } from "./reducers/vip-seller-info";

export type VipDataInit = {
    lastActiveStatus: string | null;
    categoriesCount: VipSellerInfoState["categoriesCount"];
    totalAdvertCount: number;
    reviewItemAndFeedbackSummary: ReviewItems;
    filteredSortedFeedbackTags: FeedbackTag[];
};

export interface ReviewItems {
    feedbackSummaryList: FeedbackTag[];
    reviewItemList: any | null;
}

export interface FeedbackTag {
    /** e.g. `'Friendly'` */
    title: string;
    /** e.g. `positive` */
    type: string;
    count: number;
}
