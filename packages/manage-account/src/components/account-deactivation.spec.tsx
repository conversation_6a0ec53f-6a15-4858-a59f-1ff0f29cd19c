import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { useSelector, useDispatch } from 'react-redux';
import { useMutation } from 'react-query';
import { renderWithThemeRender } from '@gumtree/shared/src/test-utils/mock-theme';
import AccountDeactivation from './account-deactivation';

jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
    useDispatch: jest.fn(),
}));

jest.mock('react-query', () => ({
    useMutation: jest.fn(),
}));

describe('AccountDeactivation Component', () => {
    const mockDispatch = jest.fn();
    const mockSubmitAccountDeactivation = jest.fn();

    beforeEach(() => {
        useSelector.mockReturnValue({
            secureToken: 'test-secure-token',
            sellerUrl: 'https://mock.seller.url',
        });

        useDispatch.mockReturnValue(mockDispatch);
        useMutation.mockReturnValue({
            mutate: mockSubmitAccountDeactivation,
        });

        mockDispatch.mockClear();
        mockSubmitAccountDeactivation.mockClear();
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    it('should render the account deactivation link correctly', () => {
        renderWithThemeRender(render, <AccountDeactivation />);

        expect(screen.getByText('Deactivate my account')).toBeInTheDocument();
    });

    it('should open the modal when "Deactivate my account" is clicked', async () => {
        renderWithThemeRender(render, <AccountDeactivation />);

        const deactivateLink = screen.getByText('Deactivate my account');
        fireEvent.click(deactivateLink);

        await waitFor(() => {
            expect(screen.getByText('Deactivate account')).toBeInTheDocument();
            expect(
                screen.getByText(
                    "Please help us improve Gumtree by letting us know why you're leaving:"
                )
            ).toBeInTheDocument();
        });
    });

    it('should allow selecting a deactivation reason', async () => {
        renderWithThemeRender(render, <AccountDeactivation />);

        const deactivateLink = screen.getByText('Deactivate my account');
        fireEvent.click(deactivateLink);

        await waitFor(() => {
            const noStuffRadioButton = screen.getByLabelText(
                "I don't have any more stuff to put on Gumtree"
            );
            fireEvent.click(noStuffRadioButton);
            expect(noStuffRadioButton).toBeChecked();
        });
    });

    it('should submit form with correct data when a reason is selected and the form is submitted', async () => {
        renderWithThemeRender(render, <AccountDeactivation />);

        const deactivateLink = screen.getByText('Deactivate my account');
        fireEvent.click(deactivateLink);

        await waitFor(() => {
            const noStuffRadioButton = screen.getByLabelText(
                "I don't have any more stuff to put on Gumtree"
            );
            fireEvent.click(noStuffRadioButton);
        });

        const submitButton = screen.getByRole('button', { name: 'Send deactivation email' });
        fireEvent.click(submitButton);

        await waitFor(() => {
            expect(mockSubmitAccountDeactivation).toHaveBeenCalledTimes(1);
            expect(mockSubmitAccountDeactivation).toHaveBeenCalledWith({
                form: expect.any(FormData),
                method: 'POST',
                url: 'https://mock.seller.url/manage-account/deactivate',
            });
        });
    });
});
