import fetchBuyerData from '@gumtree/web-bff/src/data/buyer/fetch';
import transform from './transform';

export default async (request) => {
    request.headers['Content-Type'] = 'application/json';
    request.headers['x-gt-get-model'] = process.env.GT_GET_MODEL;

    const { headers: requestHeaders, remoteAddress } = request;

    const url = process.env.BUYER_URL;

    const { data, responseHeaders, statusCode } = await fetchBuyerData({
        url,
        requestHeaders,
        remoteAddress,
    });

    return {
        responseHeaders,
        statusCode,
        data: {
            ...transform(data),
        },
    };
};
