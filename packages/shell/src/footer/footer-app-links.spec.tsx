import React from 'react';
import { render, screen } from '@testing-library/react';

import FooterAppLinks from './footer-app-links';

jest.mock('react-redux', () => ({
    useDispatch: () => jest.fn(),
}));

type Props = React.ComponentProps<typeof FooterAppLinks>;

const renderFooterAppLinks = (extraProps = {} as Props) =>
    render(<FooterAppLinks {...extraProps} />);

describe('FooterLinks component', () => {
    it('outputs IOS footer link', () => {
        renderFooterAppLinks({
            sections: [{ items: [{ name: 'ios-app', link: 'ios-link' }, {}] }],
            device: { os: 'IOS' },
            trackAndNavigate: jest.fn(),
        });

        expect(screen.getByTestId('footer-links-item')).toHaveAttribute('href', 'ios-link');
    });

    it('outputs Android footer link', () => {
        renderFooterAppLinks({
            sections: [{ items: [{ name: 'android-app', link: 'android-link' }, {}] }],
            device: { os: 'ANDROID' },
            trackAndNavigate: jest.fn(),
        });

        expect(screen.getByTestId('footer-links-item')).toHaveAttribute('href', 'android-link');
    });

    it('section has title', () => {
        const title = 'Title';
        renderFooterAppLinks({
            sections: [{ title }],
            device: { os: 'IOS' },
            trackAndNavigate: jest.fn(),
        });
        expect(screen.getByText('Title')).toBeInTheDocument();
    });
});
