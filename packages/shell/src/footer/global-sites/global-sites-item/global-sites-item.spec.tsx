import React from 'react';
import { render, screen } from '@testing-library/react';

import GlobalSitesItem from '.';

type Props = React.ComponentProps<typeof GlobalSitesItem>;

const renderGlobalSitesItem = (extraProps = {} as Props) =>
    render(<GlobalSitesItem {...extraProps} />);

describe('GlobalSitesItem component', () => {
    it('has a label', () => {
        const label = 'Marktplaats';
        renderGlobalSitesItem({ label });
        expect(screen.getByText(label)).toBeInTheDocument();
    });

    it('has a link', () => {
        const label = 'Marktplaats';
        const link = 'https://www.marktplaats.nl/';

        const SUT = renderGlobalSitesItem({ label, link });
        expect(SUT.asFragment()).toMatchInlineSnapshot(`
            <DocumentFragment>
              <a
                class="css-cnmggu-global-sites-item e1aojzpg0"
                href="https://www.marktplaats.nl/"
              >
                <span>
                  Marktplaats
                </span>
              </a>
            </DocumentFragment>
        `);
    });
});
