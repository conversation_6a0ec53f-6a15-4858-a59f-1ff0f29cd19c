import React from 'react';
import { render, screen } from '@testing-library/react';

import GlobalSites from '.';

type Props = React.ComponentProps<typeof GlobalSites>;

const renderGlobalSites = (extraProps = {} as Props) => render(<GlobalSites {...extraProps} />);

describe('GlobalSites component', () => {
    it('has a title', () => {
        const title = 'Gumtree Partners';
        renderGlobalSites({ title, items: [] });
        expect(screen.getByText(title)).toBeInTheDocument();
    });

    it('has items', () => {
        const items = [{ id: 1, link: 'https://www.marktplaats.nl/' }, { id: 2, link: 'https://www.marktplaats.nl/' }];
        renderGlobalSites({ title: 'Gumtree Partners', items });
        expect(screen.getAllByRole('link')).toHaveLength(2);
    });
});
