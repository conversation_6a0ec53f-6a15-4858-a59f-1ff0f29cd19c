import type {
    getAppViewDevice,
    getPageTheme,
} from '@gumtree/web-bff/src/render/util/app-view-utils';
import type { AppsType } from '../load-view';
import type { RoutingPageType } from '../route-map';

export interface State {
    /** Set in `preloadedState` (see render-page) */
    type: RoutingPageType;
    title: string;
    description: string;
    App: AppsType;
    appViewDevice: ReturnType<typeof getAppViewDevice>;
    pageTheme: ReturnType<typeof getPageTheme>;
    gbUserPseudoId: string;
}

const initialState = {} as State;

export const UPDATE_PAGE = 'UPDATE_PAGE';

export const updatePage = ({ title, description }: Pick<State, 'title' | 'description'>) => ({
    type: UPDATE_PAGE,
    payload: { title, description },
});

type Action = ReturnType<typeof updatePage>;

export default function pageReducer(state: State = initialState, action: Action) {
    return action.type === UPDATE_PAGE ? { ...state, ...action.payload } : state;
}
