// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Register Modal component renders 1`] = `
<DocumentFragment>
  <div
    class="css-b5q09c-login-modal e1qi3dzf16"
  >
    <h2
      class="css-jbp2bp-login-modal e1qi3dzf18"
    >
      Sign up
    </h2>
    <form
      action="/bff-api/register/via-form-new"
      class="form"
      method="POST"
      name="registration-form"
      novalidate=""
    >
      <div
        class="css-1hyvp5a-login-modal e1qi3dzf17"
      >
        <div
          class="form-element form-element--input"
        >
          <label
            class=""
            for="firstname"
          >
            First name
          </label>
          <input
            autocomplete="off"
            class="input input-text"
            data-testid="input-firstname"
            id="firstname"
            name="form.firstName"
            required=""
            type="text"
            value=""
          />
        </div>
        <div
          class="form-element form-element--input"
        >
          <label
            class=""
            for="lastname"
          >
            Last name
          </label>
          <input
            autocomplete="off"
            class="input input-text"
            data-testid="input-lastname"
            id="lastname"
            name="form.lastName"
            required=""
            type="text"
            value=""
          />
        </div>
      </div>
      <div
        class="form-element form-element--input"
      >
        <label
          class=""
          for="birthdate"
        >
          Date of birth
        </label>
        <input
          autocomplete="off"
          class="input input-text"
          data-testid="input-birthdate"
          id="birthdate"
          name="form.birthdate"
          placeholder="DD/MM/YYYY"
          required=""
          type="text"
          value=""
        />
      </div>
      <div
        class="form-element form-element--input"
      >
        <label
          class=""
          for="postcode"
        >
          Postcode
        </label>
        <input
          autocomplete="off"
          class="input input-text"
          data-testid="input-postcode"
          id="postcode"
          name="form.postcode"
          placeholder="SW1 1AA"
          required=""
          type="text"
          value=""
        />
      </div>
      <div
        class="form-element form-element--input"
      >
        <label
          class=""
          for="username"
        >
          Email address
        </label>
        <input
          autocomplete="off"
          class="input input-email"
          data-testid="input-username"
          id="username"
          name="form.emailAddress"
          required=""
          type="email"
          value=""
        />
      </div>
      <div
        class="css-s28iqn-login-modal e1qi3dzf15"
      >
        <div
          class="form-element form-element--input"
        >
          <label
            class=""
            for="password"
          >
            Password
          </label>
          <input
            autocomplete="off"
            class="input input-password"
            data-testid="input-password"
            id="password"
            name="form.password"
            required=""
            type="password"
            value=""
          />
          <button
            class="reveal-button-show"
            tabindex="0"
            type="button"
          >
            Show
          </button>
        </div>
        <ul
          class="css-jrph4z-login-modal e1qi3dzf13"
        />
      </div>
      <div
        class="css-10hw3pc-login-modal e1qi3dzf14"
      >
        <div
          class="control-group"
        >
          <label
            class="control control-checkbox"
            for="offers"
          >
            We will send you emails regarding our services, offers, competitions and carefully selected partners. These emails will always be sent by us and you can unsubscribe from receiving these marketing emails at any time. 
            <input
              id="offers"
              type="checkbox"
            />
            <div
              class="control_indicator"
            />
          </label>
          <label
            class="control control-checkbox"
            for="terms"
          >
            I agree to the 
            <a
              class="link"
              href="/termsofuse"
              rel="noopener noreferrer"
              target="_blank"
              title=""
            >
              Terms of Use
            </a>
             and 
            <a
              class="link"
              href="/privacy_policy"
              rel="noopener noreferrer"
              target="_blank"
              title=""
            >
              Privacy Notice
            </a>
            <input
              id="terms"
              type="checkbox"
            />
            <div
              class="control_indicator"
            />
          </label>
        </div>
      </div>
      <button
        class="button button--primary css-u2kj4-button-button-button"
        disabled=""
        rel="noopener noreferrer"
        style="display: flex; justify-content: center;"
        type="submit"
      >
        Continue
      </button>
    </form>
  </div>
</DocumentFragment>
`;
