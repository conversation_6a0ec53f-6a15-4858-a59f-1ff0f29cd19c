// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`route maps includes all previously defined routes 1`] = `
[
  {
    "exact": true,
    "package": "favourites",
    "path": "/my-account/favourites",
  },
  {
    "exact": true,
    "package": "saved-searches",
    "path": "/my-account/saved-searches",
  },
  {
    "exact": false,
    "package": "logout",
    "path": "/logout",
  },
  {
    "exact": false,
    "package": "forgotten-password",
    "path": "/forgotten-password",
  },
  {
    "exact": false,
    "package": "manage-account",
    "path": "/manage-account",
  },
  {
    "exact": false,
    "package": "reset-password",
    "path": "/reset-password",
  },
  {
    "exact": false,
    "package": "manage-my-ads",
    "path": "/manage/ads",
  },
  {
    "exact": false,
    "package": "register",
    "path": "/create-account",
  },
  {
    "exact": false,
    "package": "login",
    "path": "/login",
  },
  {
    "exact": false,
    "package": "profile",
    "path": "/profile/account/:accountId",
  },
  {
    "exact": false,
    "package": "message-centre",
    "path": /\\\\/manage\\\\/messages\\(\\.\\*\\)/,
  },
  {
    "exact": false,
    "package": "reply-confirmation",
    "path": /\\\\/reply-confirmation\\\\/\\(\\.\\*\\)/,
  },
  {
    "exact": false,
    "package": "sellerads",
    "path": "/sellerads/:sellerId",
  },
  {
    "exact": false,
    "package": "bump-up",
    "path": "/postad/bumpup",
  },
  {
    "exact": true,
    "package": "srp",
    "path": "/search",
  },
  {
    "exact": true,
    "package": "promote-ad",
    "path": "/postad/promote",
  },
  {
    "exact": false,
    "package": "static-pages",
    "path": /\\\\/termsofuse\\(\\.\\*\\)/,
  },
  {
    "exact": false,
    "package": "static-pages",
    "path": /\\\\/privacy_policy\\(\\.\\*\\)/,
  },
  {
    "exact": true,
    "package": "home",
    "path": "/",
  },
  {
    "exact": true,
    "package": "home",
    "path": "/london",
  },
  {
    "exact": false,
    "package": "recruiter-profile",
    "path": "/jobs/cmp/:company",
  },
  {
    "exact": false,
    "package": "syi",
    "path": /\\\\/postad\\\\/\\(category\\|create\\|edit\\|repost\\)/,
  },
  {
    "exact": true,
    "package": "sell-my-car",
    "path": "/sell-my-car",
  },
  {
    "exact": true,
    "package": "vip",
    "path": "/vip",
  },
  {
    "exact": false,
    "package": "job-pricing",
    "path": "/postad/job-pricing",
  },
  {
    "exact": false,
    "package": "vip",
    "path": "/p/:category/:title/:adId",
  },
  {
    "exact": true,
    "package": "error-404",
    "path": "/error/404",
  },
  {
    "exact": true,
    "package": "cars",
    "path": "/cars-vans-motorbikes/cars",
  },
  {
    "exact": false,
    "package": "srp",
    "path": /\\^\\\\/\\(uk\\|for-sale\\|flats-houses\\|jobs\\|business-services\\|community\\|pets\\|cars-vans-motorbikes\\|all\\)\\(\\\\/\\(\\\\w\\|-\\)\\*\\)\\*/,
  },
  {
    "exact": true,
    "package": "static-pages",
    "path": "/cookies",
  },
  {
    "exact": true,
    "package": "static-pages",
    "path": "/categories",
  },
  {
    "exact": false,
    "package": "static-pages",
    "path": "/categories/:categoryName",
  },
  {
    "exact": true,
    "package": "static-pages",
    "path": "/modern_slavery",
  },
  {
    "exact": true,
    "package": "static-pages",
    "path": "/apps",
  },
]
`;
