import keyword from './keyword-reducer';

jest.mock('../recent-service/recent-service', () => ({
    load: () => [],
}));

describe('keyword reducer', () => {
    it('returns the default state', () => {
        expect(keyword(undefined, {})).toEqual({
            noSuggestions: false,
            selectedSuggestion: undefined,
            suggestions: [],
            value: '',
            useRecentHistory: false,
        });
    });

    it('sets keyword suggestions on SET_SEARCH_BAR_KEYWORD_SUGGESTIONS action', () => {
        const action = {
            type: 'SET_SEARCH_BAR_KEYWORD_SUGGESTIONS',
            suggestions: [1, 2, 3],
        };

        expect(keyword(undefined, action)).toHaveProperty('suggestions', [1, 2, 3]);
    });

    it('sets keyword suggestions on SELECT_SEARCH_BAR_KEYWORD_SUGGESTION action', () => {
        const action = {
            type: 'SELECT_SEARCH_BAR_KEYWORD_SUGGESTION',
            suggestion: 'selected suggestion',
        };

        expect(keyword(undefined, action)).toHaveProperty(
            'selectedSuggestion',
            'selected suggestion'
        );
    });

    it('sets keyword value on SET_SEARCH_BAR_KEYWORD action', () => {
        const action = {
            type: 'SET_SEARCH_BAR_KEYWORD',
            keyword: 'keyword value',
        };

        expect(keyword(undefined, action)).toHaveProperty('value', 'keyword value');
    });

    it('sets keyword value on NO_KEYWORD_SUGGESTIONS action', () => {
        const action = {
            type: 'NO_KEYWORD_SUGGESTIONS',
        };

        expect(keyword(undefined, action)).toHaveProperty('noSuggestions', true);
    });
});
