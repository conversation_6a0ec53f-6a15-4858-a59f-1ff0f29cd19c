import { trackAndNavigate } from '@gumtree/shell/src/common/common-actions';
import { mapDispatch } from './side-menu-item-container';

jest.mock('@gumtree/shell/src/common/common-actions', () => ({
    trackAndNavigate: jest.fn(),
}));

describe('Side Menu Item container', () => {
    it('map dispatch track and navigate', () => {
        const dispatch = jest.fn();
        const event = 'event name';
        const url = 'url.com';
        const eventLabel = 'event label';
        const SUT = mapDispatch(dispatch);
        SUT.trackAndNavigate(event, url, eventLabel);
        expect(dispatch).toHaveBeenCalledWith(trackAndNavigate(event, url, eventLabel));
    });
});
