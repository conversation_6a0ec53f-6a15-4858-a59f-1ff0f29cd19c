import React from 'react';
import { render, screen } from '@testing-library/react';

import SideMenuSection from './side-menu-section';

jest.mock('./side-menu-item-container', () => () => null);
jest.mock('@gumtree/ui-library/src/utils/slug', () => () => null);
jest.mock('@gumtree/ui-library/src/utils/qa-service', () => ({
    qaAttribute: () => null,
}));

const defaultProps = {
    isLoggedIn: false,
    items: [
        {
            eventAction: '',
            eventLabel: '',
            icon: '',
            id: '',
            label: '',
            link: '',
        },
    ],
    onItemClick: jest.fn(),
    title: '',
};

type Props = React.ComponentProps<typeof SideMenuSection>;

const renderSideMenuSection = (extraProps = {} as Props) =>
    render(<SideMenuSection {...defaultProps} {...extraProps} />);

describe('SideMenuSection component', () => {
    it('renders', () => {
        const SUT = renderSideMenuSection();
        expect(SUT.asFragment()).toMatchInlineSnapshot(`
            <DocumentFragment>
              <section
                class="side-menu-section"
              >
                <div
                  class="side-menu-sublist"
                />
              </section>
            </DocumentFragment>
        `);
    });

    it('can have a title in h2 tag', () => {
        const title = 'Section title';
        renderSideMenuSection({ title });

        expect(screen.getByText(title)).toBeInTheDocument();
    });
});
