import { DEFAULT_ERROR_MESSAGE, getErrorMsg } from './social-errors';
import { LoginErrorType } from '../types';

const shareYourEmailError = 'share your email';
const noEmailRegisteredError = "you don't have an email address registered";

describe('get error message', () => {
    it('default error message', () => {
        const errorMessage = getErrorMsg({ appDisplayName: 'Facebook' });

        expect(errorMessage).toBe(DEFAULT_ERROR_MESSAGE);
    });

    it('facebook share email error', () => {
        const errorMessage = getErrorMsg({
            errorType: LoginErrorType.ShareEmailError,
            appDisplayName: 'Facebook',
        });

        expect(errorMessage).toContain('Facebook');
        expect(errorMessage).toContain(shareYourEmailError);
    });

    it('google share email error', () => {
        const errorMessage = getErrorMsg({
            errorType: LoginErrorType.ShareEmailError,
            appDisplayName: 'Google+',
        });

        expect(errorMessage).toContain('Google+');
        expect(errorMessage).toContain(shareYourEmailError);
    });

    it('facebook no email registered', () => {
        const errorMessage = getErrorMsg({
            errorType: LoginErrorType.NoEmailRegistered,
            appDisplayName: 'Facebook',
        });

        expect(errorMessage).toContain('Facebook');
        expect(errorMessage).toContain(noEmailRegisteredError);
    });

    it('google no email registered', () => {
        const errorMessage = getErrorMsg({
            errorType: LoginErrorType.NoEmailRegistered,
            appDisplayName: 'Google+',
        });

        expect(errorMessage).toContain('Google+');
        expect(errorMessage).toContain(noEmailRegisteredError);
    });
});
