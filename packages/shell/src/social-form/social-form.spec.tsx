import React from 'react';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { render, screen, fireEvent } from '@testing-library/react';
import { mocked } from 'jest-mock';
import { useSelector } from 'react-redux';
import useFacebookSignIn from '@gumtree/shell/src/social-form/useFacebookSignIn';
import useGoogleSignIn from '@gumtree/shell/src/social-form/useGoogleSignIn';
import SocialForm from './social-form';

const mockedUseSelector = mocked(useSelector);
const mockedUseFacebookSignIn = mocked(useFacebookSignIn);
const mockedUseGoogleSignIn = mocked(useGoogleSignIn);

jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
}));
jest.mock('@gumtree/shell/src/social-form/useFacebookSignIn');
jest.mock('@gumtree/shell/src/social-form/useGoogleSignIn');

const renderSocialForm = () =>
    render(
        <GoogleOAuthProvider clientId="mockId">
            <SocialForm />
        </GoogleOAuthProvider>
    );

describe('SocialForm', () => {
    beforeEach(() => {
        mockedUseSelector.mockReturnValue({
            facebookAppId: 'mock-facebook-app-id',
        });

        mockedUseFacebookSignIn.mockReturnValue({ errorMessage: '', onLaunch: jest.fn() });
        mockedUseGoogleSignIn.mockReturnValue({
            errorMessage: '',
            onError: jest.fn(),
            onSuccess: jest.fn(),
        });

        window.gumtreeDataLayer = [];
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('renders SocialForm component', () => {
        renderSocialForm();
        expect(screen.getByTestId('social-form-wrapper')).toBeInTheDocument();
    });

    it('renders facebook error', () => {
        mockedUseFacebookSignIn.mockReturnValue({
            errorMessage: 'facebook-error-message',
            onLaunch: jest.fn(),
        });

        const { getByText } = renderSocialForm();

        expect(getByText('facebook-error-message')).toBeInTheDocument();
    });

    it('renders google error', () => {
        mockedUseGoogleSignIn.mockReturnValue({
            errorMessage: 'google-error-message',
            onSuccess: jest.fn(),
            onError: jest.fn(),
        });

        const { getByText } = renderSocialForm();

        expect(getByText('google-error-message')).toBeInTheDocument();
    });

    it('triggers facebook launch on facebook sing in button ', () => {
        const launchFacebook = jest.fn();

        mockedUseFacebookSignIn.mockReturnValue({
            errorMessage: 'facebook-error-message',
            onLaunch: launchFacebook,
        });

        renderSocialForm();

        fireEvent.click(screen.getByTestId('facebook-sign-in-btn'));

        expect(launchFacebook).toHaveBeenCalled();
    });
});
