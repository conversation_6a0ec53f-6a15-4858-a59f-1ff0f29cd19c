import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import Tab from './tab';

describe('Tab component', () => {
    it('renders the tab with the correct attributes and content', () => {
        const onClickMock = jest.fn();
        const title = 'Example Tab';
        const icon = 'example-icon';

        const { getByRole, getByText } = render(
            <Tab isActive onClick={onClickMock} title={title} icon={icon} href="link.com" />
        );

        const tabItem = getByRole('tab');
        const tabLink = getByRole('link');
        const tabTitle = getByText(title);

        expect(tabItem).toHaveAttribute('aria-selected', 'true');
        expect(tabItem).toHaveAttribute('tabIndex', '-1');
        expect(tabItem).toHaveAttribute('id', `${title.toLowerCase().replace(/\s/g, '-')}-tab`);
        expect(tabLink).toHaveAttribute('href', undefined); // Href should be undefined when not provided
        expect(tabTitle).toHaveTextContent(title);
    });

    it('calls the onClick function when the tab is clicked', () => {
        const onClickMock = jest.fn();
        const title = 'Example Tab';
        const icon = 'example-icon';

        const { getByRole } = render(
            <Tab isActive={false} onClick={onClickMock} title={title} icon={icon} />
        );

        const tabItem = getByRole('tab');

        fireEvent.click(tabItem);

        expect(onClickMock).toHaveBeenCalled();
    });
});
