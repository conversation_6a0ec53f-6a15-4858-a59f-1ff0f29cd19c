import appendHighValueBids from './append-high-value-bids';

describe('appendHighValueBids()', () => {
    const createGoogleTagMock = (getTargeting, setTargeting) => ({
        pubads: () => ({
            getSlots: () => [
                {
                    getTargeting,
                    setTargeting,
                },
            ],
        }),
    });

    it('sets hbid to high when bid is 12 or above', () => {
        const bidPrice = ['12'];
        const getTargeting = jest.fn(() => bidPrice);
        const setTargeting = jest.fn();
        const mockGoogleTag = createGoogleTagMock(getTargeting, setTargeting);

        appendHighValueBids(mockGoogleTag);

        expect(getTargeting).toHaveBeenCalledWith('hb_pb');
        expect(setTargeting).toHaveBeenCalledWith('hbid', 'high');
    });

    it('sets hbid to high when bid is way above 12', () => {
        const bidPrice = ['25'];
        const getTargeting = jest.fn(() => bidPrice);
        const setTargeting = jest.fn();
        const mockGoogleTag = createGoogleTagMock(getTargeting, setTargeting);

        appendHighValueBids(mockGoogleTag);

        expect(getTargeting).toHaveBeenCalledWith('hb_pb');
        expect(setTargeting).toHaveBeenCalledWith('hbid', 'high');
    });

    it('sets hbid to 6 when bid is 6', () => {
        const bidPrice = ['6'];
        const getTargeting = jest.fn(() => bidPrice);
        const setTargeting = jest.fn();
        const mockGoogleTag = createGoogleTagMock(getTargeting, setTargeting);

        appendHighValueBids(mockGoogleTag);

        expect(getTargeting).toHaveBeenCalledWith('hb_pb');
        expect(setTargeting).toHaveBeenCalledWith('hbid', '6');
    });

    it('sets hbid to 6 when bid is 6.10', () => {
        const bidPrice = ['6.10'];
        const getTargeting = jest.fn(() => bidPrice);
        const setTargeting = jest.fn();
        const mockGoogleTag = createGoogleTagMock(getTargeting, setTargeting);

        appendHighValueBids(mockGoogleTag);

        expect(getTargeting).toHaveBeenCalledWith('hb_pb');
        expect(setTargeting).toHaveBeenCalledWith('hbid', '6');
    });

    it('sets hbid to 7 when bid is 7.99', () => {
        const bidPrice = ['7.99'];
        const getTargeting = jest.fn(() => bidPrice);
        const setTargeting = jest.fn();
        const mockGoogleTag = createGoogleTagMock(getTargeting, setTargeting);

        appendHighValueBids(mockGoogleTag);

        expect(getTargeting).toHaveBeenCalledWith('hb_pb');
        expect(setTargeting).toHaveBeenCalledWith('hbid', '7');
    });

    it('sets hbid to 11 when bid is 11.99', () => {
        const bidPrice = ['11.99'];
        const getTargeting = jest.fn(() => bidPrice);
        const setTargeting = jest.fn();
        const mockGoogleTag = createGoogleTagMock(getTargeting, setTargeting);

        appendHighValueBids(mockGoogleTag);

        expect(getTargeting).toHaveBeenCalledWith('hb_pb');
        expect(setTargeting).toHaveBeenCalledWith('hbid', '11');
    });

    it('doesn`t call setTargeting if hb_pb returns no value', () => {
        const getTargeting = jest.fn();
        const setTargeting = jest.fn();
        const mockGoogleTag = createGoogleTagMock(getTargeting, setTargeting);

        appendHighValueBids(mockGoogleTag);

        expect(getTargeting).toHaveBeenCalledWith('hb_pb');
        expect(setTargeting).not.toHaveBeenCalled();
    });

    it('doesn`t call setTargeting when bid is 5.99', () => {
        const bidPrice = ['5.99'];
        const getTargeting = jest.fn(() => bidPrice);
        const setTargeting = jest.fn();
        const mockGoogleTag = createGoogleTagMock(getTargeting, setTargeting);

        appendHighValueBids(mockGoogleTag);

        expect(getTargeting).toHaveBeenCalledWith('hb_pb');
        expect(setTargeting).not.toHaveBeenCalled();
    });
});
