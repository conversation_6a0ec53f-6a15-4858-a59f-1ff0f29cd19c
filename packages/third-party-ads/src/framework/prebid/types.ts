import { GoogleTag } from '@gumtree/shared/src/types/advertising/dfp';
import {
    Device,
    L1Category,
    L2Category,
    L3Category,
    PageType,
} from '@gumtree/shared/src/types/client-data';
import { Pbjs } from '@gumtree/shared/src/types/advertising/prebid';
import { ScreenSize } from '@gumtree/shared/src/util/breakpoints-service';

export type CommandFactoryParams = {
    pbjs: Pbjs;
    timeout: number;
    googletag: GoogleTag;
    pageType: PageType;
    deviceType: Device;
    screenSize: ScreenSize;
    categoryL1: L1Category;
    categoryL2: L2Category;
    categoryL3: L3Category;
    keywords: string;
};
