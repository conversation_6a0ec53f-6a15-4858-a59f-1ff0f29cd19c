import { Device, L1Category, PageType } from '@gumtree/shared/src/types/client-data';
import {
    browseFooter,
    carsHomeBanner,
    carsHomeLeaderBoard,
    carsHomeMpu,
    carsHomeStickyBanner,
    fBanner,
    floatingFooter,
    galleryBanner,
    galleryMPU,
    headerBanner,
    homeBanner,
    homeSideAd,
    integratedListing,
    integratedMpu,
    manageAdsLeaderboard,
    mpu,
    mpu2,
    partnershipBottom,
    partnershipWidget,
    partnershipWidget1,
    pixel,
    premiumNative1,
    rSkyB,
    rSkyB2,
    rSkyT,
    rSkyT2,
    SlotConfig,
    tBanner,
    textLink,
    textLinkBase,
    topTakeover,
    vipBanner,
    vipMiddleDesktop,
    vipMiddleMobile,
} from './slot-configs';

export type SlotConfigParams = {
    pageType: PageType;
    resultSize: number;
    device: Device | undefined;
    l1Category: L1Category | undefined;
};

export const resultsPages: PageType[] = ['ResultsSearch', 'ResultsBrowse'];
export const vipPages: PageType[] = ['VIP', 'eVIP', 'pVIP'];

export const buildSlotConfig = ({
    resultSize,
    l1Category,
    device,
    pageType,
}: SlotConfigParams): SlotConfig[] => {
    const hasZeroResults = resultSize === 0;
    const hasLargeResults = resultSize > 14;
    const hasSmallResults = resultSize > 0 && resultSize <= 14;

    const showPremiumNative1 = Boolean(
        l1Category &&
            (
                [
                    'cars-vans-motorbikes',
                    'business-services',
                    'flats-houses',
                    'community',
                    'jobs',
                    'pets',
                ] as L1Category[]
            ).includes(l1Category)
    );

    if (resultsPages.includes(pageType)) {
        const premiumNativeSlots: SlotConfig[] = [...(showPremiumNative1 ? [premiumNative1] : [])];

        if (device === 'mobile') {
            if (hasZeroResults) {
                return [floatingFooter(pageType), pixel];
            } else if (hasSmallResults) {
                return [
                    floatingFooter(pageType),
                    tBanner(pageType),
                    integratedMpu,
                    pixel,
                    ...premiumNativeSlots,
                ];
            } else if (hasLargeResults) {
                return [
                    floatingFooter(pageType),
                    tBanner(pageType),
                    integratedMpu,
                    integratedListing,
                    pixel,
                    ...premiumNativeSlots,
                ];
            }
        }
        if (device === 'tablet') {
            if (hasZeroResults) {
                return [tBanner(pageType), pixel, ...premiumNativeSlots];
            } else if (hasSmallResults) {
                return [
                    tBanner(pageType),
                    integratedMpu,
                    integratedListing,
                    pixel,
                    rSkyT,
                    rSkyB,
                    ...premiumNativeSlots,
                ];
            } else if (hasLargeResults) {
                return [
                    tBanner(pageType),
                    integratedMpu,
                    integratedListing,
                    pixel,
                    rSkyT,
                    rSkyB,
                    ...premiumNativeSlots,
                ];
            }
        }
        if (device === 'desktop') {
            if (hasZeroResults) {
                return [pixel, tBanner(pageType), rSkyT, ...premiumNativeSlots];
            } else if (hasSmallResults) {
                return [
                    integratedMpu,
                    integratedListing,
                    pixel,
                    tBanner(pageType),
                    rSkyT,
                    rSkyT2,
                    rSkyB,
                    rSkyB2,
                    ...premiumNativeSlots,
                ];
            } else if (hasLargeResults) {
                return [
                    integratedMpu,
                    integratedListing,
                    pixel,
                    tBanner(pageType),
                    rSkyT,
                    rSkyT2,
                    rSkyB,
                    rSkyB2,
                    ...premiumNativeSlots,
                ];
            }
        }
    }

    if (vipPages.includes(pageType)) {
        const base = [
            partnershipWidget,
            partnershipWidget1,
            partnershipBottom,
            galleryBanner,
            galleryMPU,
            pixel,
        ];

        if (device === 'mobile') {
            return [...base, floatingFooter(pageType), textLinkBase(pageType), vipMiddleMobile];
        }
        if (device === 'tablet') {
            return [
                ...base,
                vipBanner,
                textLink(pageType, showPremiumNative1),
                mpu(pageType),
                fBanner(pageType),

                vipMiddleDesktop,
            ];
        }
        if (device === 'desktop') {
            return [
                ...base,
                vipBanner,
                textLink(pageType, showPremiumNative1),
                mpu(pageType),
                vipMiddleDesktop,
            ];
        }
    }

    if (pageType === 'Homepage') {
        return [topTakeover, homeSideAd, homeBanner, pixel];
    }

    if (pageType === 'VipGallery') {
        return [galleryBanner];
    }

    if (pageType === 'Article') {
        return [tBanner(pageType), mpu(pageType), fBanner(pageType)];
    }

    if (pageType === 'ResultsSeller' || pageType === 'UserStatic') {
        return [mpu(pageType)];
    }

    if (pageType === 'LandingPageCategory') {
        return [browseFooter];
    }

    if (['Error_404', 'Error_500'].includes(pageType)) {
        return [mpu(pageType)];
    }

    if (pageType === 'R2SEmailSuccess') {
        if (device === 'mobile') {
            return [];
        }
        return [headerBanner];
    }

    if (pageType === 'ManageAds' || pageType === 'ManageAdsPro') {
        return [manageAdsLeaderboard];
    }

    if (pageType === 'PaymentSuccess') {
        return [manageAdsLeaderboard, mpu2];
    }

    if (pageType === 'LandingPageCars') {
        return [carsHomeBanner, carsHomeLeaderBoard, carsHomeMpu, carsHomeStickyBanner];
    }

    return [];
};

export default buildSlotConfig;
