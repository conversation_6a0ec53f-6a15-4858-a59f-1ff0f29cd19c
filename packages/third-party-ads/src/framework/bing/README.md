# Bing ads

>NOTE: implemented as per Bing API v5 documentation

On certain pages and devices we're showing ads we get from Bing.com

- mobile
  - responsive on SRP and VIP page types
  - 1x ad slot (`bing-top`) before our own listings and 1x (`bing-bottom`) after
  - according to current ad configuration provided by back-end we render only the same single ad in each slot duplicated
- tablet responsive on SRP and VIP page types

## Process end-to-end

- user opens SRP / VIP page
- back-end constructs a URL and Bing Ad configuration for front-end, exposes it in the page source in a script tag as `Gum.ads.bing`
- front-end validates the received Bing ad config and only fetches data from `Gum.ads.bing.url` when it exists along with the slot configuration and when it can find the slot containers on the page by given ids (`bing-top` and `bing-bottom`)
- front-end calls the provided URL which is a call to our back-end
- back-end receives the call from the front-end, consumes the URL and calls Bing to get ads based on the received query parameters (category, etc.)
- back-end receives JSON payload from Bing and proxies it back to the front-end
- front-end transforms received JSON extracting only the necessary information to render Bing Text or Product ads
- front-end renders Bing Text or Product ads based on their `_type` property

## Ad configuration

The Bing ad configuration is constructed on the back-end. The configuration contains:

- `url`: a URL to our own back-end to get the necessary JSON data to render Bing ads
- `slots`: an array containing Bing ad slot descriptor objects (`id` and information to decide which ads to render in the particular slot)

The property names in the `slots` objects (`maxTop`, `number`, `numberRepeated`) are not the best and were possibly taken from the `AdSense API`.

There is a fairly simple logic though in `bing/ad-allocator.js` which works out the subset of ads received in the JSON payload to be rendered in a specific slot.

## Architectural overview

### Fetching data and rendering ads

Bing can send us either Text ads or Product ads but (as of yet) cannot send the mixture of both.

The layout we're using to render the bing ads is (and should be) very similar in appearance to our natural listings.

There is a single `bing/renderer.jsx` which is responsible for:

- getting and validating the Bing configuration
- finding the slot containers by id on the page
- fetching data from the given URL
- transforming data on a successful fetch to feed the components to be rendered
- rendering the appropriate ads in each slot container

The `renderer` returns the Promise created by the fetch which is used for testing purposes but can be used for other functionality which needs to be hooked up on the event of finished Bing ad rendering.

The `renderer` differentiate between Text and Product type ads and renders them appropriately. It can handle both at the same time should the Bing JSON payload contain mixed type of ads.

### Text ads

Text ads have the following components:

- no-image thumbnail
- title
- display url
- description

it also may have extensions:

- creative annotation: overrides title and description (rendered on only tablet)
- site links: shows additional links to the ad (rendered on only tablet)
- merchant rating: provides rating in stars and also shows num of ratings (rendered on tablet and mobile)

### Product ads

Product ads have the following components:

- image thumbnail
- title
- offer description (optional)
- seller name
- item description (truncated to 100 chars via CSS)
- sale price (optional)
- original price (shown strike-through when sale price is provided)

Description is not shown on mobile to save screen estate.

We receive the price in GBP and it is shown in `£99` format.
