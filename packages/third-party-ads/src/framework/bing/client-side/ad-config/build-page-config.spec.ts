import { Device, L1Category, PageType } from '@gumtree/shared/src/types/client-data';
import buildBingConfig, { PageConfig, getResultsPageMaxTop } from './build-page-config';
import { BingSlotDivNames } from '../ad-slots';

describe('buildPageConfig', () => {
    describe('is on SRP', () => {
        describe('when there are two slots', () => {
            it('is showing text ad on mobile', () => {
                const inputParameters = {
                    device: 'mobile' as Device,
                    l1Category: 'community' as L1Category,
                    googlePpcVisit: false,
                    pageType: 'ResultsSearch' as PageType,
                };

                const expected: PageConfig = {
                    slots: [
                        { divId: BingSlotDivNames.bingTop, isCarousel: false },
                        { divId: BingSlotDivNames.bingBottom, isCarousel: false },
                    ],
                    number: 1,
                    maxTop: 1,
                };

                expect(buildBingConfig(inputParameters)).toEqual(expected);
            });

            it('is showing carousel on mobile', () => {
                const inputParameters = {
                    device: 'mobile' as Device,
                    l1Category: 'for-sale' as L1Category,
                    googlePpcVisit: false,
                    pageType: 'ResultsSearch' as PageType,
                };

                const expected: PageConfig = {
                    slots: [
                        { divId: BingSlotDivNames.bingTop, isCarousel: true },
                        { divId: BingSlotDivNames.bingBottom, isCarousel: true },
                    ],
                    number: 1,
                    maxTop: 1,
                };

                expect(buildBingConfig(inputParameters)).toEqual(expected);
            });

            it('is showing text ad on tablet', () => {
                const inputParameters = {
                    device: 'tablet' as Device,
                    l1Category: 'for-sale' as L1Category,
                    googlePpcVisit: false,
                    pageType: 'ResultsSearch' as PageType,
                };

                const expected: PageConfig = {
                    slots: [
                        { divId: BingSlotDivNames.bingTop, isCarousel: false },
                        { divId: BingSlotDivNames.bingBottom, isCarousel: false },
                    ],
                    number: 3,
                    maxTop: 1,
                };

                expect(buildBingConfig(inputParameters)).toEqual(expected);
            });

            it('is showing carousel on desktop', () => {
                const inputParameters = {
                    device: 'desktop' as Device,
                    l1Category: 'for-sale' as L1Category,
                    googlePpcVisit: false,
                    pageType: 'ResultsSearch' as PageType,
                };

                const expected: PageConfig = {
                    slots: [
                        { divId: BingSlotDivNames.bingTop, isCarousel: true },
                        { divId: BingSlotDivNames.bingBottom, isCarousel: false },
                    ],
                    number: 3,
                    maxTop: 1,
                };

                expect(buildBingConfig(inputParameters)).toEqual(expected);
            });

            it('should not show integratedListing', () => {
                const inputParameters = {
                    device: 'mobile' as Device,
                    l1Category: 'for-sale' as L1Category,
                    googlePpcVisit: false,
                    pageType: 'ResultsBrowse' as PageType,
                    resultSize: 20,
                };

                const expected: PageConfig = {
                    slots: [
                        { divId: BingSlotDivNames.bingTop, isCarousel: true },
                        { divId: BingSlotDivNames.bingBottom, isCarousel: true },
                    ],
                    number: 1,
                    maxTop: 1,
                };

                expect(buildBingConfig(inputParameters)).toEqual(expected);
            });
        });

        describe('when there is one slot', () => {
            it('googlePpcVisit is true', () => {
                const inputParameters = {
                    device: 'mobile' as Device,
                    l1Category: 'for-sale' as L1Category,
                    googlePpcVisit: true,
                    pageType: 'ResultsSearch' as PageType,
                };

                const expected: PageConfig = {
                    slots: [{ divId: BingSlotDivNames.bingBottom, isCarousel: true }],
                    number: 1,
                    maxTop: 0,
                };

                expect(buildBingConfig(inputParameters)).toEqual(expected);
            });

            it('is showing Premium Native slots', () => {
                const inputParameters = {
                    device: 'mobile' as Device,
                    l1Category: 'flats-houses' as L1Category,
                    googlePpcVisit: true,
                    pageType: 'ResultsSearch' as PageType,
                };

                const expected: PageConfig = {
                    slots: [{ divId: BingSlotDivNames.bingBottom, isCarousel: false }],
                    number: 1,
                    maxTop: 0,
                };

                expect(buildBingConfig(inputParameters)).toEqual(expected);
            });
        });
    });

    describe('is on ZRP', () => {
        it('is showing text ad on desktop', () => {
            const inputParameters = {
                device: 'desktop' as Device,
                l1Category: 'pets' as L1Category,
                googlePpcVisit: false,
                pageType: 'ResultsBrowse' as PageType,
            };

            const expected: PageConfig = {
                slots: [
                    { divId: BingSlotDivNames.bingTop, isCarousel: false },
                    { divId: BingSlotDivNames.bingBottom, isCarousel: false },
                ],
                number: 3,
                maxTop: 1,
            };

            expect(buildBingConfig(inputParameters)).toEqual(expected);
        });

        it('is showing carousel on desktop', () => {
            const inputParameters = {
                device: 'desktop' as Device,
                l1Category: 'for-sale' as L1Category,
                googlePpcVisit: false,
                pageType: 'ResultsBrowse' as PageType,
                resultSize: 0,
            };

            const expected: PageConfig = {
                slots: [
                    { divId: BingSlotDivNames.bingTop, isCarousel: true },
                    { divId: BingSlotDivNames.bingBottom, isCarousel: false },
                ],
                number: 3,
                maxTop: 1,
            };

            expect(buildBingConfig(inputParameters)).toEqual(expected);
        });

        it('is in business-services on mobile', () => {
            const inputParameters = {
                device: 'mobile' as Device,
                l1Category: 'business-services' as L1Category,
                googlePpcVisit: false,
                pageType: 'ResultsSearch' as PageType,
            };

            const expected: PageConfig = {
                slots: [
                    { divId: BingSlotDivNames.bingTop, isCarousel: false },
                    { divId: BingSlotDivNames.bingBottom, isCarousel: false },
                ],
                number: 1,
                maxTop: 1,
            };

            expect(buildBingConfig(inputParameters)).toEqual(expected);
        });

        it('is in cars-vans-motorbikes on tablet', () => {
            const inputParameters = {
                device: 'tablet' as Device,
                l1Category: 'cars-vans-motorbikes' as L1Category,
                googlePpcVisit: false,
                pageType: 'ResultsSearch' as PageType,
            };

            const expected: PageConfig = {
                slots: [
                    { divId: BingSlotDivNames.bingTop, isCarousel: false },
                    { divId: BingSlotDivNames.bingBottom, isCarousel: false },
                ],
                number: 3,
                maxTop: 1,
            };

            expect(buildBingConfig(inputParameters)).toEqual(expected);
        });

        it('is in business-services on desktop', () => {
            const inputParameters = {
                device: 'desktop' as Device,
                l1Category: 'business-services' as L1Category,
                googlePpcVisit: false,
                pageType: 'ResultsSearch' as PageType,
            };

            const expected: PageConfig = {
                slots: [
                    { divId: BingSlotDivNames.bingTop, isCarousel: false },
                    { divId: BingSlotDivNames.bingBottom, isCarousel: false },
                ],
                number: 3,
                maxTop: 1,
            };

            expect(buildBingConfig(inputParameters)).toEqual(expected);
        });

        it('is in cars-vans-motorbikes on desktop', () => {
            const inputParameters = {
                device: 'desktop' as Device,
                l1Category: 'cars-vans-motorbikes' as L1Category,
                googlePpcVisit: false,
                pageType: 'ResultsSearch' as PageType,
            };

            const expected: PageConfig = {
                slots: [
                    { divId: BingSlotDivNames.bingTop, isCarousel: false },
                    { divId: BingSlotDivNames.bingBottom, isCarousel: false },
                ],
                number: 3,
                maxTop: 1,
            };

            expect(buildBingConfig(inputParameters)).toEqual(expected);
        });
    });

    describe('getResultsPageMaxTop', () => {
        it('getResultsPageMaxTop returns ONE if slot is available and experiment is enabled', () => {
            const result = getResultsPageMaxTop({
                activeSlots: [BingSlotDivNames.bingTop],
            });

            expect(result).toBe(1);
        });

        it('getResultsPageMaxTop returns 0 if slot is not available', () => {
            const result = getResultsPageMaxTop({
                activeSlots: [],
            });

            expect(result).toBe(0);
        });
    });
});
