import React from 'react';
import { createRoot } from 'react-dom/client';
import AttributionComponent from './component/attribution';
import { BingSlotAttributionNames } from '../../ad-slots';

export default () => {
    Object.values(BingSlotAttributionNames)
        .map((attributionDivId) => window.document.getElementById(attributionDivId))
        .forEach((attributionEl) => {
            if (attributionEl) {
                const root = createRoot(attributionEl);
                root.render(<AttributionComponent />);
            }
        });
};
