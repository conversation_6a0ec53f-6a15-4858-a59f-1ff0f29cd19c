import React from 'react';
import { render, screen } from '@testing-library/react';
import Rating from './rating';

type Props = React.ComponentProps<typeof Rating>;

const renderRating = (extraProps = {} as Props) => render(<Rating {...extraProps} />);

describe('Bing Listing Rating component', () => {
    it('renders a listing rating element', () => {
        const SUT = renderRating({ domain: '', rating: 7, numberOfRatings: 0 });
        expect(SUT.asFragment()).toMatchInlineSnapshot(`
            <DocumentFragment>
              <div
                class="listing-rating"
              >
                <span>
                   is rated
                </span>
                <span
                  class="listing-rating-stars"
                >
                  <span
                    aria-hidden="true"
                    class="icn-star-fill"
                  />
                  <span
                    aria-hidden="true"
                    class="icn-star-fill"
                  />
                  <span
                    aria-hidden="true"
                    class="icn-star-fill"
                  />
                  <span
                    aria-hidden="true"
                    class="icn-star-fill no-fill"
                  />
                  <span
                    aria-hidden="true"
                    class="icn-star-fill no-fill"
                  />
                </span>
                <span
                  class="listing-rating-count"
                >
                  (0 reviews)
                </span>
              </div>
            </DocumentFragment>
        `);
    });

    it('shows the rated domain', () => {
        renderRating({ domain: 'http://example.com', rating: 0, numberOfRatings: 0 });
        expect(screen.getByText('http://example.com is rated')).toBeInTheDocument();
    });

    it('shows number of ratings', () => {
        renderRating({ numberOfRatings: 1234, domain: '', rating: 0 });
        expect(screen.getByText('(1234 reviews)')).toBeInTheDocument();
    });
});
