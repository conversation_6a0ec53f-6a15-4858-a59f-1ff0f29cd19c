import React from 'react';
import { render } from '@testing-library/react';
import Star from './star';

type Props = React.ComponentProps<typeof Star>;

const renderStar = (extraProps = {} as Props) => render(<Star {...extraProps} />);

describe('Bing Listing Star component', () => {
    // const render = ({ noFill = false } = {}) => shallow(<Star noFill={noFill} />);

    it('renders a filled star icon by default', () => {
        const SUT = renderStar();
        expect(SUT.asFragment()).toMatchInlineSnapshot(`
            <DocumentFragment>
              <span
                aria-hidden="true"
                class="icn-star-fill"
              />
            </DocumentFragment>
        `);
    });

    it('can render a non-filled star icon', () => {
        const SUT = renderStar({ noFill: true });
        expect(SUT.asFragment()).toMatchInlineSnapshot(`
            <DocumentFragment>
              <span
                aria-hidden="true"
                class="icn-star-fill no-fill"
              />
            </DocumentFragment>
        `);
    });
});
