import transformImage from './transform-image';
import { fakeProductAd } from './fake-response-item';

describe('Bing Product Ad image transformer', () => {
    it('extracts image properties from offered item', () => {
        const {
            offer: {
                itemOffered: { image },
            },
        } = fakeProductAd();
        const result = transformImage(image);
        expect(result).toEqual(
            expect.objectContaining({
                url: 'http://example.com/image.png',
                width: 1234,
                height: 5678,
            })
        );
    });
});
