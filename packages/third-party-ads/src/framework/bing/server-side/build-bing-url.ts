import { Platform } from '@gumtree/shared/src/types/platform';
import { L1Category, PageType, Device } from '@gumtree/shared/src/types/client-data';

import {
    buildAdTypesPreference,
    buildAppId,
    buildFilterType,
    encodeKeywords,
} from '../common/url-constructor-util';
import { AdsType, getAdsTypeToShow } from '../common/ad-type-selector';
import { arrayIntersection } from '../../util/array-intersection';

const appendExperiment = ({
    platform,
    abTests,
}: {
    platform: Platform;
    abTests: string[];
}): string[] => {
    const conditionsMapping = [
        {
            platforms: ['ios'],
            abTests: ['GTCO-270-A', 'GTCO-270-B'],
        },
    ];

    const filtered = conditionsMapping.filter(
        (condition) =>
            condition.platforms.includes(platform) &&
            arrayIntersection(abTests, condition.abTests).length
    );
    return filtered.map((condition) => arrayIntersection(abTests, condition.abTests)[0]);
};

const buildAdUnitId = (
    adsType: AdsType,
    platform: Platform,
    pageType: PageType,
    device: Device
) => {
    if (platform === 'android') {
        if (pageType === 'ResultsBrowse' || pageType === 'ResultsSearch') {
            if (adsType === AdsType.ProductWithBackfill) {
                return '11704763'; // gumtree_oo_android_srp_pata_uk (11704763)
            } else {
                return '11649763'; // gumtree_oo_android_srp_uk (11649763)
            }
        } else if (pageType === 'VIP') {
            return '11720126'; // gumtree_ps_vip_android_uk (11720124)
        } else {
            return '11613181'; // gumtree_ps_android_uk (11613181)
        }
    } else if (platform === 'ios') {
        if (pageType === 'ResultsBrowse' || pageType === 'ResultsSearch') {
            if (adsType === AdsType.ProductWithBackfill) {
                return '11704764'; // gumtree_oo_ios_srp_pata_uk (11704764)
            } else {
                return '11649764'; // gumtree_oo_ios_srp_uk (11649764)
            }
        } else if (pageType === 'VIP') {
            return '11720125'; // gumtree_ps_vip_ios_uk (11720125)
        }
    } else if (platform === 'web') {
        if (device === 'mobile') {
            if (adsType === AdsType.ProductWithBackfill) {
                return '11707002';
            } else {
                return '11649760';
            }
        } else if (device === 'tablet') {
            if (adsType === AdsType.ProductWithBackfill) {
                return '11707001';
            } else {
                return '11649762';
            }
        } else if (device === 'desktop') {
            return '11700036';
        }
    }

    return undefined;
};

export const buildBingUrl = (
    keyword: string,
    l1Category: L1Category,
    platform: Platform,
    pageType: PageType,
    device: Device,
    abTests: string[],
    appVersion?: string
): string => {
    const baseUrl = 'https://www.bingapis.com/api/v7/ads/search';

    const adsType: AdsType = getAdsTypeToShow(l1Category);
    const appId = buildAppId(adsType);

    const supportedAdExtensions =
        adsType === AdsType.Text || adsType === AdsType.ProductWithBackfill
            ? encodeURIComponent('SiteLinks,LongAdTitle')
            : '';

    const filterType: string = buildFilterType(adsType);
    const adTypesPreference = buildAdTypesPreference(adsType);

    const buildTracking = (
        l1Category: L1Category,
        pageType: PageType,
        platform: Platform,
        abTests: string[],
        appVersion?: string
    ) => {
        const categoryCode = (l1Category === 'toplevel' ? 'all' : l1Category)?.replace(/ /g, '');
        let pageTypeCode: string = pageType;
        if (pageType === 'ResultsBrowse' || pageType === 'ResultsSearch') {
            pageTypeCode = 'srp';
        }
        const experiments = appendExperiment({
            platform,
            abTests,
        });
        return [platform, 'mobile', pageTypeCode, categoryCode, appVersion, ...experiments]
            .filter(Boolean)
            .join('_');
    };

    const queryParameters = [
        { key: 'appid', value: appId },
        { key: 'propertyId', value: 10278676 },
        { key: 'q', value: encodeKeywords(keyword) },
        { key: 'adUnitId', value: buildAdUnitId(adsType, platform, pageType, device) },
        { key: 'supportedAdExtensions', value: supportedAdExtensions },
        { key: 'mainlineCount', value: 1 },
        { key: 'sidebarCount', value: '0' },
        { key: 'safeSearch', value: 'strict' },
        { key: 'mkt', value: 'en-GB' },
        {
            key: 'tracingTag',
            value: buildTracking(l1Category, pageType, platform, abTests, appVersion),
        },
        { key: 'adTypesFilter', value: filterType },
        { key: 'AdTypesPreference', value: adTypesPreference },
    ];

    return `${baseUrl}?${queryParameters
        .filter(({ value }) => Boolean(value))
        .map(({ key, value }) => `${key}=${value}`)
        .join('&')}`;
};
