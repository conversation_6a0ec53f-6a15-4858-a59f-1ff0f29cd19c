import React from 'react';
import { render, screen } from '@testing-library/react';
import TextAd from './text-ad';

const defaultProps = {
    num: 1,
    url: '',
    title: '',
    displayUrl: '',
    description: '',
    merchantRating: { domain: '', rating: 0, numberOfRatings: 0 },
    siteLinks: [{ text: '', link: '' }],
    TextAds: [],
    callout: '',
    clickToCallNumbers: [],
};

type Props = React.ComponentProps<typeof TextAd>;
const renderTextAd = (extraProps = {} as Props) => render(<TextAd {...extraProps} />);

describe('Bing TextAd component', () => {
    it('renders', () => {
        const SUT = renderTextAd({ ...defaultProps, num: 1234 });
        expect(SUT.asFragment()).toMatchSnapshot();
    });

    it('renders ad title', () => {
        renderTextAd({ ...defaultProps, title: 'ad title' });
        expect(screen.getByText('ad title')).toBeInTheDocument();
    });

    it('renders ad display url', () => {
        renderTextAd({ ...defaultProps, displayUrl: 'http://example.com' });
        expect(screen.getByText('http://example.com')).toBeInTheDocument();
    });

    it('renders ad description', () => {
        renderTextAd({ ...defaultProps, description: 'ad description' });
        expect(screen.getByText('ad description')).toBeInTheDocument();
    });

    it('renders ad rating', () => {
        const merchantRating = {
            domain: 'example.com',
            rating: 7,
            numberOfRatings: 1234,
        };
        renderTextAd({ ...defaultProps, merchantRating });

        expect(screen.getByTestId('domain').textContent).toBe('example.com is rated');
        expect(screen.getByTestId('ratingCount').textContent).toBe('(1234 reviews)');
    });

    it('only renders ad rating when available', () => {
        const merchantRating = {
            domain: '',
            rating: 0,
            numberOfRatings: 0,
        };
        renderTextAd({ ...defaultProps, merchantRating });
        expect(screen.queryByTestId('domain')).toBeNull();
    });

    it('renders the first item in list of site links', () => {
        const siteLinks = [
            { text: 'link 1', link: 'http://example1.com' },
            { text: 'link 2', link: 'http://example2.com' },
        ];
        renderTextAd({ ...defaultProps, siteLinks });
        const link1 = screen.getByText('link 1');

        expect(link1).toBeInTheDocument();
        expect(link1).toHaveAttribute('href', 'http://example1.com');
    });

    it('only renders ad site links when available', () => {
        renderTextAd({ ...defaultProps });
        expect(screen.queryByText('link 1')).toBeNull();
    });

    it('renders ad callout', () => {
        const callout = 'ad callout';
        renderTextAd({ ...defaultProps, callout });

        expect(screen.getByText(callout)).toBeInTheDocument();
    });

    it('only renders ad callout when available', () => {
        renderTextAd({ ...defaultProps });
        expect(screen.queryByText('ad callout')).toBeNull();
    });
});
