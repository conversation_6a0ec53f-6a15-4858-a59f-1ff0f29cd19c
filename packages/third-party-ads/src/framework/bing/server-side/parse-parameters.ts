import { PageType, L1Category } from '@gumtree/shared/src/types/client-data';
import { Platform } from '@gumtree/shared/src/types/platform';
import { parseCategoryHierarchyToCategories } from '@gumtree/shared/src/util/category-hierachy-parser';

export type ServiceParameters = {
    q: string;
    userAgent: string;
    referer: string | undefined;
    categoryHierarchySeoName: string;
    categoryHierarchySeoDisplayName: string;
    platform: Platform;
    pageType: PageType;
    clientIpFromQueryParams?: string;
    clientIpFromHeader?: string;
    experiments?: string;
    appVersion?: string;
};

const parseParameters = (
    parameters: ServiceParameters
): {
    searchKeyword: string;
    userAgent: string;
    referer: string | undefined;
    l1Category: L1Category;
    platform: Platform;
    pageType: PageType;
    clientIp?: string;
    abTests: string[];
    appVersion?: string;
} => {
    const {
        q,
        userAgent,
        referer,
        categoryHierarchySeoName,
        categoryHierarchySeoDisplayName,
        platform,
        pageType,
        clientIpFromQueryParams,
        clientIpFromHeader,
        experiments,
        appVersion,
    } = parameters;

    const categoryHierarchySeoDisplayNameTree = decodeURIComponent(
        categoryHierarchySeoDisplayName
    )?.split('>');
    const leafCategory =
        categoryHierarchySeoDisplayNameTree[categoryHierarchySeoDisplayNameTree.length - 1];
    const searchKeyword = q ? decodeURIComponent(q) : leafCategory;

    const { l1Category } = parseCategoryHierarchyToCategories(categoryHierarchySeoName);

    return {
        searchKeyword: searchKeyword.replace(/[^\w\s]/gi, ''),
        userAgent,
        referer,
        l1Category: l1Category as L1Category,
        platform,
        pageType,
        clientIp: clientIpFromHeader ? clientIpFromHeader : clientIpFromQueryParams,
        abTests: experiments?.replace(/_/g, '-').split(',').filter(Boolean) ?? [],
        appVersion:
            appVersion && appVersion.length ? `ver-${appVersion.replace(/\./g, '-')}` : appVersion,
    };
};

export default parseParameters;
