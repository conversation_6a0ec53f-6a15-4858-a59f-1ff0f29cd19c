const valueMapping: Record<'true' | 'false', string[]> = {
    true: ['A25', 'B25', 'C25', 'D10', 'F5'],
    false: ['E10'],
};

export function bktToIntowowValue(targeting?: { bkt?: string }): 'true' | 'false' | null {
    // Invalid input: warn and fallback to random
    if (!targeting || typeof targeting.bkt !== 'string') {
        console.warn('Invalid input: targeting.');
        return Math.random() < 0.9 ? 'true' : 'false';
    }

    try {
        // Check mappings for match
        for (const [key, bkts] of Object.entries(valueMapping) as ['true' | 'false', string[]][]) {
            if (bkts.includes(targeting.bkt)) {
                return key;
            }
        }

        // No match found
        return null;
    } catch (err) {
        console.error('Error in bktToIntowowValue:', err);
        return null;
    }
}
