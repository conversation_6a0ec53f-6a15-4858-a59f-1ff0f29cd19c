import React from 'react';
import classnames from 'classnames';

import './ad-slot.scss';
import ExternallyPopulatedTag from './externally-populated-tag';

interface AdSlotProps {
    id: string;
    className?: string;
    style?: React.CSSProperties;
}

const getClassName = (className: string): string =>
    classnames(className, {
        'ad-slot': true,
    });

const AdSlot: React.FC<AdSlotProps> = ({ id, className = '' }) => {
    return (
        <div className={getClassName(className)} data-display-ad>
            <ExternallyPopulatedTag tagId={id} />
        </div>
    );
};

export default AdSlot;
