import React, { useEffect, useState } from 'react';
import { TriggerPoint, UserData } from '@gumtree/ui-library/src/phone-verification/constants';
import { Portal } from './components/portal';
import { ConfigProvider } from './providers/config-provider';
import { AuthStatusProvider } from './providers/auth-status-provider';
import { ApiClientsProvider } from './providers/api-clients-provider';
import { AuthModal } from './components/auth-modal';
import { AuthStatus } from './enums/auth-status';
import { phoneVerificationBegin } from './utils/tracking';

export interface PhoneAuthProps {
    basePath: string;
    pageType: string;
    triggerPoint: TriggerPoint;
    onAuthSuccess?: () => void;
    onAuthFailure?: () => void;
    onClose?: () => void;
    userData: UserData;
}

export const PhoneAuth = ({
    userData,
    basePath,
    triggerPoint,
    pageType,
    onClose,
    onAuthFailure,
    onAuthSuccess,
}: PhoneAuthProps) => {
    const [status, setStatus] = useState<AuthStatus>(AuthStatus.InProgress);

    useEffect(() => {
        phoneVerificationBegin(pageType ?? '');
    }, [pageType]);

    useEffect(() => {
        ({
            [AuthStatus.Succeeded]: onAuthSuccess,
            [AuthStatus.Failed]: onAuthFailure,
        }[status]?.());

        if (status !== AuthStatus.InProgress) {
            onClose?.();
        }
    }, [status]);

    return (
        <Portal>
            <ConfigProvider value={{ userData, basePath, triggerPoint }}>
                <ApiClientsProvider>
                    <AuthStatusProvider value={[status, setStatus]}>
                        <AuthModal />
                    </AuthStatusProvider>
                </ApiClientsProvider>
            </ConfigProvider>
        </Portal>
    );
};
