import { trackV2 } from '@gumtree/shared/src/util/track-ga-event';
import { CountryCode } from '../api/clients/phone-auth';

const track: typeof trackV2 = (event, label, others) => {
    try {
        trackV2(event, label, others);
    } catch {
        // do nothing
    }
};

export const phoneVerificationStarted = () => {
    track('PhoneVerificationCTAClick', 'Verify phone number: Continue');
};
export const countryCodeSelected = (countryCode: CountryCode) => {
    track('PhoneVerificationCountryCodeSelected', undefined, countryCode);
};
export const validPhoneNumberEntered = () => {
    track('PhoneVerificationEnterNumberSuccess');
};
export const otpCodeRequested = () => {
    track('PhoneVerificationCTAClick', 'Enter Phone Number: Send code');
};
export const validOtpCodeEntered = () => {
    track('PhoneVerificationEnterCodeSuccess');
};
export const resendCodeRequested = () => {
    track('PhoneVerificationLinkClick', 'Resend code');
};
export const otpCodeConfirmed = () => {
    track('PhoneVerificationAttempt');
};
export const phoneVerificationBegin = (pageType: string) => {
    track('PhoneVerificationBegin', pageType);
};
export const phoneVerificationSucceed = () => {
    track('PhoneVerificationSuccess');
};
export const phoneVerificationFailed = () => {
    track('PhoneVerificationFail');
};
export const tryAgainAfterVerificationFailed = () => {
    track('PhoneVerificationCTAClick', 'Verification failed: Try again');
};
export const goToHelpDeskAfterVerificationFailed = () => {
    track('PhoneVerificationCTAClick', 'Verification failed: Help desk');
};
export const goToHelpDeskAfterAccountSuspended = () => {
    track('PhoneVerificationCTAClick', 'Account suspended: Help Desk');
};
export const invalidPhoneNumberEntered = () => {
    track('PhoneVerificationEnterNumberFail');
};
export const accountSuspendedAfterSendCode = () => {
    track('PhoneVerificationAccountSuspended', '5 phone numbers');
};
export const accountSuspendedAfterConfirmOTP = () => {
    track('PhoneVerificationAccountSuspended', '5 incorrect OTP');
};
export const accountSuspendedAfterResendCode = () => {
    track('PhoneVerificationAccountSuspended', '5 code resend requests');
};
export const invalidOtpCodeEntered = () => {
    track('PhoneVerificationEnterCodeFail');
};
export const getHelpClickedOnPhoneEntryStep = () => {
    track('PhoneVerificationLinkClick', 'Enter phone number: Get help');
};
export const getHelpClickedOnOtpEntryStep = () => {
    track('PhoneVerificationLinkClick', 'Enter your code: Get help');
};

export const phoneVerificationEnterCodeScreenShown = () => {
    track('PhoneVerificationEnterCodeScreenShown');
};
