import React from 'react';
import { useTheme } from '@emotion/react';
import Text from '@gumtree/ui-library/src/typography/text/text';
import { Button } from '@gumtree/ui-library';
import { ModalStep } from '../../../enums/modal-step';
import { useModalStep } from '../../../hooks/use-modal-step';
import { actionButtonCss, StyledHeading } from '../../../app.style';
import { phoneVerificationStarted } from '../../../utils/tracking';
import { useConfig } from '../../../hooks/use-config';
import { texts } from './texts';

export const AuthStartStep = () => {
    const theme = useTheme();
    const { triggerPoint } = useConfig();
    const { actions } = useModalStep();

    const handleOnContinueClick = () => {
        actions.setStep(ModalStep.PhoneEntry);
        phoneVerificationStarted();
    };

    const description = texts[triggerPoint] ?? texts['post ad'] ?? '';

    return (
        <>
            <StyledHeading type="h3" align="left">
                Verify your phone number
            </StyledHeading>
            <Text align="left">{description}</Text>
            <Button
                fullWidth
                display="primary"
                label="Continue"
                onClick={handleOnContinueClick}
                css={actionButtonCss(theme)}
            />
        </>
    );
};
