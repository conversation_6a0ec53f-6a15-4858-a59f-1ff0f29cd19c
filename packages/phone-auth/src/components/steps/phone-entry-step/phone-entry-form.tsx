import React, { SyntheticEvent, useEffect, useLayoutEffect, useState } from 'react';
import { useTheme } from '@emotion/react';
import { Button } from '@gumtree/ui-library';
import { usePhoneNumber } from '../../../hooks/use-phone-number';
import { useModalStep } from '../../../hooks/use-modal-step';
import { actionButtonCss, ErrorMessage } from '../../../app.style';
import { AuthenticationStatus } from '../../../api/clients/phone-auth';
import { useSendPhoneAuthentication } from '../../../hooks/use-send-phone-authentication';
import { IntlTelInput } from '../../intl-tel-input';
import { PhoneLabel } from './phone-entry-step.style';
import { ModalStep } from '../../../enums/modal-step';
import * as tracking from '../../../utils/tracking';
import { validatePhoneNumber } from '../../../utils/validation';

const NetworkErrorMessage = {
    'ERR-400-ALREADY_IN_USE': 'This phone number is connected to another Gumtree account',
    '400': 'Please enter a valid mobile number',
} as const;

export const PhoneEntryForm: React.FC = () => {
    const [, setPhoneNumber] = usePhoneNumber();
    const { actions } = useModalStep();
    const [value, setValue] = useState('');
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [isTouched, setIsTouched] = useState(false);
    const [isValid, setIsValid] = useState(() => validatePhoneNumber(value));
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const sendPhoneAuthenticationMutation = useSendPhoneAuthentication();
    const theme = useTheme();

    useLayoutEffect(() => {
        setPhoneNumber('');
    }, []);

    useEffect(() => {
        errorMessage && tracking.invalidPhoneNumberEntered();
    }, [errorMessage]);

    useEffect(() => {
        isValid && tracking.validPhoneNumberEntered();
    }, [isValid]);

    useEffect(() => {
        setIsValid(validatePhoneNumber(value));
    }, [value]);

    useEffect(() => {
        isTouched &&
            !isValid &&
            isSubmitted &&
            setErrorMessage('Please enter a valid mobile number');
        isValid && setErrorMessage(null);
    }, [isValid, isTouched, isSubmitted]);

    const handlePhoneNumberChange = (_phoneNumber, _selectedCountryCode, fullNumber) => {
        setValue(fullNumber.replace(' ', ''));
    };

    const handlePhoneNumberFocus = () => {
        setIsTouched(true);
    };

    const authenticatePhoneNumber = async (phoneNumber: string) => {
        try {
            const authenticationStatus = await sendPhoneAuthenticationMutation.mutateAsync(
                phoneNumber
            );
            switch (authenticationStatus) {
                case AuthenticationStatus.Blocked:
                    actions.setStep(ModalStep.AccountBlocked);
                    tracking.accountSuspendedAfterSendCode();
                    break;
                case AuthenticationStatus.Verified:
                    actions.setStep(ModalStep.PhoneVerified);
                    break;
                case AuthenticationStatus.Unverified:
                    setPhoneNumber(phoneNumber);
                    actions.setStep(ModalStep.OtpEntry);
                    tracking.otpCodeRequested();
                    break;
                default:
                    actions.setStep(ModalStep.VerificationFailed);
                    break;
            }
        } catch (e) {
            if (e?.isAxiosError && e.response) {
                const { response } = e;
                const errorCode = response.data?.code;
                const errorStatus = response.data?.status ?? response.status;
                const networkErrorMessage =
                    NetworkErrorMessage[errorCode] ?? NetworkErrorMessage[errorStatus];

                networkErrorMessage && setErrorMessage(networkErrorMessage);
                !networkErrorMessage && actions.setStep(ModalStep.VerificationFailed);
                return;
            }
            actions.setStep(ModalStep.SomethingWrong);
        }
    };

    const handleSubmit = (e: SyntheticEvent) => {
        e.preventDefault();
        isValid && authenticatePhoneNumber(value);
        setIsTouched(true);
        setIsSubmitted(true);
    };

    return (
        <form onSubmit={handleSubmit}>
            <PhoneLabel invalid={!!errorMessage} htmlFor="phone-number">
                Mobile number
            </PhoneLabel>
            <IntlTelInput
                autoFocus
                fieldId="phone-number"
                invalid={!!errorMessage}
                onChange={handlePhoneNumberChange}
                onFocus={handlePhoneNumberFocus}
            />
            {errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
            <Button
                fullWidth
                type="submit"
                label="Send code"
                display="primary"
                isLoading={sendPhoneAuthenticationMutation.isLoading}
                loadingText="Sending code"
                css={actionButtonCss(theme)}
            />
        </form>
    );
};
