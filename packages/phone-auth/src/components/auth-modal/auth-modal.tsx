import React, { Fragment, useReducer, useState } from 'react';
import {
    AccountBlockedStep,
    AuthStartStep,
    CheckStatusStep,
    NetworkErrorStep,
    OtpEntryStep,
    PhoneEntryStep,
    PhoneVerifiedStep,
    VerificationFailedStep,
} from '../steps';
import { ModalStep } from '../../enums/modal-step';
import { ModalStepProvider } from '../../providers/modal-step-provider';
import { PhoneNumberProvider } from '../../providers/phone-number-provider';
import { AuthModalDialog } from './auth-modal-dialog';
import { modalStepReducer } from '../../reducers/modal-step-reducer';

export type AuthModalProps = {
    initialStep?: ModalStep;
};

export const AuthModal = ({ initialStep = ModalStep.CheckStatus }: AuthModalProps) => {
    const [state, dispatch] = useReducer(modalStepReducer, { step: initialStep });
    const [phoneNumber, setPhoneNumber] = useState<string>('');

    const steps = {
        [ModalStep.CheckStatus]: CheckStatusStep,
        [ModalStep.SomethingWrong]: NetworkErrorStep,
        [ModalStep.Initial]: AuthStartStep,
        [ModalStep.PhoneEntry]: PhoneEntryStep,
        [ModalStep.OtpEntry]: OtpEntryStep,
        [ModalStep.PhoneVerified]: PhoneVerifiedStep,
        [ModalStep.VerificationFailed]: VerificationFailedStep,
        [ModalStep.AccountBlocked]: AccountBlockedStep,
    };
    const { step } = state;
    const Wrapper = step === ModalStep.CheckStatus ? Fragment : AuthModalDialog;
    const StepComponent = steps[step];

    return (
        <ModalStepProvider value={[state, dispatch]}>
            <PhoneNumberProvider value={[phoneNumber, setPhoneNumber]}>
                <Wrapper>
                    <StepComponent />
                </Wrapper>
            </PhoneNumberProvider>
        </ModalStepProvider>
    );
};
