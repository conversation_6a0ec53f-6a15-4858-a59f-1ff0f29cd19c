import React from 'react';
import { render } from '@testing-library/react';
import WithChildrenVisibilityDetector from './children-visibility-detector';
import * as util from './util'; // Import the module where isChildVisible is to mock it

// Mock the isChildVisible utility function
jest.mock('./util', () => ({
    isChildVisible: jest.fn(),
}));

describe('WithChildrenVisibilityDetector', () => {
    it('correctly reports visible and hidden children', () => {
        // Mock the isChildVisible function to return true for the first child and false for the second
        const mockIsChildVisible = util.isChildVisible as jest.Mock;
        mockIsChildVisible.mockImplementation((_, child) => {
            if (child.textContent === 'Child 1 (Visible)') {
                return true;
            }
            return false;
        });

        const handleLoad = jest.fn();

        render(
            <WithChildrenVisibilityDetector onLoad={handleLoad}>
                <div>Child 1 (Visible)</div>
                <div>Child 2 (Hidden)</div>
            </WithChildrenVisibilityDetector>
        );

        // Check if handleLoad was called once with the correct arguments
        expect(handleLoad).toHaveBeenCalledWith(2, 1); // Assuming 2 total children, 1 of which is hidden

        // Reset mock for other tests
        mockIsChildVisible.mockReset();
    });
});
