export const withTimeout = <T>(promise: Promise<T>, timeoutMs: number): Promise<T> =>
    new Promise((resolve, reject) => {
        // Set up the timeout
        const timeoutId = setTimeout(() => {
            reject(new Error('PROMISE_TIMED_OUT'));
        }, timeoutMs);

        promise.then(
            (value) => {
                clearTimeout(timeoutId); // Clear the timeout upon successful resolution
                resolve(value);
            },
            (error) => {
                clearTimeout(timeoutId); // Clear the timeout upon rejection
                reject(error);
            }
        );
    });

// // Example usage
// const examplePromise = new Promise((resolve) => setTimeout(() => resolve("Result after 2 seconds"), 2000));
//
// withTimeout(examplePromise, 1000)
//   .then((result) => console.log(result))
//   .catch((error) => console.error(error.message)); // This should log "Promise timed out" after 1 second
