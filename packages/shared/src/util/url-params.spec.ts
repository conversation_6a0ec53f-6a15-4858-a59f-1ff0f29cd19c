import { getUrlSearchParamsObjectFromAnyObject, updateUrlWithKeyValue } from './url-params';

describe('getUrlSearchParamsObjectFromAnyObject', () => {
    it('should convert simple object to URLSearchParams', () => {
        const input = {
            a: '1',
            b: 'test',
            c: 'example',
        };
        const result = getUrlSearchParamsObjectFromAnyObject(input);
        expect(result.toString()).toBe('a=1&b=test&c=example');
    });

    it('should handle numbers and boolean values', () => {
        const input = {
            a: 1,
            b: true,
            c: 2.5,
        };
        const result = getUrlSearchParamsObjectFromAnyObject(input);
        expect(result.toString()).toBe('a=1&b=true&c=2.5');
    });

    it('should handle undefined and null values', () => {
        const input = {
            a: undefined,
            b: null,
            c: 'example',
        };
        const result = getUrlSearchParamsObjectFromAnyObject(input);
        expect(result.toString()).toBe('a=undefined&b=null&c=example');
    });

    it('should allow deletion of keys', () => {
        const input = {
            a: '1',
            b: 'test',
            c: 'example',
        };
        const result = getUrlSearchParamsObjectFromAnyObject(input);
        result.delete('b');
        expect(result.toString()).toBe('a=1&c=example');
    });

    it('should allow setting new values for keys', () => {
        const input = {
            a: '1',
            b: 'test',
            c: 'example',
        };
        const result = getUrlSearchParamsObjectFromAnyObject(input);
        result.set('b', 'newTest');
        expect(result.toString()).toBe('a=1&b=newTest&c=example');
    });

    it('should allow setting new keys', () => {
        const input = {
            a: '1',
            b: 'test',
            c: 'example',
        };
        const result = getUrlSearchParamsObjectFromAnyObject(input);
        result.set('d', 'newKey');
        expect(result.toString()).toBe('a=1&b=test&c=example&d=newKey');
    });
});

describe('updateUrlWithKeyValue()', () => {
    it('adds a new key-value pair to a URL without existing query parameters', () => {
        const originalUrl = 'https://www.example.com';
        const key = 'newKey';
        const value = 'newValue';
        const expectedUrl = `https://www.example.com?newKey=newValue`;

        expect(updateUrlWithKeyValue(originalUrl, key, value)).toEqual(expectedUrl);
    });

    it('adds a new key-value pair to a URL with existing query parameters', () => {
        const originalUrl = 'https://www.example.com?a=1&b=2';
        const key = 'newKey';
        const value = 'newValue';
        const expectedUrl = `https://www.example.com?a=1&b=2&newKey=newValue`;

        expect(updateUrlWithKeyValue(originalUrl, key, value)).toEqual(expectedUrl);
    });

    it('it works for relative url', () => {
        const originalUrl = '/search?a=1&b=2';
        const key = 'newKey';
        const value = 'newValue';
        const expectedUrl = `/search?a=1&b=2&newKey=newValue`;

        expect(updateUrlWithKeyValue(originalUrl, key, value)).toEqual(expectedUrl);
    });

    it('updates an existing key-value pair in the URL', () => {
        const originalUrl = 'https://www.example.com?a=1&b=2&updateKey=oldValue';
        const key = 'updateKey';
        const value = 'newValue';
        const expectedUrl = `https://www.example.com?a=1&b=2&updateKey=newValue`;

        expect(updateUrlWithKeyValue(originalUrl, key, value)).toEqual(expectedUrl);
    });

    it('returns undefined for undefined originalUrl input', () => {
        const originalUrl = undefined;
        const key = 'newKey';
        const value = 'newValue';
        const expectedUrl = undefined;

        expect(updateUrlWithKeyValue(originalUrl, key, value)).toEqual(expectedUrl);
    });

    it('returns the original URL for undefined key input', () => {
        const originalUrl = 'https://www.example.com';
        const key = undefined;
        const value = 'newValue';
        const expectedUrl = 'https://www.example.com';

        expect(updateUrlWithKeyValue(originalUrl, key, value)).toEqual(expectedUrl);
    });

    it('returns the original URL for undefined value input', () => {
        const originalUrl = 'https://www.example.com';
        const key = 'newKey';
        const value = undefined;
        const expectedUrl = 'https://www.example.com';

        expect(updateUrlWithKeyValue(originalUrl, key, value)).toEqual(expectedUrl);
    });

    it('handles an original URL that already has an empty target key parameter', () => {
        const originalUrl = 'https://www.example.com?emptyKey=';
        const key = 'emptyKey';
        const value = 'newValue';
        const expectedUrl = 'https://www.example.com?emptyKey=newValue';

        expect(updateUrlWithKeyValue(originalUrl, key, value)).toEqual(expectedUrl);
    });

    it('returns the original URL for both undefined originalUrl and value inputs', () => {
        const originalUrl = undefined;
        const key = 'newKey';
        const value = undefined;
        const expectedUrl = undefined;

        expect(updateUrlWithKeyValue(originalUrl, key, value)).toEqual(expectedUrl);
    });

    it('returns the original URL for empty string key and value inputs', () => {
        const originalUrl = 'https://www.example.com';
        const key = '';
        const value = '';
        const expectedUrl = 'https://www.example.com';

        expect(updateUrlWithKeyValue(originalUrl, key, value)).toEqual(expectedUrl);
    });
});
