import {
    addCategoryToDataLayer,
    addLocationToDataLayer,
    updatePageTypeOnDataLayer,
    getTheLatestDataLayerObject,
    addAdImgCountToDataLayer,
} from './data-layer-service';

describe('Data Layer Service', () => {
    describe('addCategoryToDataLayer', () => {
        it('should add category data to dataLayer when categoryCrumb is provided', () => {
            const dataLayer = [{ a: 'a' }, { b: { b: 'b' } }];
            const categoryCrumb = [
                { id: 2549, seoName: 'for-sale' },
                { id: 2497, seoName: 'baby-kids-stuff' },
                { id: 10579, seoName: 'baby-child-safety' },
                { id: 10580, seoName: 'baby-monitors' },
            ];

            addCategoryToDataLayer(dataLayer, categoryCrumb);

            expect(dataLayer).toEqual([
                { a: 'a' },
                {
                    b: {
                        b: 'b',
                    },
                },
                {
                    c: {
                        c: { id: 10580 },
                        n: 'baby-monitors',
                        l0: { id: 1 },
                        l1: { id: 2549 },
                        l2: { id: 2497 },
                        l3: { id: 10579 },
                        l4: { id: 10580 },
                    },
                },
            ]);
        });

        it('should do nothing when categoryCrumb is an empty array', () => {
            const dataLayer = [{ a: 'a' }, { b: { b: 'b' } }];
            addCategoryToDataLayer(dataLayer, []);

            expect(dataLayer).toEqual(dataLayer);
        });
    });

    describe('addLocationToDataLayer', () => {
        it('should add location data to dataLayer when locationCrumb is provided', () => {
            const dataLayer = [{ a: 'a' }, { b: { b: 'b' } }];
            const locationCrumb = [{ id: 10000393 }, { id: 375 }, { id: 204 }];

            addLocationToDataLayer(dataLayer, locationCrumb);

            expect(dataLayer).toEqual([
                { a: 'a' },
                {
                    b: {
                        b: 'b',
                    },
                },
                {
                    l: {
                        c: { id: 204 },
                        l0: { id: 1 },
                        l1: { id: 10000393 },
                        l2: { id: 375 },
                        l3: { id: 204 },
                    },
                },
            ]);
        });

        it('should do nothing when locationCrumb is an empty array', () => {
            const dataLayer = [{ a: 'a' }, { b: { b: 'b' } }];
            addLocationToDataLayer(dataLayer, []);

            expect(dataLayer).toEqual(dataLayer);
        });
    });

    describe('addAdImgCountToDataLayer', () => {
        it('should add image count data to dataLayer when adImgCount is > 0', () => {
            const dataLayer = [{ x: 'x' }, { y: { y: 'y' } }];
            const adImgCount = 3;

            addAdImgCountToDataLayer(dataLayer, adImgCount);

            expect(dataLayer).toEqual([
                { x: 'x' },
                {
                    y: {
                        y: 'y',
                    },
                },
                { a: { ic: '3' } },
            ]);
        });

        it('should add image count data to dataLayer when adImgCount is 0', () => {
            const dataLayer = [{ x: 'x' }, { y: { y: 'y' } }];
            const adImgCount = 0;

            addAdImgCountToDataLayer(dataLayer, adImgCount);

            expect(dataLayer).toEqual([
                { x: 'x' },
                {
                    y: {
                        y: 'y',
                    },
                },
                { a: { ic: '0' } },
            ]);
        });
    });

    describe('updatePageTypeOnDataLayer', () => {
        it('should update the pageType on dataLayer', () => {
            const dataLayer = [{ a: 'a' }, { b: { b: 'b' } }];
            const pageTypeToUpdate = 'newPageType';
            updatePageTypeOnDataLayer(dataLayer, pageTypeToUpdate);

            expect(dataLayer).toEqual([
                { a: 'a' },
                {
                    b: {
                        b: 'b',
                    },
                },
                {
                    p: {
                        t: 'newPageType',
                    },
                },
            ]);
        });
    });

    describe('getCurrentDataLayerObject()', () => {
        it('should return updated data layer with category and location', () => {
            const dataLayer = [{}, { c: { l1: 1 } }, { l: { l1: 2 } }];
            const updatedDataLayer = getTheLatestDataLayerObject(dataLayer);

            expect(updatedDataLayer).toEqual({
                c: { l1: 1 },
                l: { l1: 2 },
            });
        });

        it('should handle empty dataLayer', () => {
            const dataLayer = [];
            const updatedDataLayer = getTheLatestDataLayerObject(dataLayer);

            expect(updatedDataLayer).toEqual({});
        });
    });
});
