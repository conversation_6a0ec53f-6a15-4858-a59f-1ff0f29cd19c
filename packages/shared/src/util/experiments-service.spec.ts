import { convertBooleanToAB } from './experiments-service';

jest.mock('./data-service', () => ({
    getClientData: jest.fn(),
}));

describe('experimentsService module', () => {
    describe('convertBooleanToAB', () => {
        describe('should return `A` when', () => {
            it('the value is `OFF`', () => {
                expect(convertBooleanToAB('OFF')).toEqual('A');
            });

            it('the value is false', () => {
                expect(convertBooleanToAB(false)).toEqual('A');
            });

            it('the value is `A`', () => {
                expect(convertBooleanToAB('A')).toEqual('A');
            });
        });

        describe('should return `B` when', () => {
            it('the value is `ON`', () => {
                expect(convertBooleanToAB('ON')).toEqual('B');
            });

            it('the value is true', () => {
                expect(convertBooleanToAB(true)).toEqual('B');
            });

            it('the value is `B`', () => {
                expect(convertBooleanToAB('B')).toEqual('B');
            });
        });
    });
});
