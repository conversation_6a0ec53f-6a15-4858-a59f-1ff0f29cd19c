const MS_IN_SEC = 1000;
const MS_IN_MINUTE = MS_IN_SEC * 60;
const MS_IN_HOUR = MS_IN_MINUTE * 60;
const MS_IN_DAY = MS_IN_HOUR * 24;
const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export const calculateTimestamp = (timeString: string | number) => {
    const inputTime = new Date(timeString);
    const now = Date.now();
    const diffDays = (now - Number(inputTime)) / MS_IN_DAY;

    const month = (inputTime.getMonth() + 1).toString().padStart(2, '0');
    const day = inputTime.getDate().toString().padStart(2, '0');
    const hours = inputTime.getHours().toString().padStart(2, '0');
    const minutes = inputTime.getMinutes().toString().padStart(2, '0');
    const hhmm = `${hours}:${minutes}`;
    const roundedData = Math.round(diffDays);

    if (roundedData === 0) {
        return hhmm;
    }
    if (roundedData === 1) {
        return `Yesterday ${hhmm}`;
    }
    if (roundedData > 1 && roundedData < 7) {
        return `${daysOfWeek[inputTime.getDay()]} ${hhmm}`;
    }

    return `${day}/${month}/${inputTime.getFullYear()} ${hhmm}`;
};

export const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const today = new Date(Date.now());

    today.setUTCHours(0, 0, 0, 0);
    date.setUTCHours(0, 0, 0, 0);

    const differenceInTime = today.getTime() - date.getTime();
    const differenceInDays = Math.floor(differenceInTime / (1000 * 60 * 60 * 24));

    if (differenceInDays === 0) {
        return 'Today';
    } else if (differenceInDays === 1) {
        return 'Yesterday';
    } else if (differenceInDays <= 6) {
        return `${differenceInDays} days ago`;
    } else {
        const day = date.getUTCDate();
        const month = date.toLocaleString('en-GB', { month: 'long', timeZone: 'UTC' });
        const weekday = date.toLocaleString('en-GB', { weekday: 'short', timeZone: 'UTC' });

        const ordinalSuffix = (n) => {
            if (n > 3 && n < 21) return 'th';
            switch (n % 10) {
                case 1:
                    return 'st';
                case 2:
                    return 'nd';
                case 3:
                    return 'rd';
                default:
                    return 'th';
            }
        };

        const dayWithSuffix = `${day}${ordinalSuffix(day)}`;
        return `${weekday} ${dayWithSuffix} ${month}`;
    }
};


export const checkThreeDaysAgo = (timestamp) => {
    const threeDaysAgo = Date.now() - 3 * 24 * 60 * 60 * 1000; // 3 days in milliseconds

    if (timestamp && timestamp <= threeDaysAgo) {
        return true;
    } else {
        return false;
    }
};

export const statusTimestampCheck = (timestamp) => {
    const lastActive = new Date(timestamp);
    const currentDate = Date.now();

    // Calculate the difference in days
    const difference = Math.floor((currentDate - lastActive.getTime()) / (1000 * 60 * 60 * 24));

    if (difference === 0) {
        return 'Active today';
    } else if (difference === 1) {
        return 'Active yesterday';
    } else if (difference === 2) {
        return 'Active 2 days ago';
    } else if (difference === 3) {
        return 'Active 3 days ago';
    } else if (difference === 4) {
        return 'Active 4 days ago';
    } else if (difference === 5) {
        return 'Active 5 days ago';
    } else if (difference >= 6 && difference <= 14) {
        return 'Active 1 week ago';
    } else {
        return undefined;
    }
};
