import getHashFromString from './string-hash';

describe('string-hash', () => {
    it('produces a number of the default length (10)', () => {
        const result = getHashFromString('test');
        expect(result.toString()).toHaveLength(10);
    });

    it('produces a string of for a sample session cookie id + search keyword', () => {
        const result = getHashFromString('node0q1agi37m6mfwr0uo3uzcxzi47+this is a search keyword');
        expect(result).toEqual(2328774423);
    });

    it('produces the same output for the same input', () => {
        const input = 'Hello, world!';
        const result1 = getHashFromString(input);
        const result2 = getHashFromString(input);
        expect(result1).toBe(result2);
    });

    it('produces different outputs for different inputs', () => {
        const result1 = getHashFromString('input1');
        const result2 = getHashFromString('input2');
        expect(result1).not.toBe(result2);
    });

    it('handles empty string input', () => {
        const result = getHashFromString('');
        expect(result).toEqual(5381);
    });

    it('produces only a number', () => {
        const result = getHashFromString('test@123!@£$%%^^&*()');
        expect(result).toEqual(1664084520);
    });

    it('handles very long input strings', () => {
        const longInput = 'a'.repeat(10000);
        const result = getHashFromString(longInput);
        expect(result).toEqual(398888453);
    });

    it('produces different outputs for similar inputs', () => {
        const result1 = getHashFromString('password');
        const result2 = getHashFromString('password1');
        expect(result1).not.toBe(result2);
    });
});
