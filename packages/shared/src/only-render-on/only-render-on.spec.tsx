import React from 'react';
import { applyMiddleware, combineReducers, compose, createStore } from 'redux';
import thunk from 'redux-thunk';
import page from '@gumtree/shell/src/reducers/page';
import { Provider } from 'react-redux';
import { render, screen } from '@testing-library/react';
import { AppViewDevices } from '@gumtree/shared/src/constants/app-view';
import OnlyRenderOn from './only-render-on';

describe('OnlyRenderOn', () => {
    const reducers = combineReducers({page});
    const store = (preloadedState) => createStore(reducers, preloadedState, compose(applyMiddleware(thunk)));

    describe('when specifying to render only on app', () => {
        it('should render when viewing via the app', () => {
            const storeData = {
                page: {
                    appViewDevice: AppViewDevices.ANDROID
                }
            };
            
            render(<Provider store={store(storeData)}>
                <OnlyRenderOn app>
                    <div>Hello</div>
                </OnlyRenderOn>
            </Provider>);

            expect(screen.queryByText('Hello')).toBeInTheDocument();
        });

        it('should not render when viewing via the web', () => {
            const storeData = {
                page: {
                    appViewDevice: null
                }
            };
            
            render(<Provider store={store(storeData)}>
                <OnlyRenderOn app>
                    <div>Hello</div>
                </OnlyRenderOn>
            </Provider>);

            expect(screen.queryByText('Hello')).not.toBeInTheDocument();
        });
    });

    describe('when specifying to render only on web', () => {
        it('should render when viewing via the web', () => {
            const storeData = {
                page: {
                    appViewDevice: null
                }
            };
            
            render(<Provider store={store(storeData)}>
                <OnlyRenderOn web>
                    <div>Hello</div>
                </OnlyRenderOn>
            </Provider>);

            expect(screen.queryByText('Hello')).toBeInTheDocument();
        });

        it('should not render when viewing via the app', () => {
            const storeData = {
                page: {
                    appViewDevice: AppViewDevices.IOS
                }
            };
            
            render(<Provider store={store(storeData)}>
                <OnlyRenderOn web>
                    <div>Hello</div>
                </OnlyRenderOn>
            </Provider>);

            expect(screen.queryByText('Hello')).not.toBeInTheDocument();
        });
    });

    describe('should throw errors when', () => {
        beforeEach(() => {
            jest.spyOn(console, "error").mockImplementation(() => {})
        });

        afterEach(() => {
             
            (console.error as jest.Mock).mockRestore();
        });

        it('should throw an error when specifying to render on both app and web', () => {
            const storeData = {};

            const renderSUT = () => {
                render(<Provider store={store(storeData)}>
                    <OnlyRenderOn app web>
                        <div>Hello</div>
                    </OnlyRenderOn>
                </Provider>);
            };
    
            expect(renderSUT).toThrow('You cannot use this component with both app and web properties specified as true.  If this was intentional, you can just remove the use of this component.');
        });
    
        it('should throw an error when specifying to render on neither app or web', () => {
            const storeData = {};

            const renderSUT = () => {
                render(<Provider store={store(storeData)}>
                    <OnlyRenderOn>
                        <div>Hello</div>
                    </OnlyRenderOn>
                </Provider>);
            };
    
            expect(renderSUT).toThrow('You must specify either the app or web property when using this component.');
        });
    });

});
