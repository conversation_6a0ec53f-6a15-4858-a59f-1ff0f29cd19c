// Hexadecimal encoding/decoding
export const hexEncode = (str: string) => {
    return Array.from(str)
        .map((char) => char.charCodeAt(0).toString(16).padStart(2, '0'))
        .join('');
};

export const hexDecode = (hex: string): string => {
    return (
        hex
            .match(/^([0-9A-Fa-f]{2})*$/i)?.[0]
            .match(/.{2}/g)
            ?.map((byte) => String.fromCharCode(parseInt(byte, 16)))
            .join('') ?? ''
    );
};
