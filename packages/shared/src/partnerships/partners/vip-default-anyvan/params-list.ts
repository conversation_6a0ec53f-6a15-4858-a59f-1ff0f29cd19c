import { ParamsList } from '../../validator/shared';

const paramsList: ParamsList = [
    { keyName: 'outcode', isRequired: false },
    { keyName: 'title', isRequired: true },
    { keyName: 'deviceType', isRequired: true },
    { keyName: 'gaPageType', isRequired: true },
    { keyName: 'l1Category', isRequired: true },
    { keyName: 'l2Category', isRequired: false },
    { keyName: 'l3Category', isRequired: false },
    { keyName: 'imageUrl', isRequired: false },
    { keyName: 'subArea', isRequired: false },
];

export default paramsList;
