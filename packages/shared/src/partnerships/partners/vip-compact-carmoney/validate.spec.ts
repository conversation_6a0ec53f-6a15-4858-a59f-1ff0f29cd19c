import validate from './validate';
import { DeviceType, GaPageType, SellerType } from '../../../types/advertising/partnership';

describe('validate', () => {
    it('results true when all params are valid', () => {
        const params = {
            baseUrl: 'https://baseUrlStub',
            deviceType: 'mobile' as DeviceType,
            gaPageType: 'VIP' as GaPageType,
            sellerType: 'Private' as SellerType,
            priceInPence: '123',
            canonicalUrl: 'https://abc',
            mileageValue: '12,000',
        };
        expect(validate(params).isValid).toEqual(true);
    });

    it('results false when deviceType is invalid', () => {
        const params = {
            baseUrl: 'https://baseUrlStub',
            deviceType: 'not_a_device_type' as DeviceType,
            gaPageType: 'VIP' as GaPageType,
            sellerType: 'Private' as SellerType,
            priceInPence: '123',
            canonicalUrl: 'https://abc',
        };

        expect(validate(params).isValid).toEqual(false);
    });

    it('results false when gaPageType is invalid', () => {
        const params = {
            baseUrl: 'https://baseUrlStub',
            deviceType: 'mobile' as DeviceType,
            gaPageType: 'not_a_ga_page_type' as GaPageType,
            sellerType: 'Private' as SellerType,
            priceInPence: '123',
            notRequiredField: undefined,
            canonicalUrl: 'https://abc',
        };

        delete params.sellerType;

        expect(validate(params).isValid).toEqual(false);
    });

    it('results false when sellerType is missing', () => {
        const params = {
            baseUrl: 'https://baseUrlStub',
            deviceType: 'mobile' as DeviceType,
            gaPageType: 'VIP' as GaPageType,
            sellerType: 'Private' as SellerType,
            priceInPence: '123',
            notRequiredField: undefined,
            canonicalUrl: 'https://abc',
        };

        delete params.sellerType;

        expect(validate(params).isValid).toEqual(false);
    });

    it('results false when sellerType is not Private and accountId is in opted out list', () => {
        const params = {
            baseUrl: 'https://baseUrlStub',
            deviceType: 'mobile' as DeviceType,
            gaPageType: 'VIP' as GaPageType,
            sellerType: 'Trade' as SellerType,
            notRequiredField: undefined,
            accountId: '********',
            canonicalUrl: 'https://abc',
        };

        expect(validate(params).isValid).toEqual(false);
    });

    it('results true when sellerType is not Private and accountId is NOT in opted out list', () => {
        const params = {
            baseUrl: 'https://baseUrlStub',
            deviceType: 'mobile' as DeviceType,
            gaPageType: 'VIP' as GaPageType,
            sellerType: 'Trade' as SellerType,
            notRequiredField: undefined,
            accountId: '123',
            canonicalUrl: 'https://abc',
        };

        expect(validate(params).isValid).toEqual(true);
    });
});
