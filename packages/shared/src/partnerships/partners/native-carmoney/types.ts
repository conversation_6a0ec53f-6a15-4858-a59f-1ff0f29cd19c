import type { NativePlatform } from '@gumtree/shared/src/types/platform';

export type Params = {
    category_hierarchy: string | undefined;
    page_type: 'vip';
    price: string | undefined;
    attributes: string | undefined; // e.g. 'encrypted_vrn_zuto:Tl7XuucqasMsgvcZCRyGqSXEzoSgdZ3h/49WksaGHPg=,seller_type:trade';
    native_platform: NativePlatform;
    device_type: 'm' | 't';
    advert_image: string | undefined;
};

export type Transformed = {
    pageType: string;
    deviceOs: NativePlatform;
    deviceSize: 'm' | 't';
    priceInPoundsSterling: number | undefined;
    encryptedVrn: string | undefined;
    campaign: string;
    advertImage: string | undefined;
    l1Category?: string;
    l2Category?: string;
};
