import { PageType } from '../../types/client-data';
import { Platform } from '../../types/platform';
import { GA_ACCOUNT } from './ga-config';

export enum RequestType {
    GET = 'GET',
    POST = 'POST',
}

const buildGetRequest = (queryParameters): string => {
    const baseUrl = 'https://www.google-analytics.com/collect';

    return `${baseUrl}?${queryParameters
        .filter(({ value }) => Boolean(value))
        .map(({ key, value }) => `${key}=${value}`)
        .join('&')}`;
};

export const convertIpAddressToNumericalHash = (ipAddress: string): number => {
    let h = 0;
    const l = ipAddress.length;
    let i = 0;
     
    if (l > 0) while (i < l) h = ((h << 5) - h + ipAddress.charCodeAt(i++)) | 0;
    return Math.abs(h);
};

const generateUuidV4 = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = (Math.random() * 16) | 0;  
        const v = c === 'x' ? r : (r & 0x3) | 0x8;  

        return v.toString(16);
    });
};

const buildGaPingUrl = (parameters: {
    requestType: RequestType;
    platform: Platform;
    pageType: PageType;
    clientIp: string | undefined;
    eventAction: string;
    eventLabel?: string;
    isInteractive: boolean;
}): string | undefined => {
    const { requestType, platform, pageType, eventAction, eventLabel, clientIp, isInteractive } =
        parameters;

    let propertyId;
    if (platform === 'android') {
        propertyId = GA_ACCOUNT.ANDROID;
    } else if (platform === 'ios') {
        propertyId = GA_ACCOUNT.IOS;
    } else {
        propertyId = GA_ACCOUNT.WEB;
    }
    const queryParameters = [
        { key: 'v', value: 1 },
        { key: 'tid', value: propertyId },
        {
            key: 'cid',
            value: clientIp ? convertIpAddressToNumericalHash(clientIp) : generateUuidV4(),
        },
        { key: 'ni', value: isInteractive ? 0 : 1 }, // Non-interaction parameter
        { key: 't', value: 'event' }, // Event hit type
        { key: 'ec', value: pageType }, // Event Category
        { key: 'ea', value: eventAction }, // Event Action
        { key: 'el', value: eventLabel }, // Event Action
    ];

    if (requestType === RequestType.GET) {
        return buildGetRequest(queryParameters);
    }

    return;
};

export default buildGaPingUrl;
