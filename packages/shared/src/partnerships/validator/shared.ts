import { CommonParams } from '../../types/advertising/partnership';

type Params = CommonParams;
export type ParamsList = { keyName: string; isRequired: boolean }[];

export type ValidationResult = {
    isValid: boolean;
    reason?: string;
};

const isSubset = (superset: string[], subset: string[]): boolean =>
    subset.every((val) => superset.includes(val));

const isCommonParamsValid = (params: Params): ValidationResult => {
    if (
        ['mobile', 'tablet', 'desktop'].includes(params.deviceType) &&
        ['VIP', 'ResultsSearch', 'ResultsBrowse'].includes(params.gaPageType)
    ) {
        return { isValid: true };
    } else {
        return {
            isValid: false,
            reason: `Not all required parameters exist. Required: ['mobile', 'tablet', 'desktop'].includes(${params.deviceType}) && ['VIP', 'ResultsSearch', 'ResultsBrowse'].includes(${params.gaPageType}).}`,
        };
    }
};

export const isAllRequiredParamsPresent = (
    params: Params | object,
    paramsList: ParamsList
): ValidationResult => {
    const requiredKeys = paramsList
        .filter(({ isRequired }) => isRequired)
        .map(({ keyName }) => keyName);

    const nonNullKeys = Object.entries(params)
        .filter(([_, value]) => Boolean(value))
        .map(([key, _]) => key);

    if (isSubset(nonNullKeys, requiredKeys)) {
        return { isValid: true };
    } else {
        return {
            isValid: false,
            reason: `Not all required parameters exist. Required: ${JSON.stringify(
                requiredKeys
            )}. Actual: ${JSON.stringify(nonNullKeys)}`,
        };
    }
};

export const doBasicCheck = (params: Params, paramsList: ParamsList): ValidationResult => {
    if (!isCommonParamsValid(params).isValid) {
        return isCommonParamsValid(params);
    }
    if (!isAllRequiredParamsPresent(params, paramsList).isValid) {
        return isAllRequiredParamsPresent(params, paramsList);
    }
    return { isValid: true };
};
