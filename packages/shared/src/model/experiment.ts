export enum ExperimentVariant {
    VARIANT_A = 'A',
    VARIANT_B = 'B',
    VARIANT_C = 'C',
    VARIANT_D = 'D',
}

export const GrowthBookFeature = {
    REVIEW_FEATURE_FLAG: 'GTC-2380',
    UNREAD_FEATURE_FLAG: 'GTC-2379',
    FORGOTTEN_PASSWORD_FLAG: 'GTC-2643',
    G5S: 'G5S-17',
    OPEN_VIP_IN_NEW_TAB: 'GTB-13',
    LOGOUT_TO_BFF: 'GTCON-20',
    EXPIRED_ADS_BANNER: 'GTS-74',
    GALLERY_HAS_MPU_AD: 'GTB-26',
    MCUFEED_CTA_CHANGE: 'GTCON-91',
    DATE_OF_BIRTH_INPUT: 'GTCON-42',
    HIDE_SMS: 'GTCON-274',
    NEW_FEATURES_COPY: 'GTS-142',
    LISTING_CAP_MODAL: 'GTS-105',
    RE<PERSON><PERSON>_NUMBER_LABEL: 'GTCON-331',
    CTA_MESSAGE_CHANGE: 'GTCON-329',
    PETS_CTA_CHANGE: 'GTCON-330',
    POSTCODE_INPUT: 'GTCON-275',
} as const;

export const GrowthBookServerSideFeatures = {
    SRP_RECALL_TRACKING: 'srp_recall_all_cate_flag',
    SRP_RANK_TRACKING: 'srp_rank_relevant_flag',
    PHONE_COLOR_STORAGE_CONDITION: 'srp_filter_mobile_colour_storage_condition',
    PHONE_MODEL: 'srp_filter_mobile_apple_samsung_model',
    PHONE_MODEL_A: 'srp_filter_mobile_model_google_xiaomi_huawei',
    KEYWORD_CORRECTION: 'GTF-2399',
};
