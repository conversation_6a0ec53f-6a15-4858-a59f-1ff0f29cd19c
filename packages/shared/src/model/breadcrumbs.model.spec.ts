 
import { postParseTransform } from './breadcrumbs.model';

describe('postParseTransform()', () => {
    it('appends car brand to car model', () => {
        const vwPoloExample = [
            {
                name: 'Home',
                url: '/',
                location: false,
            },
            {
                name: 'Motors',
                url: '/cars-vans-motorbikes',
                location: false,
            },
            {
                name: 'Cars',
                url: '/cars',
                location: false,
            },
            {
                name: 'Volkswagen',
                url: '/cars/uk/volkswagen',
                location: false,
            },
            {
                name: 'POLO',
                location: false,
            },
        ];

        const result = postParseTransform(vwPoloExample);

        expect(result[result.length - 1].name).toEqual('Volkswagen POLO');
        expect(result).toEqual([
            {
                name: 'Home',
                url: '/',
                location: false,
            },
            {
                name: 'Motors',
                url: '/cars-vans-motorbikes',
                location: false,
            },
            {
                name: 'Cars',
                url: '/cars',
                location: false,
            },
            {
                name: 'Volkswagen',
                url: '/cars/uk/volkswagen',
                location: false,
            },
            {
                name: 'Volkswagen POLO',
                location: false,
            },
        ]);
    });

    it('does not modify non-car categories', () => {
        const iphoneExample = [
            {
                name: 'Home',
                url: '/',
                location: false,
            },
            {
                name: 'For Sale',
                url: '/for-sale',
                location: false,
            },
            {
                name: 'Phones, Mobile Phones & Telecoms',
                url: '/phones',
                location: false,
            },
            {
                name: 'Mobile Phones',
                url: '/mobile-phones',
                location: false,
            },
            {
                name: 'Apple iPhone',
                location: false,
            },
        ];

        const result = postParseTransform(iphoneExample);

        expect(result).toEqual(iphoneExample);
    });

    it('should work if the breadcrumbs are less than 2', () => {
        const breadcrumbs = [
            {
                name: 'Home',
                location: false,
            },
        ];
        const result = postParseTransform(breadcrumbs);

        expect(result).toEqual(breadcrumbs);
    });

    it('should work if the breadcrumbs are empty', () => {
        const breadcrumbs = [];
        const result = postParseTransform(breadcrumbs);

        expect(result).toEqual(breadcrumbs);
    });
});
