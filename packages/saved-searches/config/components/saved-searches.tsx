import React from 'react';
import { css } from '@emotion/react';
import { shallowEqual, useSelector } from 'react-redux';

import { fontSizes, fontWeights, gutterSizes, colors } from '@gumtree/ui-library/src/base/theme';
import { NotificationBox } from '@gumtree/ui-library/src/notification-box/notification-box';
import Icon from '@gumtree/ui-library/src/icon/icon';

import { type SavedSearchesState } from '../../src/reducers';
import SavedSearch from './saved-search';

import './checkbox-on-off.scss';

// 🚧 remove unused CSS

export default function SavedSearches() {
    const {
        csrfToken,
        isDisableAlertPage,
        isDeletedAlertPage,
        savedSearchesCount,
        savedSearches,
        manageAlertsUrl,
        firstName,
    } = useSelector(
        ({ userData, savedSearches }: SavedSearchesState) => ({
            csrfToken: savedSearches.csrfToken,
            firstName: userData.firstName,
            isDeletedAlertPage: savedSearches.isDeletedAlertPage,
            isDisableAlertPage: savedSearches.isDisableAlertPage,
            manageAlertsUrl: savedSearches.manageAlertsUrl,
            savedSearches: savedSearches.items,
            savedSearchesCount: savedSearches.items?.length,
        }),
        shallowEqual
    );

    return (
        <main css={savedSearchesCss}>
            {isDisableAlertPage && (
                <NotificationBox
                    data-q="turned-off-alert"
                    iconType="check"
                    message="This alert has been turned off. If this was a mistake you can turn it back on below."
                />
            )}

            {isDeletedAlertPage && (
                <NotificationBox iconType="bin" message="This alert has been deleted." />
            )}

            {/* {(device.mobile || device.tablet) && (
              [@searchBanner.appBanner 'https://app.adjust.com/ez1o5rv?campaign=In%20Page%20Banner&adgroup=Manage%20Alerts&creative=V1' 'AppDownloadBegin' 'ManageAlerts Banner' '' 'Get search alerts sent straight to your phone' 'top' /]  
            )} */}

            {savedSearchesCount > 0 && !isDisableAlertPage && (
                <div className="header-container">
                    <h1>
                        {`Hi ${firstName}, you have ${savedSearchesCount} search ${
                            savedSearchesCount > 1 ? 'alerts' : 'alert'
                        } saved`}
                    </h1>

                    <a
                        href="/my-account/saved-searches/clear-all"
                        className="delete-all txt-secondary set-right space-mbm"
                        data-q="delete-all"
                    >
                        <Icon type="bin" /> Delete all
                        <span className="hide-visually"> search alerts</span>
                    </a>
                </div>
            )}

            <div className="items-container space-pam space-pbxl">
                {savedSearchesCount > 0 && (
                    <ul data-q="saved-searches" className="managed-list">
                        {savedSearches.map((item, index) => (
                            <SavedSearch
                                key={item.id}
                                csrfToken={csrfToken}
                                index={index}
                                saved={item}
                            />
                        ))}
                    </ul>
                )}

                {savedSearchesCount === 0 && !isDeletedAlertPage && (
                    <div data-q="no-saved-searches">
                        <h2>You don't have any search alerts yet</h2>
                        <p>
                            To set an alert, select the 'Set search alert' button on any search
                            results page
                        </p>
                    </div>
                )}

                {manageAlertsUrl !== '' && (
                    <div className="manage-alerts-link space-pts clearfix">
                        <a href={manageAlertsUrl} className="set-right" data-q="manage-alerts-link">
                            Manage all alerts
                        </a>
                    </div>
                )}
            </div>
        </main>
    );
}

export const savedSearchesCss = css`
    a:hover {
        text-decoration: underline;
    }

    h1 {
        font-size: ${fontSizes.large};
        font-weight: ${fontWeights.normal};
        margin: 0 0 ${gutterSizes.large18};
    }

    .header-container {
        display: flex;
        justify-content: space-between;
        padding: ${gutterSizes.large18} ${gutterSizes.large18} 0 ${gutterSizes.large18};
        border-bottom: 1px solid ${colors.bark20};

        .delete-all {
            color: ${colors.foregroundSubdued};
        }
    }

    .items-container {
        padding: ${gutterSizes.large18} ${gutterSizes.large18} 32px ${gutterSizes.large18};
        border-bottom: 1px solid ${colors.bark20};
    }

    .manage-alerts-link {
        color: ${colors.blue};
        display: flex;
        justify-content: right;
    }
`;
