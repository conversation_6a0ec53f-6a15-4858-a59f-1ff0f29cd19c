import type { SavedSearchesThunk } from '.';
import type { BaseSavedSearchesState } from '../get-data/transform/saved-searches';

export interface State extends BaseSavedSearchesState {
    // 🚧
}

const initialState = {} as State;

const UPDATE_SAVED_SEARCHES = 'UPDATE_SAVED_SEARCHES';

export function updateSavedSearches(payload: BaseSavedSearchesState) {
    return {
        type: UPDATE_SAVED_SEARCHES,
        payload,
    } as const;
}

export const updateSavedSearchThunk: SavedSearchesThunk<
    {
        action: 'on' | 'off';
        csrfToken: string;
        savedSearchId: string;
    },
    Promise<{ action: 'on' | 'off'; success: boolean }>
> =
    ({ action, csrfToken, savedSearchId }) =>
    async (_dispatch, _getState) => {
        try {
            const resp = await fetch(
                `/my-account/saved-searches/${savedSearchId}/alert?emailAlert=${action}`,
                {
                    headers: {
                        accept: 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        X_CSRF_TOKEN: csrfToken,
                    },
                    credentials: 'include',
                }
            );

            if (!resp.ok) {
                throw Error(`status: ${resp.status} (${resp.statusText})`);
            }

            return { action, success: true };
        } catch (e) {
            console.error('updateSavedSearchThunk', e);
            return { action, success: false };
        }
    };

type Action = ReturnType<typeof updateSavedSearches>;

export default function savedSearchesReducer(
    state = initialState,
    { type, payload }: Action
): State {
    switch (type) {
        case UPDATE_SAVED_SEARCHES: {
            return {
                ...state,
                ...payload,
            };
        }
        default:
            return state;
    }
}
