 
import { Handler, NextFunction, Request, Response } from 'express';
import logger from '../logger';

const methodNotAllowedHandler: Handler = (req: Request, res: Response, next: NextFunction) => {
    if (req.method !== 'GET') {
        logger.warn(
            `[method-not-allowed-handler] Method ${req.method} is not allowed. Req path: ${req.path}`
        );
        res.status(405).end();
    }

    next();
};

export default methodNotAllowedHandler;
