import { Request } from 'express';
import { APIError, SystemError } from '../error';
import logger from '../logger';
import { errorHandler, error500PageHandler } from './error-handler';

jest.mock('../logger', () => ({ error: jest.fn() }));
jest.mock('@gumtree/error-500', () => jest.fn());

const ERROR_PAGE_CONTENT = 'hello';

describe('Error handler', () => {
    let res;
    let next;

    const renderError500Page = jest.fn(() => ERROR_PAGE_CONTENT);
    const customResponseHandler = jest.fn();

    beforeEach(() => {
        res = {
            send: jest.fn(() => res),
            end: jest.fn(() => res),
            redirect: jest.fn(() => res),
            write: jest.fn(() => res),
            status: jest.fn(() => res),
        };
        next = jest.fn();
    });
    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('Express error handler', () => {
        it('logs API error', async () => {
            const testError = new APIError('Some message', 503);
            const cookie = 'GTALL-19553:Qg==|GTALL-19548:Qg==';
            await errorHandler(renderError500Page, customResponseHandler)(
                testError,
                { cookies: { gt_exp_ovr: cookie }, path: 'www.a.dummy.path.com' } as Request,
                res,
                next
            );
            expect(logger.error).toHaveBeenCalledWith(
                'Server side error. Error code: 503. Req path: www.a.dummy.path.com',
                'error-handler',
                testError,
                cookie
            );
        });

        it('logs system error', async () => {
            const testError = new SystemError('Some error message', 'ECONNREFUSED');
            const cookie = 'GTALL-19553:Qg==|GTALL-19548:Qg==';
            await errorHandler(renderError500Page, customResponseHandler)(
                testError,
                { cookies: { gt_exp_ovr: cookie }, path: 'www.a.dummy.path.com' } as Request,
                res,
                next
            );
            expect(logger.error).toHaveBeenCalledWith(
                'Server side error. Error code: ECONNREFUSED. Req path: www.a.dummy.path.com',
                'error-handler',
                testError,
                cookie
            );
        });

        it('skip to next handler if error not defined', async () => {
            await errorHandler(renderError500Page, customResponseHandler)(
                null,
                {} as Request,
                res,
                next
            );
            expect(logger.error).not.toHaveBeenCalled();
            expect(res.end).not.toHaveBeenCalled();
            expect(res.redirect).not.toHaveBeenCalled();
            expect(res.write).not.toHaveBeenCalled();
            expect(res.status).not.toHaveBeenCalled();
            expect(next).toHaveBeenCalled();
        });

        it('renders 500 page if headers are not yet sent', async () => {
            res.headersSent = false;
            await errorHandler(renderError500Page, customResponseHandler)(
                new Error(),
                {} as Request,
                res,
                next
            );
            expect(renderError500Page).toHaveBeenCalled();
            expect(customResponseHandler).toHaveBeenCalled();
            expect(res.status).toHaveBeenCalledWith(500);
            expect(res.send).toHaveBeenCalled();
        });

        it('appends redirection script if headers are already sent', async () => {
            res.headersSent = true;
            await errorHandler(renderError500Page, customResponseHandler)(
                new Error(),
                {} as Request,
                res,
                next
            );
            expect(res.write).toHaveBeenCalledWith(
                '<script>window.location="/error/500";</script>'
            );
            expect(res.end).toHaveBeenCalled();
            expect(res.status).not.toHaveBeenCalled();
            expect(res.send).not.toHaveBeenCalledWith();
        });
    });

    describe('Error 500 Page handler', () => {
        it('responds normally', async () => {
            await error500PageHandler(renderError500Page, customResponseHandler)(
                {} as Request,
                res,
                next
            );

            expect(renderError500Page).toHaveBeenCalled();
            expect(customResponseHandler).toHaveBeenCalled();
            expect(logger.error).not.toHaveBeenCalled();
            expect(res.status).toHaveBeenCalled();
            expect(res.send).toHaveBeenCalled();
        });

        it('still responds if error occurs in custom customResponseHandlerWithError()', async () => {
            const customResponseHandlerWithError = jest.fn(() => {
                throw Error();
            });

            await error500PageHandler(renderError500Page, customResponseHandlerWithError)(
                {} as Request,
                res,
                next
            );

            expect(renderError500Page).toHaveBeenCalled();
            expect(customResponseHandler).not.toHaveBeenCalled();
            expect(logger.error).toHaveBeenCalledWith(
                'Server side error - Failed to run customModifier for 500 error page. Error occurs while rendering 500 page.',
                'error-handler',
                Error(),
                undefined
            );
            expect(res.status).toHaveBeenCalled();
            expect(res.send).toHaveBeenCalled();
        });
    });
});
