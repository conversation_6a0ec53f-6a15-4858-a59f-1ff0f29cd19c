import type { KeyedReturnTypes } from '@gumtree/shared/src/model/generic.model';
import commonReducers, { updateCommonData } from '@gumtree/shell/src/reducers/common';
import buyerReducers, { updateBuyerData } from '@gumtree/shell/src/reducers/buyer';
import page, { updatePage } from '@gumtree/shell/src/reducers/page';
import { hideSearchBar } from '@gumtree/shell/src/header/search-bar/search-bar-actions';
import type { Transformed } from '../get-data/transform';
import login, { updateLoginReducer } from './login';

export const showPage =
    ({ title, description, ...data }: Transformed) =>
    (dispatch) => {
        dispatch(hideSearchBar());
        dispatch(updateCommonData(data));
        dispatch(updateBuyerData(data));
        dispatch(updateLoginReducer(data.login));
        dispatch(updatePage({ title, description }));
    };

const index = {
    ...commonReducers,
    ...buyerReducers,
    page,
    login,
};

export default index;

export type LoginState = KeyedReturnTypes<typeof index>;
