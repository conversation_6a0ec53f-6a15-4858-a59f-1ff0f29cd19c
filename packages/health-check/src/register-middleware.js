import checkUrl from './check-url';

const absoluteUrlRegex = /^https?:\/\/.*/;

export default (dependencyUrls = []) => (req, res) => {
    return Promise
        .all(dependencyUrls
        // filter empty urls and non absolute urls
            .filter(url => !!url && absoluteUrlRegex.test(url))
        // checking that dependencies endpoints are ok
            .map(url => checkUrl(url, req))
        )
        .then(() => res.status(200).end())
        .catch(() => res.status(500).end());
};
