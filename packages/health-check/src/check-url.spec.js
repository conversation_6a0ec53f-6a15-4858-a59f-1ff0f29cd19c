import fetch from 'isomorphic-fetch';
import checkUrl from './check-url';

jest.mock('isomorphic-fetch', () => jest.fn());

describe('Health Check check url', () => {
    it('calls the health check url', () => {
        fetch.mockImplementation(() => Promise.resolve({ ok: true }));
        const url = 'http://localhost/test';
        checkUrl(url);
        expect(fetch).toHaveBeenCalledWith(`${url}/internal/healthcheck`);
    });

    it('with 200', () => {
        fetch.mockImplementation(() => Promise.resolve({ ok: true }));

        return expect(checkUrl('/test')).resolves.toEqual(true);
    });

    it('with 404', () => {
        fetch.mockImplementation(() => Promise.resolve({ ok: false }));

        return expect(checkUrl('/test')).rejects.toEqual(new Error('/test check has failed'));
    });

    it('with 500', () => {
        fetch.mockImplementation(() => Promise.reject());

        return expect(checkUrl('/test')).rejects.toEqual(new Error('/test check has failed'));
    });
});
