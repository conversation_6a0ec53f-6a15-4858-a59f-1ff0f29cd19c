import getData from './get-data';
import { showPage } from './reducers';

const thunk = async ({ dispatch, getState }) => {
    const { request } = getState();

    const { data, responseHeaders, statusCode } = await getData(request);

    if (statusCode === 200) {
        dispatch(showPage({ ...data, requestCookies: request.cookies }));
    }

    return { responseHeaders, statusCode };
};

export { thunk, getData };
