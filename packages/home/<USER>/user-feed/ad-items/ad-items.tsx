import React from 'react';

import { useSelector, useDispatch, shallowEqual } from 'react-redux';
import type { ShellUseDispatch } from '@gumtree/shell/src/reducers/common';
import { GridList, HomepageFeedTile } from '@gumtree/ui-library';
import { trackAndNavigate } from '@gumtree/shell/src/common/common-actions';
import { getSaveAdPath, toggleSavedAdThunk } from '@gumtree/shared/src/reducers/saved-ads';
import { trackV2 } from '@gumtree/shared/src/util/track-ga-event';
import { trackGA4PreNav } from '@gumtree/shared/src/util/ga4-shared';
import { PriceObject } from '@gumtree/shared/src/types/home';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { GrowthBookFeature } from '@gumtree/shared/src/model/experiment';
import { getUserFeedClickListingDetails } from '@gumtree/shared/src/util/listing-details';
import { useModal } from '@gumtree/shell/src/use-modal';
import { LoginModalType } from '@gumtree/shell/src/reducers/common/login-modal';

import { L1Category } from '@gumtree/shared/src/types/client-data';
import { RecommendedAds } from '@gumtree/shared/src/model/ad-details.model';

import { formatPrice } from './ad-items-service';

interface AdItem extends Omit<RecommendedAds, 'price' | 'l1Category'> {
    price: PriceObject;
    timeSincePostedUncon: number;
    csrfToken: string;
    imageUrl: string;
    postedDate: string;
    featured: boolean;
    urgent: boolean;
    l1Category: L1Category;
}

interface AdItemsProps {
    ads?: AdItem[];
    children?: unknown;
    spotlightAds?: boolean;
}

const AdItems = ({ ads, children, spotlightAds }: AdItemsProps) => {
    const dispatch = useDispatch() as ShellUseDispatch;

    function selectState({ userSavedAds, baseConfig, userData }) {
        return {
            savedAds: userSavedAds.savedAds,
            buyerUrl: baseConfig.buyerUrl,
            sellerUrl: baseConfig.sellerUrl,
            userData,
        };
    }

    const { userData, savedAds, buyerUrl } = useSelector(selectState, shallowEqual);

    const isOpenInNewTab = useFeatureIsOn(GrowthBookFeature.OPEN_VIP_IN_NEW_TAB);

    const { openDialog } = useModal();

    const openAd =
        (path: string, isSpotlight: boolean, ad: AdItem) => async (event: React.MouseEvent) => {
            event.preventDefault();

            trackGA4PreNav<GA4.ClickListingEvent>({
                event: 'click_listing',
                linkDomain: window.location.host,
                linkURL: path,
                linkText: undefined,
                linkType: 'card',
                linkSection: 'Discover more Good Finds',
                listName: 'Home Page: User Feed',
                clickListingDetails: getUserFeedClickListingDetails(ad),
            });

            if (isOpenInNewTab) {
                window.open(path, '_blank', 'noopener');
            } else {
                dispatch(
                    trackAndNavigate(
                        'Homepage_listing_rec',
                        path,
                        isSpotlight ? 'Spotlight' : 'Standard'
                    )
                );
            }
        };

    const saveAdHandler = (adId: number, token: string, toSaveAd: boolean, ad: AdItem) => {
        trackV2('HomePageCardsSaveAd', toSaveAd ? 'ToSave' : 'ToUnSave');

        const trackEventPayload = {
            listName: 'Home Page',
            clickListingDetails: getUserFeedClickListingDetails(ad),
        };

        dispatch(
            toggleSavedAdThunk({
                advertId: adId,
                token,
                isAlreadySaved: toSaveAd,
                trackEventPayload,
            })
        );
    };

    const loggedOutUserHandler = (adId, token) => {
        trackV2('HomePageCardsSaveAdSignInPromptShown');

        const redirectUrl = `${buyerUrl}${getSaveAdPath('add', adId, token)}`;
        openDialog(undefined, LoginModalType.FAVOURITE, true, redirectUrl);
    };

    return (
        <>
            <GridList data-testid="grid-list" rowItems={1} rowItemsSizes={{ xs: 2, l: 3, xl: 4 }}>
                {ads?.map((ad) => {
                    const {
                        id,
                        title,
                        pictureUrl,
                        path,
                        price,
                        location,
                        timeSincePosted,
                        timeSincePostedUncon,
                        l1Category,
                        spotlight,
                        motorsAttributes,
                        jobsAttributes,
                        propertyAttributes,
                        csrfToken,
                        imageUrl,
                        postedDate,
                        featured,
                        urgent,
                    } = ad;

                    return (
                        <a
                            key={id}
                            href={path}
                            data-testid="ad-link"
                            onClick={openAd(path, spotlightAds || spotlight, ad)}
                        >
                            <HomepageFeedTile
                                adId={id}
                                image={{ src: pictureUrl || imageUrl, alt: title }}
                                isHighlighted={spotlightAds || spotlight}
                                location={location}
                                timeSincePosted={timeSincePosted || postedDate}
                                l1Category={l1Category}
                                title={title}
                                price={
                                    (price && price?.type ? formatPrice(price) : price) as
                                        | string
                                        | undefined
                                }
                                featured={featured}
                                urgent={urgent}
                                saveAdHandler={saveAdHandler}
                                isAdSaved={savedAds[id]}
                                isUserLoggedIn={Boolean(userData?.userLoggedIn) || false}
                                loggedOutUserHandler={loggedOutUserHandler}
                                timeSincePostedUncon={timeSincePostedUncon}
                                motorsAttributes={motorsAttributes}
                                jobsAttributes={jobsAttributes}
                                propertyAttributes={propertyAttributes}
                                token={encodeURIComponent(csrfToken)}
                                isLazyLoadImage
                                ad={ad}
                            />
                        </a>
                    );
                })}
                {children}
            </GridList>
        </>
    );
};

AdItems.defaultProps = {
    ads: [],
    children: null,
    spotlightAds: false,
};

export default AdItems;
