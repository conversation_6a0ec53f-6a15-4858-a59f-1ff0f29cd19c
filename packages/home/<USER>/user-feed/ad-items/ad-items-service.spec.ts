import { formatPrice } from './ad-items-service';

describe('Ad Items Service', () => {
    describe('format Ads price', () => {
        it('fixed price', () => {
            const price = { type: 'FIXED', amount: 4000, currency: 'GBP' };

            const SUT = formatPrice(price);

            expect(SUT).toBe('£40');
        });

        it('fixed price with decimals', () => {
            const price = { type: 'FIXED', amount: 4050, currency: 'GBP' };

            const SUT = formatPrice(price);

            expect(SUT).toBe('£40.50');
        });

        it('free price', () => {
            const price = { type: 'FREE', amount: null, currency: 'GBP' };

            const SUT = formatPrice(price);

            expect(SUT).toBe('£0.00');
        });

        it('weekly price', () => {
            const price = { type: 'WEEKLY', amount: 1000, currency: 'GBP' };

            const SUT = formatPrice(price);

            expect(SUT).toBe('£10 pw');
        });

        it('weekly price with decimals', () => {
            const price = { type: 'WEEKLY', amount: 1099, currency: 'GBP' };

            const SUT = formatPrice(price);

            expect(SUT).toBe('£10.99 pw');
        });

        it('monthly price', () => {
            const price = { type: 'MONTHLY', amount: 1000, currency: 'GBP' };

            const SUT = formatPrice(price);

            expect(SUT).toBe('£10 pcm');
        });

        it('monthly price with decimals', () => {
            const price = { type: 'MONTHLY', amount: 1234, currency: 'GBP' };

            const SUT = formatPrice(price);

            expect(SUT).toBe('£12.34 pcm');
        });

        it('unknown type fallbacks to FIXED', () => {
            const price = { type: 'NONE', amount: 4000, currency: 'GBP' };

            const SUT = formatPrice(price);

            expect(SUT).toBe('£40');
        });

        it('no type fallbacks to FIXED', () => {
            const price = { amount: 4000, currency: 'GBP' };

            // @ts-ignore
            const SUT = formatPrice(price);

            expect(SUT).toBe('£40');
        });

        it('null price and fixed type is not free', () => {
            const price = { type: 'FIXED', amount: null, currency: 'GBP' };

            const SUT = formatPrice(price);

            expect(SUT).toBe(null);
        });

        it('null price and unknown type', () => {
            const price = { type: 'UNKNOWN', amount: null, currency: 'GBP' };

            const SUT = formatPrice(price);

            expect(SUT).toBe(null);
        });

        it('free type with amount present', () => {
            const price = { type: 'FREE', amount: 5000, currency: 'GBP' };

            const SUT = formatPrice(price);

            expect(SUT).toBe('£0.00');
        });

        it('non-GBP currency with fixed amount', () => {
            const price = { type: 'FIXED', amount: 4000, currency: 'USD' };

            const SUT = formatPrice(price);

            expect(SUT).toBe('40');
        });

        it('non-GBP currency with decimals', () => {
            const price = { type: 'FIXED', amount: 4999, currency: 'USD' };

            const SUT = formatPrice(price);

            expect(SUT).toBe('49.99');
        });

        it('non-GBP currency with weekly type', () => {
            const price = { type: 'WEEKLY', amount: 1234, currency: 'USD' };

            const SUT = formatPrice(price);

            expect(SUT).toBe('12.34 pw');
        });

        it('non-GBP currency with null amount', () => {
            const price = { type: 'FIXED', amount: null, currency: 'USD' };

            const SUT = formatPrice(price);

            expect(SUT).toBe(null);
        });

        it('free type with non-GBP currency', () => {
            const price = { type: 'FREE', amount: null, currency: 'USD' };

            const SUT = formatPrice(price);

            expect(SUT).toBe('0.00');
        });
    });
});
