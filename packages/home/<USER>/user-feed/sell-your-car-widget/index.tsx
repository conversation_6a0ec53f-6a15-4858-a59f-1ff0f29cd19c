import React, { useEffect, useState } from 'react';
import 'intersection-observer';
import { useInView } from 'react-intersection-observer';

import { trackV2 } from '@gumtree/shell/src/common/common-actions';
import VrmLookup from '@gumtree/sell-my-car/src/vrm-lookup/vrm-lookup';
import { qaAttribute } from '@gumtree/ui-library/src/utils/qa-service';
import SellYourCarSteps from './sell-your-car-steps';
import { sellYouCarCss } from './index.style';

export default function SellYourCarWidget({ sellerUrl, buyerUrl, bffUrl }: Props) {
    const [ref, inView] = useInView({ threshold: 1 });
    const [trackingSent, setTrackingSent] = useState(false);
    const [hasIntersection, setIntersection] = useState(false);

    useEffect(() => {
        typeof window !== 'undefined' && window.IntersectionObserver && setIntersection(true);
    }, [hasIntersection]);

    useEffect(() => {
        if (!trackingSent && hasIntersection && inView) {
            trackV2('SellerWidgetShown');
            setTrackingSent(true);
        }
    }, [trackingSent, hasIntersection, inView]);

    const baseVrmProps = {
        bffUrl,
        buyerUrl,
        sellerUrl,
        headerTitle: '',
        title: '',
        subtitle: '',
        titleSize: '',
        buttonIcon: '',
        buttonLabel: 'Sell now',
        eventCategory: 'HomePageCarsWidget',
        headerIcon: false,
    };

    return (
        <div
            ref={ref}
            className="sell-your-car-container"
            {...qaAttribute('sell-car-home')}
            css={sellYouCarCss}
        >
            <span className="sell-your-car-image" />
            <div className="sell-your-car-info">
                <h4>Looking to sell your car?</h4>
                <p className="sell-your-car-info-descr">
                    Reach millions of active car buyers on Gumtree
                </p>
                <div className="sell-your-car-content">
                    <SellYourCarSteps />
                    <div className="sell-your-car-mobile-image" />
                </div>
            </div>
            <VrmLookup
                {...baseVrmProps}
                type="lookup"
                eventAction="PostAdBegin"
                placeHolder="Enter reg"
            />
        </div>
    );
}

interface Props {
    bffUrl?: string;
    buyerUrl: string;
    sellerUrl: string;
}
