import styled from '@emotion/styled';
import { fontSizes, colorVariables, fontWeights, colors, gutterSizes } from '@gumtree/ui-library/src/base/theme';
import { AVATAR_SIZE } from './constants';

export const StyledAvatar = styled.div<{ isLink: boolean; size: keyof typeof AVATAR_SIZE }>`
    box-sizing: border-box;
    overflow: hidden;
    width: ${({ size }) => AVATAR_SIZE[size]};
    height: ${({ size }) => AVATAR_SIZE[size]};
    flex-shrink: 0;
    background-color: ${colorVariables.backgroundLight};
    border: ${({ size }) =>
        size === 'small'
            ? `2px solid ${colorVariables.borderDark}`
            : `1px solid ${colorVariables.borderDark}`};
    border-radius: 50%;
    font-size: ${({ size }) => (size === 'xsmall' ? fontSizes.small : fontSizes.header)};
    font-weight: ${fontWeights.bold};
    line-height: calc(${({ size }) => AVATAR_SIZE[size]} - 2px);
    color: ${colorVariables.borderDark};
    text-align: center;
    text-transform: uppercase;

    ${({ isLink }) =>
        isLink &&
        `
    &:hover {
      text-decoration: underline;
    }
  `};
`;

export const StyledLink = styled.a<{ isUserOnline?: boolean }>`
    flex-shrink: 0;
    align-self: flex-start;
    position: relative;

    ${({ isUserOnline }) =>
        isUserOnline &&
        `
    &:after {
      content: " ";
      width: 13px;
      height: 13px;
      border-radius: 50%;
      background: ${colors.fgSuccess};
      display: inline-block;
      position: absolute;
      bottom: 0px;
      right: ${gutterSizes.small};
      border: 2px solid ${colors.white};
    }
  `};
`;
