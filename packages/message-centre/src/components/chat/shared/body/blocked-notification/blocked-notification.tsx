import React, { <PERSON> } from 'react';
import type { User } from '@gumtree/shared/src/types/message-centre';
import { 
  StyledWrapper,
  StyledInlineButton,
  StyledDescription,
  StyledIcon,
  StyledCenteredWrapper,
} from './style';

const BlockedNotification:FC<BlockedNotificationProps> = ({ conversee: { firstName }, onUnblock }) => {
  return (
      <StyledCenteredWrapper>
          <StyledWrapper>
              <StyledIcon type="block icon--color-blue" size="large"/>
              <StyledDescription data-q="blocked-descr">
                  {`You have blocked messages from ${firstName}. If you wish to send ${firstName} a message, click `}
                  <StyledInlineButton onClick={onUnblock}>unblock</StyledInlineButton>
              </StyledDescription>
          </StyledWrapper>
      </StyledCenteredWrapper>
  )
};

interface BlockedNotificationProps {
  conversee: User;
  onUnblock: () => void;
}

export default BlockedNotification;
