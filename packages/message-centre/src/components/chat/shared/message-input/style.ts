import styled from '@emotion/styled';

import { css } from '@emotion/react';
import { TextArea } from '@gumtree/ui-library';
import Camera from '@gumtree/ui-library/src/assets/camera.png';
import {
    breakpoints,
    colorVariables,
    colors,
    fontSizes,
    gutterSizes,
    lineHeights,
    mediaQuery,
} from '@gumtree/ui-library/src/base/theme';
import { MessageTypeEnum } from '@gumtree/shared/src/types/message-centre';

const shouldForwardProp = (p: string) => !['isHidden', 'hasImage', 'backgroundImage'].includes(p);

const imageUploadContainerSize = '96px';
const imageUploadButton = '26px';

export const StyledButtonLabelWrapper = styled.div`
    display: flex;
    gap: ${gutterSizes.small};
`;

export const StyledMobileHiddenText = styled.span`
    ${mediaQuery.until(breakpoints.medium)} {
        display: none;
    } ;
`;

export const StyledWrapper = styled.div<{ messageType: MessageTypeEnum }>`
    padding: 20px;
`;

export const HideVisually = styled.span`
    visibility: hidden;
    font-size: 0;
    padding: 0;
    margin: 0;
`;

export const StyledForm = styled('form', { shouldForwardProp })<{
    hasImage: boolean;
}>`
    display: flex;
    gap: ${gutterSizes.base};

    ${(props) =>
        props.hasImage
            ? `
        align-items: end;
    `
            : `align-items: center;`};

    .upload-container {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: ${gutterSizes.base};

        input {
            display: inline-block;
            width: ${imageUploadContainerSize};
            padding: ${imageUploadContainerSize} 0 0 0;
            height: ${imageUploadContainerSize};
            overflow: hidden;
            box-sizing: border-box;
            background: ${colorVariables.backgroundLight};
            background-image: url('${Camera}');
            background-size: ${imageUploadButton} ${imageUploadButton};
            background-repeat: no-repeat;
            background-position: center;
            border-radius: ${gutterSizes.base};
            cursor: pointer;
        }
        &-outer {
            display: inline-block;
            overflow: hidden;
            box-sizing: border-box;
            background-image: url('${Camera}');
            background-size: ${imageUploadButton} ${imageUploadButton};
            background-repeat: no-repeat;
            background-position: center;

            input {
                width: ${imageUploadButton};
                padding: ${imageUploadButton} 0px 0 0;
                height: ${imageUploadButton};
                background-size: 60px 60px;
                position: relative;
                top: ${gutterSizes.medium};
                bottom: 0;
                cursor: pointer;
                background: none;

                ${mediaQuery.until(breakpoints.medium)} {
                    padding: ${gutterSizes.xxxlarge} 0px 0 0;
                }
            }
        }
    }

    .image-preview {
        display: flex;
        flex-wrap: wrap;
        gap: ${gutterSizes.base};
    }
`;

export const ImageContainer = styled.div`
    position: relative;
    width: ${imageUploadContainerSize};
    height: ${imageUploadContainerSize};

    .button {
        position: absolute;
        padding: 0;
        right: ${gutterSizes.small};
        top: ${gutterSizes.small};
        height: auto;
    }
`;

export const ImagePreview = styled('div', { shouldForwardProp })<{
    backgroundImage: string;
}>`
    ${(props) =>
        props.backgroundImage &&
        `
        background-image: url('${props.backgroundImage}');
        background-position: center center;
        width: ${imageUploadContainerSize};
        height: ${imageUploadContainerSize};
        background-size: cover;
        border-radius: ${gutterSizes.base};
    `};
`;

export const GrowWrap = styled.div`
    display: grid;

    &:after {
        content: attr(data-replicated-value) " ";
        white-space: pre-wrap;
        visibility: hidden;
        border: none;
        padding: ${gutterSizes.large} ${gutterSizes.medium} 0 ${gutterSizes.medium};
        grid-area: 1 / 1 / 2 / 2;
        word-break: break-word;
    }
`;

export const StyledTextArea = styled(TextArea, { shouldForwardProp })<{
    isHidden: boolean;
    hasImage: boolean;
}>`
    min-height: 44px;
    grid-area: 1 / 1 / 2 / 2;
    padding: ${gutterSizes.large} ${gutterSizes.medium} 0 ${gutterSizes.medium};
    box-sizing: border-box;
    overflow: hidden;
    position: relative;
    font-size: ${fontSizes.base};
    line-height: 19px;
    font-family: inherit;
    width: 100%;
    color: ${colors.messageTextColor};
    border: none;
    border-radius: ${gutterSizes.large};

    ${(props) =>
        props.hasImage &&
        `
        height: auto;
        margin-top: ${gutterSizes.medium};
        padding: 0px ${gutterSizes.medium} 0 0;
        line-height: 22px;
        border-radius: 0;
    `};

    &:focus {
        outline: none;
    }

    @media screen and (-webkit-min-device-pixel-ratio: 0) {
        font-size: ${fontSizes.medium};
    }

    ${mediaQuery.until(breakpoints.medium)} {
        min-height: ${gutterSizes.xxxlarge};
        padding: ${gutterSizes.large} ${gutterSizes.medium} 0 ${gutterSizes.medium};

        ${(props) =>
            props.hasImage &&
            `
            padding: ${gutterSizes.large} ${gutterSizes.medium} 0 ${gutterSizes.small};
        `};
    } ;
`;

export const StyledMediaContainer = styled('div', { shouldForwardProp })<{
    hasImage: boolean;
}>`
    border: 1px solid ${colors.newBorder};
    border-radius: ${gutterSizes.large};
    flex: 2;

    ${(props) =>
        props.hasImage &&
        `
        padding: ${gutterSizes.medium};
    `};
`;

export const formButtonCss = (theme, isHidden, disabled) => css`
    && {
        box-sizing: border-box;
        align-self: flex-end;
        justify-content: center;
        width: ${gutterSizes.xxxlarge};
        border-radius: 22px;
        padding: ${gutterSizes.medium};
        background-color: ${theme.palette.primary.main};
        border: 1px solid transparent;
        font-size: ${fontSizes.medium};
        line-height: ${lineHeights.small};
        color: ${theme.palette.primary.mainContrastText};
        cursor: pointer;

        &:hover {
            background-color: ${theme.palette.primary.darkate};
            color: ${theme.palette.primary.darkContrastText};
        }

        ${disabled &&
        `
            background: ${theme.palette.disabled.main};
            pointer-events: none;
            border: 1px solid ${theme.palette.disabled.border};
            color: rgba(0,0,0,.2);
        `}

        ${isHidden && `display: none`};

        ${mediaQuery.until(breakpoints.medium)} {
            height: ${gutterSizes.xxxlarge};
        }

        transition: background-color 0.2s ease-out;
    }
`;

export const StyledJobMessageWrapper = styled.div`
    margin-bottom: ${gutterSizes.large};
`;
