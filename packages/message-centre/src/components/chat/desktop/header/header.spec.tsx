import React from 'react';
import { screen, render } from '@testing-library/react';
import { useSelector, useDispatch } from 'react-redux';
import { mocked } from 'jest-mock';
import { RecursivePartial } from '@gumtree/shared/src/model/generic.model';
import { renderWithThemeRender } from '@gumtree/shared/src/test-utils/mock-theme';
import { MessageTypeEnum } from '@gumtree/shared/src/types/message-centre';
import Header from './header';
import { MessageCentreState } from '../../../../reducers';

const mockedUseSelector = mocked(useSelector);

jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
    useDispatch: jest.fn(),
}));

jest.mock('@gumtree/shared/src/util/track-ga-event', () => ({
    trackV2: jest.fn(),
}));

type Props = React.ComponentProps<typeof Header>;

describe('Header component', () => {
    const props: Props = {
        advert: {
            title: 'rating ad',
            url: 'http://advert.com',
            price: 3300,
            date: '1692628544625',
            messageType: MessageTypeEnum.Gumtree,
            categoryId: 2459,
            img: '',
            status: 'LIVE',
            location: 'Teddington',
        },
        conversee: {
            firstName: 'Cat',
            userId: 125456,
        },
        isConverseeBuyer: false,
    };

    beforeEach(() => {
        useDispatch();

        mockedUseSelector.mockReturnValue({
            canReview: false,
            activeConversationId: '98f35ad5-146e-381c-92e9-e3b0cd1f0801',
            publicProfileId: '1bc123456',
        } as RecursivePartial<MessageCentreState>);
    });

    it('renders', () => {
        const header = renderWithThemeRender(render, <Header {...props} />);
        expect(header.asFragment()).toMatchSnapshot();
    });

    it('renders advert title', () => {
        renderWithThemeRender(render, <Header {...props} />);
        expect(screen.getByText('rating ad')).toBeInTheDocument();
    });

    it('renders advert price', () => {
        renderWithThemeRender(render, <Header {...props} />);
        expect(screen.getByText('£33')).toBeInTheDocument();
    });

    it('renders conversee name', () => {
        renderWithThemeRender(render, <Header {...props} />);
        expect(screen.getByText('Cat')).toBeInTheDocument();
    });

    it('has link to profile of conversee', () => {
        renderWithThemeRender(render, <Header {...props} />);
        expect(screen.getByTestId('avatar')).toHaveAttribute('href', '/profile/account/1bc123456');
    });
});
