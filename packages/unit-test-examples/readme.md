# Unit testing best practices
This folder contains a collection of examples for unit testing with jest.

## Structure of the examples
* async: everything that is asynchronous like promises and timers, etc.
* dependencies: examples for mocking dependencies automatically or manually
* react-component: using jest with react components, finding elements asserting children
* redux: testing connected components actions and reducers

## DOs
* Always mock your module's dependencies either with jest.mock('/file-path') or provide a manual mock as the second argument.
* Always return async expectations.
* Mock fetch using the example in async/fetch-service.spec.js for test readability.
* Always use the mock timers method from jest when using setTimeout, setInterval or any other timing function. See example in async/method-with-timeout.
* Use deep-freeze to check if reducer returns a new reference instead of modifying in place.

## Links
* [Jest mock timers](https://facebook.github.io/jest/docs/en/timer-mocks.html)
* [Jest async tutorials](https://facebook.github.io/jest/docs/en/tutorial-async.html)
* [Jest manual mocks](https://facebook.github.io/jest/docs/en/manual-mocks.html)
