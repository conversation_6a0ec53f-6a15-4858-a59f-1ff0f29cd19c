{"name": "@gumtree/ui-library-v2", "version": "4.1.4", "description": "Gumtree UI Library V2", "license": "UNLICENSED", "main": "src/index.js", "repository": "https://github.com/gumtree-tech/frontend.git", "author": "<PERSON><PERSON><PERSON>", "contributors": [], "scripts": {"storybook": "storybook dev -p 6006", "build": "storybook build", "backstop:reference": "node generate-bitmaps", "backstop:reference:all": "node generate-bitmaps --all", "backstop:test": "node run-backstop-tests", "backstop:test:chromium": "node run-backstop-tests --browser=chromium --docker", "backstop:test:webkit": "node run-backstop-tests --browser=webkit --docker"}, "dependencies": {"@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@material/web": "^2.2.0", "@mui/material": "^6.1.6", "react": "^19.0.0", "react-dom": "^19.0.0", "axios": "1.8.2", "fs-extra": "^9.0.0"}, "devDependencies": {"@babel/core": "7.23.2", "@emotion/jest": "^11.7.1", "@storybook/addon-essentials": "^8.4.1", "@storybook/addon-interactions": "^8.4.1", "@storybook/addon-onboarding": "^8.4.1", "@storybook/addon-styling-webpack": "^1.0.1", "@storybook/addon-themes": "^8.4.1", "@storybook/preview-api": "^8.4.1", "@storybook/addon-webpack5-compiler-swc": "^1.0.5", "@storybook/react": "^8.4.1", "@storybook/react-webpack5": "^8.4.1", "@storybook/test": "^8.4.1", "babel-loader": "^8.1.0", "babel-preset-react-app": "10.0.1", "storybook": "^8.4.1", "sass": "1.32.13", "css-loader": "^5.1.1", "sass-loader": "^10.2.0", "style-loader": "3.3.3", "@svgr/webpack": "^8.1.0", "playwright": "^1.49.1", "backstopjs": "6.3.25"}}