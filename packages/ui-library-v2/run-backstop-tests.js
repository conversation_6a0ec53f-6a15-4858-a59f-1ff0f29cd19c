const backstop = require('backstopjs');
const { getConfigForTestRun } = require('./backstop_data/generate-backstop-config');

const browsers = ['chromium', 'webkit'];

const args = process.argv.slice(2);

const browserArg = args.find((arg) => arg.includes('--browser'));
const storiesBaseUrlArg = args.find((arg) => arg.includes('--storiesBaseUrl'));
const webserverBaseUrlArg = args.find((arg) => arg.includes('--webserverBaseUrl'));
const dockerArg = args.find((arg) => arg.includes('--docker'));

const DEFAULT_STORIES_BASE_URL = 'http://localhost:6006';

const DEFAULT_LOCAL_WEBSERVER_BASE_URL = 'http://localhost:6006';
const DEFAULT_DOCKER_WEBSERVER_BASE_URL = 'http://host.docker.internal:6006';

(async () => {
    const browser = browserArg.replace('--browser=', '');

    let storiesBaseUrl = DEFAULT_STORIES_BASE_URL;
    let webserverBaseUrl = dockerArg ? DEFAULT_DOCKER_WEBSERVER_BASE_URL : DEFAULT_LOCAL_WEBSERVER_BASE_URL;

    if(storiesBaseUrlArg) {
        storiesBaseUrl = storiesBaseUrlArg.replace('--storiesBaseUrl=', '');
    }

    if(webserverBaseUrlArg) {
        webserverBaseUrl = webserverBaseUrlArg.replace('--webserverBaseUrl=', '');
    }

    if (!browsers.includes(browser)) {
        console.error(
            `${browser} is not a valid browser.  Please start this NPM script with a --browser:chromium or --browser:webkit flag.`
        );
        process.exit(1);
    }

    const config = await getConfigForTestRun(browser, storiesBaseUrl, webserverBaseUrl);

    await backstop('test', { config, docker: !!dockerArg });
})();
