const backstop = require('backstopjs');
const fsExtra = require('fs-extra');
const { getConfigForReferenceCreation } = require('./backstop_data/generate-backstop-config');

const browsers = ['webkit', 'chromium'];

const args = process.argv.slice(2);
const generateAll = args.find((arg) => arg.includes('--all'));

const DEFAULT_STORIES_BASE_URL = 'http://localhost:6006';
const DEFAULT_WEBSERVER_BASE_URL = 'http://host.docker.internal:6006';

const printUpdateRequiredInformation = (scenariosLength, browser) => {
    if (scenariosLength === 0) {
        console.log(`\x1b[42mNo scenario updates needed for ${browser}\x1b[0m`);
    } else {
        console.log(`\x1b[43m${scenariosLength} scenario updates needed for ${browser}\x1b[0m`);
    }
};

const backupOriginalImages = async (referencePath, tempPath) => {
    if (fsExtra.existsSync(referencePath)) {
        await fsExtra.move(referencePath, tempPath);
    }
};

const restoreOriginalImages = async (referencePath, tempPath) => {
    if (fsExtra.existsSync(tempPath)) {
        await fsExtra.copy(tempPath, referencePath);
        await fsExtra.remove(tempPath);
    }
};

(async () => {
    for await (const browser of browsers) {
        const referencePath = `backstop_data/bitmaps_reference_${browser}`;
        const tempPath = `${referencePath}_temp`;

        const config = await getConfigForReferenceCreation(browser, referencePath, generateAll, DEFAULT_STORIES_BASE_URL, DEFAULT_WEBSERVER_BASE_URL);

        printUpdateRequiredInformation(config.scenarios.length, browser);

        if (config.scenarios.length === 0) {
            continue;
        }

        !generateAll && (await backupOriginalImages(referencePath, tempPath));

        try {
            await backstop('reference', { config, docker: true });
        } finally {
            !generateAll && (await restoreOriginalImages(referencePath, tempPath));
            await new Promise(promise => setTimeout(promise, 5000));
        }
    }
})();
