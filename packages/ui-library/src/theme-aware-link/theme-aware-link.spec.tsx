import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';

import ThemeAwareLink from './theme-aware-link';

jest.mock('@emotion/react', () => ({
    ...jest.requireActual('@emotion/react'),
    useTheme: () => {
        const { modernTheme } = require('@gumtree/ui-library/src/base/themes/modern-theme');
        return modernTheme;
    },
}));

describe('theme-aware-link', () => {
    it('should render, with populated props', () => {
        const rendered = render(
            <ThemeAwareLink label="test label" href="http://testing.com" type="primary" />
        );
        expect(rendered.asFragment()).toMatchSnapshot();
    });

    describe('Once clicked, it should', () => {
        beforeEach(() => {
            const mockResponse = jest.fn();

            Object.defineProperty(window, 'location', {
                value: {
                    hash: {
                        endsWith: mockResponse,
                        includes: mockResponse,
                    },
                    assign: mockResponse,
                },
                writable: true,
            });
        });

        it('execute any click handler', () => {
            const mockClickHandler = jest.fn();

            render(
                <ThemeAwareLink
                    label="test label"
                    onLinkClicked={mockClickHandler}
                    href="http://testing.com"
                    type="primary"
                />
            );

            const link = screen.getByRole('link');

            act(() => {
                fireEvent.click(link);
            });

            expect(mockClickHandler).toHaveBeenCalled();
        });

        it('navigate to the new link', () => {
            render(<ThemeAwareLink label="test label" href="http://testing.com" type="primary" />);

            const link = screen.getByRole('link');

            act(() => {
                fireEvent.click(link);
            });

            expect(window.location.href).toEqual('http://testing.com');
        });

        it('should open the link in a new tab when clicked', () => {
            render(
                <ThemeAwareLink
                    label="test label"
                    href="http://testing.com"
                    type="primary"
                    target="_blank"
                />
            );

            const link = screen.getByRole('link');

            act(() => {
                fireEvent.click(link);
            });

            expect(link).toHaveProperty('href', 'http://testing.com/');
            expect(link).toHaveProperty('target', '_blank');
        });
    });
});
