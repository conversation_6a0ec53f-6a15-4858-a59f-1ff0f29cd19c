import React from 'react';
import { render, screen } from '@testing-library/react';

import HeaderLocation from './header-location';

type Props = React.ComponentProps<typeof HeaderLocation>;

const renderHeaderLocation = (extraProps = {} as Partial<Props>) =>
    render(<HeaderLocation {...extraProps} />);

describe('HeaderLocation component', () => {
    it('renders', () => {
        const SUT = renderHeaderLocation();
        expect(SUT.asFragment()).toMatchInlineSnapshot(`
            <DocumentFragment>
              <div
                class="header-location"
              >
                <span
                  aria-hidden="true"
                  class="icon icon--beacon location-icon css-0 eom5h670"
                />
              </div>
            </DocumentFragment>
        `);
    });

    it('can have a location', () => {
        renderHeaderLocation({ location: 'London' });
        expect(screen.getByText('London')).toBeInTheDocument();
    });
});
