import React from 'react';
import { string } from 'prop-types';

import cx from 'classnames';

const HeaderTitle = ({ size, title }) => {
    const ElementType = size;

    const classes = cx(ElementType, 'header-title');

    return (
        <ElementType className={classes}>
            {title}
        </ElementType>
    );
};

HeaderTitle.defaultProps = {
    size: 'h1',
    title: null,
};

HeaderTitle.propTypes = {
    size: string,
    title: string,
};

export default HeaderTitle;
