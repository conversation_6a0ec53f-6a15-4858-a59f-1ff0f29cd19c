import { render } from '@testing-library/react';
import React from 'react';
import RadioWithLabel from './radio-with-label';

describe('radio-list', () => {
    it('render', () => {
        const rendered = render(
            <RadioWithLabel
                id="id-1"
                label="Item 1"
                listName="list-a"
                isDisabled={false}
                selectedValue="dog"
                setSelectedValue={() => {}}
                setSelectedDisplayName={() => {}}
            />
        );

        expect(rendered.asFragment()).toMatchSnapshot();
    });

    it('render with disabled theme', () => {
        const rendered = render(
            <RadioWithLabel
                id="id-1"
                label="Item 1"
                listName="list-a"
                isDisabled
                selectedValue="dog"
                setSelectedValue={() => {}}
                setSelectedDisplayName={() => {}}
            />
        );

        expect(rendered.asFragment()).toMatchSnapshot();
    });

    it('render with disabled theme and a text tooltip', () => {
        const rendered = render(
            <RadioWithLabel
                id="id-1"
                label="Item 1"
                listName="list-a"
                isDisabled
                selectedValue="dog"
                setSelectedValue={() => {}}
                setSelectedDisplayName={() => {}}
                hasTooltip
                tooltipMessage="This is a tooltip"
            />
        );

        expect(rendered.asFragment()).toMatchSnapshot();
    });

    it('render with disabled theme and a JSX tooltip', () => {
        const rendered = render(
            <RadioWithLabel
                id="id-1"
                label="Item 1"
                listName="list-a"
                isDisabled
                selectedValue="dog"
                setSelectedValue={() => {}}
                setSelectedDisplayName={() => {}}
                hasTooltip
                tooltipMessage={<h1>This is a tooltip</h1>}
            />
        );

        expect(rendered.asFragment()).toMatchSnapshot();
    });
});
