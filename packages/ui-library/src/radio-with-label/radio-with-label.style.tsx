import styled from '@emotion/styled';
import { breakpoints, colors, fontWeights, gutterSizes, mediaQuery } from '../base/theme';

export const RadioWithLabelLi = styled.li`
    && {
        position: relative;

        .hint-tooltip {
            margin: 0 0 -${gutterSizes.base} ${gutterSizes.base};

            .btn-link {
                line-height: inherit;
            }

            ${mediaQuery.from(breakpoints.small)} {
                .hint-tooltip-content--left {
                    transform: translateX(-52px);
                }
            }
        }

        &.is-disabled {
            & > span {
                color: ${colors.bark60};
                cursor: auto;

                &:before {
                    background: ${colors.branch10};
                    border-color: ${colors.newLigthGrey};
                }
            }
        }
    }
`;

export const RadioSwitch = styled.input`
    display: none;

    & + label,
    & + span {
        display: flex;
        align-items: center;
        cursor: pointer;
        font-weight: ${fontWeights.normal};
        width: 100%;
        padding: 0 ${gutterSizes.xxxlarge} 0 0;
        margin-bottom: ${gutterSizes.base};
        position: relative;
        vertical-align: middle;

        &:before {
            content: '';
            background: ${colors.white};
            border-radius: 40px;
            border: 1px solid ${colors.bark40};
            width: 24px;
            height: 24px;
            margin-top: -${gutterSizes.medium};
            position: absolute;
            top: 50%;
            left: auto;
            right: 0;
            vertical-align: middle;
        }
    }

    &:checked + label {
        &:before {
            background: ${colors.green};
            background: radial-gradient(
                ellipse at center,
                ${colors.green} 0,
                ${colors.green} 39%,
                #fff 44%,
                #fff 100%
            );
        }
    }
`;
