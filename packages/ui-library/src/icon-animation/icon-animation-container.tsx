 
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import {
    AnimationItem,
    AnimationDirection,
    AnimationEventCallback,
    LottiePlayer,
} from 'lottie-web';

export interface Props {
    isLoop?: boolean;
    isAutoPlay: boolean;
    speed?: number;
    direction?: AnimationDirection;
    onComplete?: AnimationEventCallback;
    onLoopComplete?: AnimationEventCallback;
    onEnterFrame?: AnimationEventCallback;
    onSegmentStart?: AnimationEventCallback;
    onConfigReady?: AnimationEventCallback;
    onDataReady?: AnimationEventCallback;
    onDOMLoaded?: AnimationEventCallback;
    onDestroy?: AnimationEventCallback;
}

interface InnerContainerProps extends Props {
    animationData: any;
}

// eslint-disable-next-line react/display-name
const IconAnimationContainer = forwardRef(
    (
        {
            animationData,
            isLoop = false,
            isAutoPlay = false,
            speed = 1,
            direction = 1,
            onComplete,
            onLoopComplete,
            onEnterFrame,
            onSegmentStart,
            onConfigReady,
            onDataReady,
            onDOMLoaded,
            onDestroy,
            ...otherProps
        }: InnerContainerProps,
        ref
    ) => {
        const containerRef = useRef(null!);
        const [instance, setAnimationInstance] = useState<AnimationItem | null>(null);

        useImperativeHandle(
            ref,
            () => ({
                getInstance: () => {
                    return instance;
                },
                // Reference methods: http://airbnb.io/lottie/#/web?id=usage-1
                play: () => {
                    instance?.play();
                },
                stop: () => {
                    instance?.stop();
                },
                pause: () => {
                    instance?.pause();
                },
            }),
            [instance]
        );

        useEffect(() => {
            const initAnimation = async () => {
                // Client side only import
                const lottiePlayer: LottiePlayer = (await import(
                    'lottie-web'
                )) as unknown as LottiePlayer;
                const animation = lottiePlayer.loadAnimation({
                    container: containerRef.current,
                    renderer: 'svg',
                    loop: isLoop,
                    autoplay: isAutoPlay,
                    animationData,
                });
                lottiePlayer.setSpeed(speed);
                lottiePlayer.setDirection(direction);

                // Referene events: http://airbnb.io/lottie/#/web?id=events
                onComplete && animation.addEventListener('complete', onComplete);
                onLoopComplete && animation.addEventListener('loopComplete', onLoopComplete);
                onEnterFrame && animation.addEventListener('enterFrame', onEnterFrame);
                onSegmentStart && animation.addEventListener('segmentStart', onSegmentStart);
                onConfigReady && animation.addEventListener('config_ready', onConfigReady);
                onDataReady && animation.addEventListener('data_ready', onDataReady);
                onDOMLoaded && animation.addEventListener('DOMLoaded', onDOMLoaded);
                onDestroy && animation.addEventListener('destroy', onDestroy);

                setAnimationInstance(animation);
            };

            initAnimation();
            return () => {
                instance?.destroy();
            };
        }, []);
        return <span ref={containerRef} {...otherProps} />;
    }
);

export default IconAnimationContainer;
