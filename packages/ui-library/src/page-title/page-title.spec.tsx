import React from 'react';
import { render } from '@testing-library/react';

import PageTitle from './page-title';

type Props = React.ComponentProps<typeof PageTitle>;

const renderPageTitle = (extraProps = {} as Props) => render(<PageTitle {...extraProps} />);

describe('Header block component', () => {
    it('renders', () => {
        const SUT = renderPageTitle({
            title: 'Page Title',
            className: 'class',
            style: { display: 'block' },
        });
        expect(SUT.asFragment()).toMatchInlineSnapshot(`
            <DocumentFragment>
              <h1
                class="class"
                itemprop="name"
                style="display: block;"
              >
                Page Title
              </h1>
            </DocumentFragment>
        `);
    });
});
