// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ad-feature-label should render the minimal version when the type is featured 1`] = `
<DocumentFragment>
  <div
    class="ad-feature-label-minimal css-1tnty2t-ad-feature-label e1shgvf80"
    color="#007FB0"
    data-q="ad-feature-label-featured"
  >
    Featured
  </div>
</DocumentFragment>
`;

exports[`ad-feature-label should render the minimal version when the type is spotlight 1`] = `
<DocumentFragment>
  <div
    class="ad-feature-label-minimal css-1tnty2t-ad-feature-label e1shgvf80"
    color="#52a744"
    data-q="ad-feature-label-spotlight"
  >
    Spotlight
  </div>
</DocumentFragment>
`;

exports[`ad-feature-label should render the minimal version when the type is urgent 1`] = `
<DocumentFragment>
  <div
    class="ad-feature-label-minimal css-1tnty2t-ad-feature-label e1shgvf80"
    color="#e52815"
    data-q="ad-feature-label-urgent"
  >
    Urgent
  </div>
</DocumentFragment>
`;

exports[`ad-feature-label should render when the type is featured 1`] = `
<DocumentFragment>
  <div
    class="css-naemyg-ad-feature-label e1shgvf81"
    color="#007FB0"
    data-q="ad-feature-label-featured"
  >
    Featured
  </div>
</DocumentFragment>
`;

exports[`ad-feature-label should render when the type is spotlight 1`] = `
<DocumentFragment>
  <div
    class="css-1s9odp5-ad-feature-label e1shgvf81"
    color="#52a744"
    data-q="ad-feature-label-spotlight"
  >
    Spotlight
  </div>
</DocumentFragment>
`;

exports[`ad-feature-label should render when the type is urgent 1`] = `
<DocumentFragment>
  <div
    class="css-1mi3hab-ad-feature-label e1shgvf81"
    color="#e52815"
    data-q="ad-feature-label-urgent"
  >
    Urgent
  </div>
</DocumentFragment>
`;
