import React from 'react';
import { render } from '@testing-library/react';

import Icon from './icon';

type Props = React.ComponentProps<typeof Icon>;

const renderIcon = (extraProps = {} as Props) => render(<Icon {...extraProps} />);

describe('Icon component', () => {
    it('renders', () => {
        const SUT = renderIcon({ type: 'close', size: 'large', className: 'abc' });
        expect(SUT.asFragment()).toMatchInlineSnapshot(`
            <DocumentFragment>
              <span
                aria-hidden="true"
                class="icon icon--large icon--close abc css-0 eom5h670"
              />
            </DocumentFragment>
        `);
    });
});
