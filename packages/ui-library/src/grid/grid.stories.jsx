import React from 'react';
import PropTypes from 'prop-types';

import { Container } from '..';

import Grid from './grid';

// box used purely for demonstrative purposes
const Box = ({ children }) => (
    <div style={{ backgroundColor: 'rgba(57, 151, 186, 0.4)', height: 100 }}>{children}</div>
);
Box.propTypes = {
    children: PropTypes.node.isRequired,
};

export default {
    title: 'Layout/grid',
    component: Grid,
};

export const Columns12 = () => (
    <Container>
        <Box>
            <Grid container>
                <Grid item>
                    <Box />
                </Grid>
                <Grid item>
                    <Box />
                </Grid>
                <Grid item>
                    <Box />
                </Grid>
                <Grid item>
                    <Box />
                </Grid>
                <Grid item>
                    <Box />
                </Grid>
                <Grid item>
                    <Box />
                </Grid>
                <Grid item>
                    <Box />
                </Grid>
                <Grid item>
                    <Box />
                </Grid>
                <Grid item>
                    <Box />
                </Grid>
                <Grid item>
                    <Box />
                </Grid>
                <Grid item>
                    <Box />
                </Grid>
                <Grid item>
                    <Box />
                </Grid>
            </Grid>
        </Box>
    </Container>
);

export const SimpleUse = () => (
    <Container>
        <Box>
            <Grid container>
                <Grid item>
                    <Box />
                </Grid>
                <Grid item>
                    <Box />
                </Grid>
                <Grid item>
                    <Box />
                </Grid>
                <Grid item>
                    <Box />
                </Grid>
            </Grid>
        </Box>
    </Container>
);

export const ColumnSpanSize = () => (
    <Container>
        <Box>
            <Grid container>
                <Grid item col={6}>
                    <Box />
                </Grid>
                <Grid item col={3}>
                    <Box />
                </Grid>
                <Grid item col={3}>
                    <Box />
                </Grid>
            </Grid>
        </Box>
    </Container>
);

export const NestedGrids = () => (
    <Container>
        <Box>
            <Grid container>
                <Grid item>
                    <Box />
                </Grid>
                <Grid item container>
                    <Grid item>
                        <Box />
                    </Grid>
                    <Grid item>
                        <Box />
                    </Grid>
                    <Grid item>
                        <Box />
                    </Grid>
                    <Grid item>
                        <Box />
                    </Grid>
                </Grid>
            </Grid>
        </Box>
    </Container>
);

export const WithContentJustified = () => (
    <Container>
        <Box>
            <Grid container>
                <Grid item>Not Justified</Grid>
                <Grid item justifyContent="flex-end">
                    Justified to end
                </Grid>
            </Grid>
        </Box>
    </Container>
);

export const MediaQueryBreakPoints = () => (
    <div>
        <Container>
            <h4>All break points</h4>
            <Grid container>
                <Grid item col={6}>
                    <Box />
                </Grid>
                <Grid item col={3}>
                    <Box />
                </Grid>
                <Grid item col={3}>
                    <Box />
                </Grid>
            </Grid>
        </Container>
        <Container>
            <h4>Small and above</h4>
            <Grid container>
                <Grid item colS={6}>
                    <Box />
                </Grid>
                <Grid item colS={3}>
                    <Box />
                </Grid>
                <Grid item colS={3}>
                    <Box />
                </Grid>
            </Grid>
        </Container>
        <Container>
            <h4>Medium and above</h4>
            <Grid container>
                <Grid item colM={6}>
                    <Box />
                </Grid>
                <Grid item colM={3}>
                    <Box />
                </Grid>
                <Grid item colM={3}>
                    <Box />
                </Grid>
            </Grid>
        </Container>
        <Container>
            <h4>Large and above</h4>
            <Grid container>
                <Grid item colL={6}>
                    <Box />
                </Grid>
                <Grid item colL={3}>
                    <Box />
                </Grid>
                <Grid item colL={3}>
                    <Box />
                </Grid>
            </Grid>
        </Container>
        <Container>
            <h4>X-large and above</h4>
            <Grid container>
                <Grid item colXl={6}>
                    <Box />
                </Grid>
                <Grid item colXl={3}>
                    <Box />
                </Grid>
                <Grid item colXl={3}>
                    <Box />
                </Grid>
            </Grid>
        </Container>
        <Container>
            <h4>XX-Large and above</h4>
            <Grid container>
                <Grid item colXxl={6}>
                    <Box />
                </Grid>
                <Grid item colXxl={3}>
                    <Box />
                </Grid>
                <Grid item colXxl={3}>
                    <Box />
                </Grid>
            </Grid>
        </Container>
    </div>
);
