import React from 'react';
import { render, screen } from '@testing-library/react';

import RecentSearchesCard from './recent-searches-card';

type Props = React.ComponentProps<typeof RecentSearchesCard>;

const renderRecentSearchesCard = (extraProps = {} as Props) =>
    render(<RecentSearchesCard {...extraProps} />);

describe('RecentSearchesCard', () => {
    it('renders', () => {
        const SUT = renderRecentSearchesCard();
        expect(SUT.asFragment()).toMatchInlineSnapshot(`
            <DocumentFragment>
              <div
                class="recent-searches-card"
              >
                <div
                  class="card-content"
                >
                  <div
                    class="card-icon"
                  >
                    <span
                      aria-hidden="true"
                      class="icon icon--magnifying-glass css-0 eom5h670"
                    />
                  </div>
                </div>
              </div>
            </DocumentFragment>
        `);
    });

    it('renders a link', () => {
        renderRecentSearchesCard({ link: 'http://link.com' });
        expect(screen.getByRole('link')).toHaveAttribute('href', 'http://link.com');
    });
});
