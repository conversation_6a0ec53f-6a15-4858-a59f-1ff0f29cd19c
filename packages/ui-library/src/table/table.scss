@import "../base/color-variables";
@import "../base/grid";

.table {
    border-collapse: collapse;
    border-spacing: 0;

    th,
    td {
        padding: $gutter-size;
    }

    &--comfortable {
        th,
        td {
            padding: $gutter-size $gutter-size--large;
        }
    }

    &--space-between {
        th:first-child,
        td:first-child {
            text-align: left;
        }

        th:last-child,
        td:last-child {
            text-align: right;
        }
    }

    &--striped {
        tr:nth-child(odd) {
            background-color: $color-background--striped;
        }
    }

    &--full-width {
        width: 100%;
    }
}
