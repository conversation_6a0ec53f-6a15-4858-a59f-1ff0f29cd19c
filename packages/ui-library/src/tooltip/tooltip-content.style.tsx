import React, { forwardRef } from 'react';

import { css } from '@emotion/react';
import styled from '@emotion/styled';
import { colors, gutterSizes } from '../base/theme';
import { Button } from '../index';

export const CloseButton = styled(Button)`
    position: absolute;
    top: 0;
    right: 0;
`;

interface ContainerProps extends React.PropsWithChildren {
    className?: string;
    hasCloseButton: boolean;
    position: 'top' | 'below' | 'left' | 'right';
}

export const Container = forwardRef<HTMLDivElement, ContainerProps>((props, ref) => {
    const containerStyle = ({
        hasCloseButton,
        position,
    }: {
        hasCloseButton: boolean;
        position: 'top' | 'below' | 'left' | 'right';
    }) => css`
        background: ${colors.white};
        border-radius: 2px;
        box-shadow: 0px 0px 12px 0px ${colors.shadowLight};
        opacity: 0;
        visibility: hidden;
        padding: ${gutterSizes.medium};
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translate(-50%);
        z-index: 10;
        width: 200px;
        margin: ${gutterSizes.medium} 0 0 0;
        text-align: center;
        transition: opacity 140ms ease-in, visibility 140ms ease-in;

        &:before {
            content: ' ';
            position: absolute;
            width: 10px;
            height: 10px;
            margin-top: -1px;
            background: inherit;
            left: 50%;
            top: 0;
            transform: rotate(45deg) translate(-50%, 0%);
        }

        &:after {
            content: ' ';
            position: absolute;
            left: 50%;
            top: ${gutterSizes.small};
            transform: translate(-50%, 0%);
            width: ${gutterSizes.xlarge};
            height: ${gutterSizes.base};
            margin-top: -${gutterSizes.small};
            background: inherit;
        }

        ${position === 'top' &&
        css`
            bottom: 100%;
            top: auto;
            left: 50%;
            transform: translate(-50%);
            margin: 0 0 ${gutterSizes.medium} 0;

            &:before {
                left: 50%;
                top: auto;
                bottom: -9px;
                transform: rotate(45deg) translate(-50%, 0%);
            }

            &:after {
                left: 50%;
                top: auto;
                bottom: 0;
                transform: translate(-50%, 0%);
                width: ${gutterSizes.xlarge};
                height: ${gutterSizes.base};
            }
        `}

        ${position === 'left' &&
        css`
            top: 50%;
            left: auto;
            right: 100%;
            transform: translate(-50%, -50%);
            margin: 0 ${gutterSizes.medium} 0 0;

            &:before {
                right: -5px;
                left: auto;
                top: 50%;
                transform: rotate(45deg) translate(-50%, -50%);
                margin-top: 0;
            }

            &:after {
                right: -4px;
                left: auto;
                top: 50%;
                bottom: 0;
                transform: translate(-50%, -50%);
                width: ${gutterSizes.base};
                height: ${gutterSizes.xlarge};
            }
        `}

        ${position === 'right' &&
        css`
            top: 50%;
            left: 100%;
            transform: translate(0, -50%);
            margin: 0 0 0 ${gutterSizes.medium};

            &:before {
                left: -5px;
                top: 50%;
                transform: rotate(45deg) translate(-50%, -50%);
                margin-top: 0;
                box-shadow: 0px 0px 12px 0px ${colors.shadowLight};
            }

            &:after {
                left: 7px;
                top: 50%;
                bottom: 0;
                transform: translate(-50%, -50%);
                width: ${gutterSizes.medium};
                height: ${gutterSizes.xlarge};
            }
        `}
        
        ${hasCloseButton &&
        css`
            padding: ${gutterSizes.large} ${gutterSizes.xlarge} ${gutterSizes.large}
                ${gutterSizes.large};
        `}
    `;

    return (
        <div className={props.className} css={containerStyle(props)} ref={ref}>
            {props.children}
        </div>
    );
});

Container.displayName = 'Container';
