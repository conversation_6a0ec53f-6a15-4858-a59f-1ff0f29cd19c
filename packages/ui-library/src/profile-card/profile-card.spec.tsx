import React from 'react';
import { render, screen } from '@testing-library/react';

import ProfileCard from './profile-card';

const defaultProps = {
    profilePicture: null,
    profileLink: null,
    averageRating: null,
    ratingBreakdown: null,
    formattedAverageRating: null,
    hasRatingTooltip: false,
    totalRatingsReceived: null,
    profileName: 'name',
};

type Props = React.ComponentProps<typeof ProfileCard>;

const renderProfileCard = (extraProps = {} as Partial<Props>) =>
    render(<ProfileCard {...defaultProps} {...extraProps} />);

describe('Profile card...', () => {
    it('renders picture and link', () => {
        const props = {
            profileName: 'abc',
            profilePicture: 'abc',
            profileLink: 'profile-link',
        };
        const SUT = renderProfileCard({ ...props });
        expect(screen.getByAltText("abc's profile picture")).toBeInTheDocument();
        expect(screen.getByRole('link', { name: 'View profile' })).toHaveAttribute(
            'href',
            'profile-link'
        );

        expect(SUT.asFragment()).toMatchSnapshot();
    });

    it('rating, rating tooltip and profile link', () => {
        const props = {
            profileName: 'abc',
            profilePicture: 'abc',
            profileLink: 'profile-link',
            averageRating: 123,
            ratingBreakdown: { a: 1, b: 2, c: 3 },
            formattedAverageRating: '123',
            hasRatingTooltip: true,
            totalRatingsReceived: 123,
        };
        const SUT = renderProfileCard({ ...props });
        expect(screen.getByAltText("abc's profile picture")).toBeInTheDocument();
        expect(screen.getByRole('link', { name: 'View profile' })).toHaveAttribute(
            'href',
            'profile-link'
        );

        expect(SUT.asFragment()).toMatchSnapshot();
    });
});
