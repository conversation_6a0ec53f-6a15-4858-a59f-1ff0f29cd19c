import styled from "@emotion/styled";
import {
    colors,
    fontSizes,
    fontWeights,
    gutterSizes,
    lineHeights,
} from "@gumtree/ui-library/src/base/theme";

export const Container = styled.div<{ invalid?: boolean }>`
    position: relative;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: ${gutterSizes.base} ${gutterSizes.medium};
    gap: 6px;
    border: 1px solid ${({ invalid }) => (invalid ? colors.red : colors.bark40)};
    border-radius: 4px;
    background: ${colors.white};
`;

export const TelInput = styled.input`
    margin: 0;
    border: 0;
    padding: 0;
    display: inline-block;
    vertical-align: middle;
    white-space: normal;
    background: none;
    box-sizing: content-box;

    :focus {
        outline: 0;
    }

    ::placeholder {
        color: ${colors.bark40};
    }

    font-weight: ${fontWeights.normal};
    font-size: ${fontSizes.base};
    line-height: ${lineHeights.base};
    letter-spacing: 0.01em;

    // hack for autofill user agent stylesheet  https://developer.mozilla.org/en-US/docs/Web/CSS/:autofill
    box-shadow: 0 0 0 ${lineHeights.base} white inset !important;
    color: ${colors.bark} !important;
`;

export const Arrow = styled.span<{ isOpen?: boolean }>`
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: ${({ isOpen }) => (isOpen ? `5px solid ${colors.darkGrey}` : "none")};
    border-top: ${({ isOpen }) => (isOpen ? "none" : `5px solid ${colors.darkGrey}`)};
`;

export const ArrowWrapper = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    padding: ${gutterSizes.base};
`;

export const SpaceWrapper = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3px;
`;

export const SelectedCountry = styled.button`
    display: flex;
    align-items: center;
    border: 0;
    background: transparent;
`;

export const SelectedCountryDialCode = styled.div`
    vertical-align: middle;
`;

export const Dropdown = styled.div<{ isOpen?: boolean }>`
    box-sizing: border-box;
    display: ${({ isOpen }) => (isOpen ? "block" : "none")};
    position: absolute;
    top: 100%;
    left: -1px;
    right: -1px;
    height: 254px;
    background: ${colors.white};
    border: 1px solid ${colors.bark40};
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
    border-radius: 0 0 4px 4px;
    z-index: 1;
`;

export const DropdownOverlay = styled.div`
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    &:before {
        position: absolute;
        content: "";
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: ${colors.barkDark};
        opacity: 0.5;
    }
`;
