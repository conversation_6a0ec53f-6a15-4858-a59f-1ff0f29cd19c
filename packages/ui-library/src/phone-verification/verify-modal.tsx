import React, { useReducer, useState } from 'react';
import { StyledDialog } from '@gumtree/shell/src/login-modal/app.style';
import { PhoneEntryStep } from './phone-entry-step';
import { OtpEntryStep } from './otp-entry-step';
import { PhoneVerifiedStep } from './phone-verified-step';
import { PhoneNumberProvider } from './data/phone-number-provider';
import { AuthStatus, ModalStep } from './constants';
import { useAuthStatus } from './data/use-auth-status';
import { ModalStepProvider } from './data/modal-step-provider';
import { modalStepReducer } from './data/modal-step-reducer';

export type AuthModalProps = {
    initialStep?: ModalStep;
};

export const VerifyModal = ({ initialStep = ModalStep.PhoneEntry }: AuthModalProps) => {
    const [state, dispatch] = useReducer(modalStepReducer, { step: initialStep });
    const [phoneNumber, setPhoneNumber] = useState<string>('');
    const [authStatus, setAuthStatus] = useAuthStatus();

    const { step } = state;

    const steps = {
        [ModalStep.PhoneEntry]: PhoneEntryStep,
        [ModalStep.OtpEntry]: OtpEntryStep,
        [ModalStep.PhoneVerified]: PhoneVerifiedStep,
    };

    const StepComponent = steps[step];

    return (
        <ModalStepProvider value={[state, dispatch]}>
            <PhoneNumberProvider value={[phoneNumber, setPhoneNumber]}>
                <StyledDialog
                    isOpen
                    onClose={() => {
                        // if user click the close button, act successed as same as the continue button
                        if (authStatus === AuthStatus.ShowVerified) {
                            setAuthStatus(AuthStatus.Succeeded);
                        } else {
                            setAuthStatus(AuthStatus.Aborted);
                        }
                    }}
                    closeIconType="clear"
                >
                    <StepComponent />
                </StyledDialog>
            </PhoneNumberProvider>
        </ModalStepProvider>
    );
};
