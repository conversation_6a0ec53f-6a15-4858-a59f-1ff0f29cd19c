import React, { useEffect, useState } from 'react';
import { useTheme } from '@emotion/react';
import { Button } from '@gumtree/ui-library';
import { trackGA4Event } from '@gumtree/shared/src/util/ga4-shared';
import { actionButtonCss, ErrorMessage } from '@gumtree/phone-auth/src/app.style';
import { AuthenticationStatus } from '@gumtree/phone-auth/src/api/clients/phone-auth';
import { validateOtp } from '@gumtree/phone-auth/src/utils/validation';
import { useVerifyPhoneAuthentication } from '../data/use-verify-phone-authentication';
import { useConfig } from '../data/use-config';
import { StyledNumberBoxes } from './otp-entry-step.style';
import { ErrorMessageText, ModalStep } from '../constants';
import { useModalStep } from '../data/use-modal-step';

export const OtpEntryForm = () => {
    const theme = useTheme();
    const { actions } = useModalStep();
    const [otp, setOtp] = useState<string>('');
    const [errorMessage, setErrorMessage] = useState('');
    const [isValid, setIsValid] = useState(() => validateOtp(otp));
    const verifyPhoneAuthenticationMutation = useVerifyPhoneAuthentication();
    const { listingDetails } = useConfig();

    useEffect(() => {
        errorMessage &&
            trackGA4Event<GA4.PhoneVerificationEvent>({
                event: 'phone_verification_enter_code_error',
                listingDetails,
            });
    }, [errorMessage]);

    useEffect(() => {
        setIsValid(validateOtp(otp));
    }, [otp]);

    const handleChange = (value) => {
        setOtp(value);
        setErrorMessage('');
    };

    const handleConfirm = async (e: React.SyntheticEvent) => {
        e.preventDefault();
        if (!isValid) return setErrorMessage('Please enter a valid code');

        try {
            const authenticationStatus = await verifyPhoneAuthenticationMutation.mutateAsync(otp);

            switch (authenticationStatus) {
                case AuthenticationStatus.Verified:
                    actions.setStep(ModalStep.PhoneVerified);
                    break;
                case AuthenticationStatus.Unverified:
                    setOtp('');
                    setErrorMessage('Please enter a valid code');
                    break;
                case AuthenticationStatus.Blocked:
                    setErrorMessage(ErrorMessageText.BLOCKED);
                    break;
                default:
                    setErrorMessage(ErrorMessageText.UNKNOWN);
                    break;
            }
        } catch (e) {
            setErrorMessage(e.response?.data?.title || ErrorMessageText.UNKNOWN);
        }

        trackGA4Event<GA4.PhoneVerificationEvent>({
            event: 'phone_verification_enter_code',
            listingDetails,
        });
    };

    return (
        <form onSubmit={handleConfirm}>
            <StyledNumberBoxes
                value={otp}
                shouldAutoFocus
                onChange={handleChange}
                isError={!!errorMessage}
            />
            {errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
            <Button
                type="submit"
                fullFlex
                isLoading={verifyPhoneAuthenticationMutation.isLoading}
                display="primary"
                label="Confirm"
                loadingText="Confirming"
                css={actionButtonCss(theme)}
            />
        </form>
    );
};
