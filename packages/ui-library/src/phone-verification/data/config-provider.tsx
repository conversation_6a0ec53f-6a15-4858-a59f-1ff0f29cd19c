import { PhoneAuthProps } from '@gumtree/phone-auth';
import React, { createContext } from 'react';
import { TriggerPoint } from '../constants';

export interface AppProps extends PhoneAuthProps {
    userType: string;
    adCategoryId: number;
}

export type Config = Pick<AppProps, 'basePath' | 'userData'> & {
    listingDetails: {} | GA4.GA4ListingDetails;
    triggerPoint: TriggerPoint;
};

export interface ConfigProviderProps {
    children: React.ReactNode;
    value: Config;
}

export const ConfigContext = createContext<Config>(null!);

export const ConfigProvider: React.FC<ConfigProviderProps> = ({
    value,
    children,
}: ConfigProviderProps) => (
    <ConfigContext.Provider value={value}>{children}</ConfigContext.Provider>
);
