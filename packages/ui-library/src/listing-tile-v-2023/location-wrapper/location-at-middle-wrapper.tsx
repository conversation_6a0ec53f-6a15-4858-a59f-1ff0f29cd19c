import React from 'react';

import { css } from '@emotion/react';
import {
    breakpoints,
    fontSizes,
    fontWeights,
    gutterSizes,
    lineHeights,
    mediaQuery,
} from '../../base/theme';

export const LocationAtMiddleWrapper = ({
    children,
    isGridView,
    isJobsCategory,
    ...props
}: React.PropsWithChildren<{
    isGridView?: boolean;
    isJobsCategory?: boolean;
}>) => {
    const displayLargeMarginBottom = !isGridView && !isJobsCategory;

    const LocationWrapperStyles = css`
        font-weight: ${fontWeights.normal};
        font-size: ${fontSizes.base};
        line-height: ${lineHeights.base};
        margin-bottom: ${displayLargeMarginBottom ? gutterSizes.large : gutterSizes.base};
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: ${isGridView ? 1 : 2};
        -webkit-box-orient: vertical;

        ${mediaQuery.until(breakpoints.medium)} {
            -webkit-line-clamp: 1;
        }
    `;

    return (
        <div css={LocationWrapperStyles} {...props}>
            {children}
        </div>
    );
};
