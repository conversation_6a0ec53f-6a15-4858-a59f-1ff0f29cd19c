import React from 'react';

import { css } from '@emotion/react';
import { PageLayout } from '@gumtree/shared/src/types/srp';

import { breakpoints, gutterSizes, mediaQuery } from '../../base/theme';

export const CounterWrapper = ({
    children,
    pageLayout,
    ...props
}: React.PropsWithChildren<{
    pageLayout: PageLayout;
}>) => {
    const CounterWrapperStyles = css`
        position: absolute;
        left: ${gutterSizes.base};
        bottom: ${gutterSizes.base};
        display: flex;

        ${mediaQuery.until(breakpoints.large)} {
            left: 6px;
            bottom: 6px;
        }

        ${mediaQuery.until(breakpoints.small)} {
            ${pageLayout === PageLayout.TwoColumns && `display: none`};
        }
    `;

    return (
        <div css={CounterWrapperStyles} {...props}>
            {children}
        </div>
    );
};
