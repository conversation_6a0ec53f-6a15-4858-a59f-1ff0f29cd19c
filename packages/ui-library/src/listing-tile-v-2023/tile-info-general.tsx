import React from 'react';

import { L1CategoryId, getL1CategoryIdBySeoName } from '@gumtree/shared/src/util/category-mapping';

import { getShortTimeSince } from '../utils/time-service';

import {
    FullTime,
    H2,
    LocationInline,
    PriceStyle,
    ShortTime,
    TimeWrapperStyle,
    VatStyle,
} from './listing-tile.style';
import { colors } from '../base/theme';
import { qaAttribute } from '../utils/qa-service';
import { LocationAtMiddleWrapper } from './location-wrapper/location-at-middle-wrapper';
import { InfoWrapper } from './info-wrapper/info-wrapper';
import { TileHeaderWrapper } from './title-row-wrapper/title-row-wrapper';

export interface Props {
    title: string;
    isGridViewOnDesktop?: boolean;
    attributesOrDescription: {
        isAttributes: boolean;
        render: () => React.ReactElement | undefined;
    };
    favouriteToggle: React.ReactElement | undefined;
    location: string;
    timeSincePosted?: string;
    price: string;
    sellerId: number;
    l1CategoryId: L1CategoryId;
    vat?: string;
}

const TileInfoGeneral = ({
    isGridViewOnDesktop,
    title,
    attributesOrDescription,
    location,
    timeSincePosted = '',
    price,
    vat,
    l1CategoryId,
    favouriteToggle,
}: Props) => {
    const hasPrice = Boolean(price);
    const isLocationInline = !hasPrice;

    const isDescriptionInBarkColour =
        l1CategoryId === getL1CategoryIdBySeoName('business-services') ||
        l1CategoryId === getL1CategoryIdBySeoName('community');
    const showPostedDate = l1CategoryId !== getL1CategoryIdBySeoName('business-services');

    const render = () => (
        <div
            style={isDescriptionInBarkColour ? { color: colors.bark60 } : {}}
            suppressHydrationWarning
            {...qaAttribute('tile-description')}
        >
            {attributesOrDescription.render()}
        </div>
    );

    return (
        <>
            <TileHeaderWrapper>
                <H2 isGridView={isGridViewOnDesktop} {...qaAttribute('tile-title')}>
                    {title}
                </H2>
                {favouriteToggle}
            </TileHeaderWrapper>
            <>{render()}</>
            {!isLocationInline && (
                <LocationAtMiddleWrapper
                    isGridView={isGridViewOnDesktop}
                    {...qaAttribute('tile-location')}
                >
                    {location}
                </LocationAtMiddleWrapper>
            )}
            <InfoWrapper>
                {isLocationInline ? (
                    <LocationInline
                        {...qaAttribute('tile-location')}
                        isGridView={isGridViewOnDesktop}
                    >
                        {location}
                    </LocationInline>
                ) : (
                    <div css={PriceStyle} data-testid="price" {...qaAttribute('tile-price')}>
                        {price}
                        {vat && <span css={VatStyle}> ({vat})</span>}
                    </div>
                )}
                {showPostedDate && (
                    <div css={TimeWrapperStyle} {...qaAttribute('tile-datePosted')}>
                        {!isLocationInline ? (
                            timeSincePosted
                        ) : (
                            <>
                                <ShortTime>{getShortTimeSince(timeSincePosted)}</ShortTime>
                                <FullTime>{timeSincePosted}</FullTime>
                            </>
                        )}
                    </div>
                )}
            </InfoWrapper>
        </>
    );
};

export default TileInfoGeneral;
