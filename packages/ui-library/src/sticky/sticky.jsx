import React, { Component } from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';

import {
    addEventListener,
    removeEventListener,
    requestAnimationFrame,
} from '../utils/browser-service';
import { isContentScrolledOut, isContainerScrolledOut } from './sticky-service';

import './sticky.scss';

const STICKY_POSITION = {
    TOP: 'top',
    BOTTOM: 'bottom',
    DEFAULT: 'none',
};

class Sticky extends Component {
    state = {
        stickyPosition: STICKY_POSITION.DEFAULT,
    };

    componentDidMount() {
        addEventListener(window, 'scroll', this.setStickyClassName);
    }

    componentWillUnmount() {
        removeEventListener(window, 'scroll', this.setStickyClassName);
    }

    setStickyClassName = () => {
        requestAnimationFrame(() => {
            const stickToTop = isContainerScrolledOut(this.container);
            const stickToBottom = isContentScrolledOut(this.container, this.content);

            if (stickToBottom) {
                this.setState({
                    stickyPosition: STICKY_POSITION.BOTTOM,
                });
            } else if (stickToTop) {
                this.setState({
                    stickyPosition: STICKY_POSITION.TOP,
                });
            } else {
                this.setState({
                    stickyPosition: STICKY_POSITION.NONE,
                });
            }
        });
    };

    getClassName = () => {
        const { stickyPosition } = this.state;
        return classnames('content', {
            top: stickyPosition === STICKY_POSITION.TOP,
            bottom: stickyPosition === STICKY_POSITION.BOTTOM,
        });
    };

    render() {
        const { children, width } = this.props;
        return (
            <div
                data-testid="sticky"
                className="sticky-container"
                ref={(element) => (this.container = element)}
            >
                <div
                    className={this.getClassName()}
                    ref={(element) => (this.content = element)}
                    style={{ width }}
                >
                    {children}
                </div>
            </div>
        );
    }
}

Sticky.defaultProps = {
    width: 0,
};

Sticky.propTypes = {
    children: PropTypes.node.isRequired,
    width: PropTypes.number,
};

export default Sticky;
