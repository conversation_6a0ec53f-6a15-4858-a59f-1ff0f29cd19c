import styled from '@emotion/styled';
import { boxSizes, colors, fontSizes, fontWeights, gutterSizes } from '../base/theme';

const expandableButtonWidth = 44;

export const Header = styled.h2`
    font-size: ${fontSizes.xlarger};
`;

export const FAQItem = styled.div`
    padding: ${gutterSizes.large} 0;
    border-bottom: solid thin ${colors.bark10};
`;

export const FAQHeaderRow = styled.div`
    display: flex;
    align-items: center;
    cursor: pointer;
`;

export const ExpandableButton = styled.button`
    flex-shrink: 0;
    padding: 0;
    margin-left: ${gutterSizes.large};
    border: none;
    height: 44px;

    min-width: ${expandableButtonWidth}px;
    width: ${expandableButtonWidth}px;

    border-radius: ${boxSizes.borderRadius3};
    background-color: ${colors.branch5};
    font-size: ${fontSizes.xlarge};

    color: ${colors.bark};

    cursor: pointer;
`;

export const FAQExpandableRow = styled.div`
    padding-top: ${gutterSizes.large};
    padding-right: ${expandableButtonWidth + 8}px;
    white-space: pre-line;
`;

export const FAQItemHeader = styled.div`
    font-weight: ${fontWeights.bold};
    flex-grow: 1;
`;

export const ShowMoreLessWrapper = styled.div`
    padding-top: ${gutterSizes.large};
`;
