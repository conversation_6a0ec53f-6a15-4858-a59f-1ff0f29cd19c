import { getFormattedTime, getShortTimeSince } from './time-service';

describe('Time service...', () => {
    it('getFormattedTime should convert UNIX timestamp to YYYY:MM:DDTHH:MM:SS.MLSZ format.', () => {
        const unixTimeStamp = 1529420147537;
        const expectedTimeFormat = '2018-06-19T14:55:47.537Z';
        const result = getFormattedTime(unixTimeStamp);

        expect(result).toEqual(expectedTimeFormat);
    });
});

describe('getShortTimeSince function', () => {
    it('returns short mins value', () => {
        const timeSince = '123 mins ago';
        const result = getShortTimeSince(timeSince);
        const expectedTimeFormat = '123 m';

        expect(result).toEqual(expectedTimeFormat);
    });

    it('returns short hours value', () => {
        const timeSince = '2 hour ago';
        const result = getShortTimeSince(timeSince);
        const expectedTimeFormat = '2 h';

        expect(result).toEqual(expectedTimeFormat);
    });

    it('returns short days value', () => {
        const timeSince = '44 days ago';
        const result = getShortTimeSince(timeSince);
        const expectedTimeFormat = '44 d';

        expect(result).toEqual(expectedTimeFormat);
    });

    it('returns Now', () => {
        const timeSince = 'Just now';
        const result = getShortTimeSince(timeSince);
        const expectedTimeFormat = 'Now';

        expect(result).toEqual(expectedTimeFormat);
    });

    it('doesn`t return anything', () => {
        const timeSince = 'A while ago';
        const result = getShortTimeSince(timeSince);
        const expectedTimeFormat = '';

        expect(result).toEqual(expectedTimeFormat);
    });
});
