import type { KeywordSuggestionsResponse } from './suggestions-service-interface';

export type SimplifiedSuggestion = {
    name: string;
    categorySeoName: string;
};

export const convertToSimplifiedSuggestions = (
    suggestions: KeywordSuggestionsResponse
): SimplifiedSuggestion[] =>
    suggestions.map(({ completion: name, categories }) => {
        const categorySeoName = categories[0]?.canonicalName;
        return {
            name,
            categorySeoName,
        };
    });

export const removeSuggestion = (
    suggestions: SimplifiedSuggestion[],
    suggestionToRemove: string
): SimplifiedSuggestion[] =>
    suggestions.filter((suggestion) => suggestion.name !== suggestionToRemove);
