// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Logo component can be displayed vertically 1`] = `
<DocumentFragment>
  <div
    class="logo logo--vertical"
    title="Gumtree"
  >
    <div
      class="logo-image"
    >
      <svg
        height="24.298"
        viewBox="27.74 5.25 19.52 24.298"
        width="19.52"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M44.433 11.914a.707.707 0 0 1-.337-.606C43.76 7.942 40.933 5.25 37.5 5.25s-6.327 2.625-6.596 6.058a.806.806 0 0 1-.336.606c-1.683 1.211-2.827 3.164-2.827 5.384 0 3.029 2.087 5.654 4.914 6.395.471.135 1.01.202 1.144.067.337-.202.808-1.885.606-2.221-.135-.203-.539-.404-1.077-.539-1.683-.471-2.895-1.952-2.895-3.769 0-1.01.404-1.885 1.01-2.625a2.964 2.964 0 0 1 1.01-.808c.74-.404 1.144-1.144 1.144-1.952 0-.404.067-.808.202-1.211.539-1.548 1.952-2.692 3.702-2.692s3.164 1.144 3.702 2.692c.134.404.202.808.202 1.211 0 .808.403 1.548 1.144 1.952.404.202.673.471 1.01.808a3.967 3.967 0 0 1 1.01 2.625 3.907 3.907 0 0 1-3.903 3.904c-2.491 0-4.443 2.02-4.443 4.51v2.558c0 .471.067 1.009.202 1.144.27.27 2.02.27 2.288 0 .135-.135.203-.673.203-1.144v-2.625c0-.942.807-1.75 1.75-1.75 3.634 0 6.596-2.962 6.596-6.596-.002-2.155-1.147-4.107-2.829-5.318z"
          fill="#72EF36"
        />
      </svg>
    </div>
    <div
      class="logo-text"
    >
      <svg
        height="64.291"
        viewBox="169.06 371.734 383.756 64.291"
        width="383.756"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M281.766 388.8h-9.92c-.794 0-1.19.396-1.19 1.189v19.049c0 10.318-3.572 15.477-11.112 15.477-2.778 0-5.159-.793-6.746-2.777-1.588-1.984-2.382-4.365-2.382-7.539v-23.812c0-.793-.396-1.189-1.19-1.189h-9.921c-.794 0-1.19.396-1.19 1.189v26.588c0 5.557 1.587 10.318 5.158 13.891 3.175 3.572 7.541 5.16 13.097 5.16 5.556 0 9.921-1.588 13.493-4.764.396-.396.794 0 .794.396v1.588c0 .793.396 1.189 1.19 1.189h9.921c.793 0 1.189-.396 1.189-1.189v-43.654c-.001-.395-.396-.792-1.191-.792zm67.861-1.588c-7.143 0-12.699 2.381-16.667 7.541-3.571-4.764-8.334-7.541-15.081-7.541-5.158 0-9.523 1.586-12.699 4.365-.396.396-.793 0-.793-.396v-1.588c0-.793-.396-1.189-1.191-1.189h-9.523c-.793 0-1.189.396-1.189 1.189v43.258c0 .793.396 1.189 1.189 1.189h9.922c.793 0 1.19-.396 1.19-1.189v-20.637c0-4.762.794-7.936 2.776-10.316 1.984-2.383 4.367-3.572 7.541-3.572 2.381 0 4.763.793 6.351 2.777 1.587 1.588 2.381 3.969 2.381 7.143v24.605c0 .795.396 1.189 1.19 1.189h9.922c.793 0 1.189-.395 1.189-1.189v-20.637c0-4.762.793-7.936 2.777-10.316 1.984-2.383 4.365-3.572 7.541-3.572 2.381 0 4.762.793 6.35 2.777 1.588 1.588 2.381 3.969 2.381 7.143v24.605c0 .795.396 1.189 1.19 1.189h9.524c.793 0 1.189-.395 1.189-1.189v-27.383c0-5.557-1.587-9.922-5.158-13.096-3.57-3.175-7.538-5.16-12.302-5.16zm57.941 36.114c-.396-.793-.793-.793-1.588-.793-2.381 1.189-4.364 1.588-6.35 1.588-4.365 0-6.746-2.779-6.746-7.939v-16.666c0-.795.396-1.191 1.19-1.191h12.698c.795 0 1.19-.396 1.19-1.189v-7.541c0-.793-.396-1.189-1.19-1.189h-12.698c-.794 0-1.19-.396-1.19-1.191v-11.508c0-.793-.396-1.191-1.189-1.191h-9.922c-.795 0-1.19.398-1.19 1.191v11.508c0 .795-.396 1.191-1.19 1.191h-7.539c-.795 0-1.191.396-1.191 1.189v7.541c0 .793.396 1.189 1.191 1.189h7.539c.795 0 1.19.396 1.19 1.191v17.857c0 5.953 1.588 10.715 4.762 13.494 3.177 3.174 7.541 4.365 13.097 4.365 3.572 0 7.145-.793 11.112-2.777.396-.398.794-.795.396-1.191l-2.382-7.938zm41.67-35.321h-2.381c-6.351 0-11.111 2.381-15.08 7.541-.397.396-.794 0-.794-.396v-5.16c0-.793-.794-1.586-1.589-1.586h-9.127c-.793 0-1.189.395-1.189 1.189v43.256c0 .795.396 1.191 1.189 1.191h9.922c.794 0 1.189-.396 1.189-1.191v-13.492c0-13.096 5.557-19.445 16.271-19.445h1.587c.396 0 1.19-.396 1.19-1.191l.396-9.523c-.393-.398-.791-.794-1.584-1.193zm28.176-.793c-6.746 0-12.302 2.381-16.668 6.746-4.365 4.365-6.746 10.318-6.746 17.461 0 7.145 2.381 12.701 6.746 17.066 4.366 4.363 9.922 6.746 16.668 6.746 5.159 0 9.523-1.191 13.493-3.572 3.571-1.984 5.953-5.158 7.938-9.127.396-.795 0-1.191-.794-1.588l-8.729-1.984c-.397 0-.794 0-1.191.396-2.381 3.572-5.952 5.557-10.715 5.557-2.777 0-5.159-.793-7.145-2.777-1.982-1.588-3.175-3.969-3.968-7.145 0-.396 0-.793.396-.793h33.731v-3.969c-.396-6.746-2.776-12.303-7.144-16.668-4.363-3.968-9.522-6.349-15.872-6.349zm9.922 19.049h-20.24c-.396 0-.793-.396-.396-.793.794-2.381 1.983-3.969 3.969-5.557 1.985-1.586 4.365-1.984 6.746-1.984s4.763.793 6.747 1.984c1.587 1.191 3.175 3.176 3.968 5.557-.001.397-.397.793-.794.793zm31.748 8.334h33.732v-3.969c-.396-6.746-2.778-12.303-7.145-16.668s-9.921-6.35-15.874-6.35c-6.746 0-12.303 2.381-16.668 6.748-4.365 4.363-6.746 10.316-6.746 17.461 0 7.143 2.381 12.697 6.746 17.062s9.922 6.748 16.668 6.748c5.159 0 9.524-1.189 13.493-3.572 3.571-1.984 5.953-5.158 7.937-9.127.397-.795 0-1.191-.793-1.588l-8.729-1.984c-.397 0-.794 0-1.191.396-2.381 3.572-5.952 5.557-10.715 5.557-2.777 0-5.159-.795-7.144-2.777-1.984-1.588-3.175-3.969-3.969-7.145-.396-.395.002-.792.398-.792zm0-9.127c.794-2.381 1.984-3.969 3.969-5.557 1.985-1.586 4.365-1.984 6.746-1.984s4.763.793 6.747 1.984c1.587 1.191 3.175 3.176 3.968 5.557 0 .396 0 .793-.396.793h-20.239c-.795 0-1.191-.396-.795-.793zm-289.305-7.144h-31.352c-.794 0-1.19.396-1.19 1.191v9.127c0 .793.396 1.189 1.19 1.189h19.843c.396 0 .793.398.396.795-1.19 3.967-3.175 7.143-6.35 9.523-3.175 2.381-7.144 3.57-11.905 3.57-5.557 0-10.318-1.982-14.287-5.953-3.969-3.969-5.953-8.729-5.953-14.285s1.984-10.318 5.557-14.287c3.571-3.971 8.73-5.953 14.286-5.953 7.144 0 12.699 2.777 16.668 7.936.397.398.794.398 1.588.398l8.334-5.557c.396-.396.793-1.189.396-1.588a22.932 22.932 0 0 0-8.334-7.936c-5.159-3.176-11.509-4.762-17.858-4.762-9.128 0-16.668 3.174-22.621 9.127-6.35 5.953-9.127 13.492-9.127 22.621 0 9.127 3.175 16.668 9.524 22.621 6.35 5.951 13.89 9.127 23.018 9.127 9.127 0 16.271-3.176 22.224-9.127 5.953-5.953 8.73-13.494 8.73-22.621v-4.365c-1.587.004-1.984-.791-2.777-.791z"
          fill="#F0ECE6"
        />
      </svg>
      <span
        class="hide-visually"
      >
        Gumtree
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`Logo component can be smaller 1`] = `
<DocumentFragment>
  <div
    class="logo logo--small"
    title="Gumtree"
  >
    <div
      class="logo-image"
    >
      <svg
        height="24.298"
        viewBox="27.74 5.25 19.52 24.298"
        width="19.52"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M44.433 11.914a.707.707 0 0 1-.337-.606C43.76 7.942 40.933 5.25 37.5 5.25s-6.327 2.625-6.596 6.058a.806.806 0 0 1-.336.606c-1.683 1.211-2.827 3.164-2.827 5.384 0 3.029 2.087 5.654 4.914 6.395.471.135 1.01.202 1.144.067.337-.202.808-1.885.606-2.221-.135-.203-.539-.404-1.077-.539-1.683-.471-2.895-1.952-2.895-3.769 0-1.01.404-1.885 1.01-2.625a2.964 2.964 0 0 1 1.01-.808c.74-.404 1.144-1.144 1.144-1.952 0-.404.067-.808.202-1.211.539-1.548 1.952-2.692 3.702-2.692s3.164 1.144 3.702 2.692c.134.404.202.808.202 1.211 0 .808.403 1.548 1.144 1.952.404.202.673.471 1.01.808a3.967 3.967 0 0 1 1.01 2.625 3.907 3.907 0 0 1-3.903 3.904c-2.491 0-4.443 2.02-4.443 4.51v2.558c0 .471.067 1.009.202 1.144.27.27 2.02.27 2.288 0 .135-.135.203-.673.203-1.144v-2.625c0-.942.807-1.75 1.75-1.75 3.634 0 6.596-2.962 6.596-6.596-.002-2.155-1.147-4.107-2.829-5.318z"
          fill="#72EF36"
        />
      </svg>
    </div>
    <div
      class="logo-text"
    >
      <svg
        height="64.291"
        viewBox="169.06 371.734 383.756 64.291"
        width="383.756"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M281.766 388.8h-9.92c-.794 0-1.19.396-1.19 1.189v19.049c0 10.318-3.572 15.477-11.112 15.477-2.778 0-5.159-.793-6.746-2.777-1.588-1.984-2.382-4.365-2.382-7.539v-23.812c0-.793-.396-1.189-1.19-1.189h-9.921c-.794 0-1.19.396-1.19 1.189v26.588c0 5.557 1.587 10.318 5.158 13.891 3.175 3.572 7.541 5.16 13.097 5.16 5.556 0 9.921-1.588 13.493-4.764.396-.396.794 0 .794.396v1.588c0 .793.396 1.189 1.19 1.189h9.921c.793 0 1.189-.396 1.189-1.189v-43.654c-.001-.395-.396-.792-1.191-.792zm67.861-1.588c-7.143 0-12.699 2.381-16.667 7.541-3.571-4.764-8.334-7.541-15.081-7.541-5.158 0-9.523 1.586-12.699 4.365-.396.396-.793 0-.793-.396v-1.588c0-.793-.396-1.189-1.191-1.189h-9.523c-.793 0-1.189.396-1.189 1.189v43.258c0 .793.396 1.189 1.189 1.189h9.922c.793 0 1.19-.396 1.19-1.189v-20.637c0-4.762.794-7.936 2.776-10.316 1.984-2.383 4.367-3.572 7.541-3.572 2.381 0 4.763.793 6.351 2.777 1.587 1.588 2.381 3.969 2.381 7.143v24.605c0 .795.396 1.189 1.19 1.189h9.922c.793 0 1.189-.395 1.189-1.189v-20.637c0-4.762.793-7.936 2.777-10.316 1.984-2.383 4.365-3.572 7.541-3.572 2.381 0 4.762.793 6.35 2.777 1.588 1.588 2.381 3.969 2.381 7.143v24.605c0 .795.396 1.189 1.19 1.189h9.524c.793 0 1.189-.395 1.189-1.189v-27.383c0-5.557-1.587-9.922-5.158-13.096-3.57-3.175-7.538-5.16-12.302-5.16zm57.941 36.114c-.396-.793-.793-.793-1.588-.793-2.381 1.189-4.364 1.588-6.35 1.588-4.365 0-6.746-2.779-6.746-7.939v-16.666c0-.795.396-1.191 1.19-1.191h12.698c.795 0 1.19-.396 1.19-1.189v-7.541c0-.793-.396-1.189-1.19-1.189h-12.698c-.794 0-1.19-.396-1.19-1.191v-11.508c0-.793-.396-1.191-1.189-1.191h-9.922c-.795 0-1.19.398-1.19 1.191v11.508c0 .795-.396 1.191-1.19 1.191h-7.539c-.795 0-1.191.396-1.191 1.189v7.541c0 .793.396 1.189 1.191 1.189h7.539c.795 0 1.19.396 1.19 1.191v17.857c0 5.953 1.588 10.715 4.762 13.494 3.177 3.174 7.541 4.365 13.097 4.365 3.572 0 7.145-.793 11.112-2.777.396-.398.794-.795.396-1.191l-2.382-7.938zm41.67-35.321h-2.381c-6.351 0-11.111 2.381-15.08 7.541-.397.396-.794 0-.794-.396v-5.16c0-.793-.794-1.586-1.589-1.586h-9.127c-.793 0-1.189.395-1.189 1.189v43.256c0 .795.396 1.191 1.189 1.191h9.922c.794 0 1.189-.396 1.189-1.191v-13.492c0-13.096 5.557-19.445 16.271-19.445h1.587c.396 0 1.19-.396 1.19-1.191l.396-9.523c-.393-.398-.791-.794-1.584-1.193zm28.176-.793c-6.746 0-12.302 2.381-16.668 6.746-4.365 4.365-6.746 10.318-6.746 17.461 0 7.145 2.381 12.701 6.746 17.066 4.366 4.363 9.922 6.746 16.668 6.746 5.159 0 9.523-1.191 13.493-3.572 3.571-1.984 5.953-5.158 7.938-9.127.396-.795 0-1.191-.794-1.588l-8.729-1.984c-.397 0-.794 0-1.191.396-2.381 3.572-5.952 5.557-10.715 5.557-2.777 0-5.159-.793-7.145-2.777-1.982-1.588-3.175-3.969-3.968-7.145 0-.396 0-.793.396-.793h33.731v-3.969c-.396-6.746-2.776-12.303-7.144-16.668-4.363-3.968-9.522-6.349-15.872-6.349zm9.922 19.049h-20.24c-.396 0-.793-.396-.396-.793.794-2.381 1.983-3.969 3.969-5.557 1.985-1.586 4.365-1.984 6.746-1.984s4.763.793 6.747 1.984c1.587 1.191 3.175 3.176 3.968 5.557-.001.397-.397.793-.794.793zm31.748 8.334h33.732v-3.969c-.396-6.746-2.778-12.303-7.145-16.668s-9.921-6.35-15.874-6.35c-6.746 0-12.303 2.381-16.668 6.748-4.365 4.363-6.746 10.316-6.746 17.461 0 7.143 2.381 12.697 6.746 17.062s9.922 6.748 16.668 6.748c5.159 0 9.524-1.189 13.493-3.572 3.571-1.984 5.953-5.158 7.937-9.127.397-.795 0-1.191-.793-1.588l-8.729-1.984c-.397 0-.794 0-1.191.396-2.381 3.572-5.952 5.557-10.715 5.557-2.777 0-5.159-.795-7.144-2.777-1.984-1.588-3.175-3.969-3.969-7.145-.396-.395.002-.792.398-.792zm0-9.127c.794-2.381 1.984-3.969 3.969-5.557 1.985-1.586 4.365-1.984 6.746-1.984s4.763.793 6.747 1.984c1.587 1.191 3.175 3.176 3.968 5.557 0 .396 0 .793-.396.793h-20.239c-.795 0-1.191-.396-.795-.793zm-289.305-7.144h-31.352c-.794 0-1.19.396-1.19 1.191v9.127c0 .793.396 1.189 1.19 1.189h19.843c.396 0 .793.398.396.795-1.19 3.967-3.175 7.143-6.35 9.523-3.175 2.381-7.144 3.57-11.905 3.57-5.557 0-10.318-1.982-14.287-5.953-3.969-3.969-5.953-8.729-5.953-14.285s1.984-10.318 5.557-14.287c3.571-3.971 8.73-5.953 14.286-5.953 7.144 0 12.699 2.777 16.668 7.936.397.398.794.398 1.588.398l8.334-5.557c.396-.396.793-1.189.396-1.588a22.932 22.932 0 0 0-8.334-7.936c-5.159-3.176-11.509-4.762-17.858-4.762-9.128 0-16.668 3.174-22.621 9.127-6.35 5.953-9.127 13.492-9.127 22.621 0 9.127 3.175 16.668 9.524 22.621 6.35 5.951 13.89 9.127 23.018 9.127 9.127 0 16.271-3.176 22.224-9.127 5.953-5.953 8.73-13.494 8.73-22.621v-4.365c-1.587.004-1.984-.791-2.777-.791z"
          fill="#F0ECE6"
        />
      </svg>
      <span
        class="hide-visually"
      >
        Gumtree
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`Logo component renders 1`] = `
<DocumentFragment>
  <div
    class="logo"
    title="Gumtree"
  >
    <div
      class="logo-image"
    >
      <svg
        height="24.298"
        viewBox="27.74 5.25 19.52 24.298"
        width="19.52"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M44.433 11.914a.707.707 0 0 1-.337-.606C43.76 7.942 40.933 5.25 37.5 5.25s-6.327 2.625-6.596 6.058a.806.806 0 0 1-.336.606c-1.683 1.211-2.827 3.164-2.827 5.384 0 3.029 2.087 5.654 4.914 6.395.471.135 1.01.202 1.144.067.337-.202.808-1.885.606-2.221-.135-.203-.539-.404-1.077-.539-1.683-.471-2.895-1.952-2.895-3.769 0-1.01.404-1.885 1.01-2.625a2.964 2.964 0 0 1 1.01-.808c.74-.404 1.144-1.144 1.144-1.952 0-.404.067-.808.202-1.211.539-1.548 1.952-2.692 3.702-2.692s3.164 1.144 3.702 2.692c.134.404.202.808.202 1.211 0 .808.403 1.548 1.144 1.952.404.202.673.471 1.01.808a3.967 3.967 0 0 1 1.01 2.625 3.907 3.907 0 0 1-3.903 3.904c-2.491 0-4.443 2.02-4.443 4.51v2.558c0 .471.067 1.009.202 1.144.27.27 2.02.27 2.288 0 .135-.135.203-.673.203-1.144v-2.625c0-.942.807-1.75 1.75-1.75 3.634 0 6.596-2.962 6.596-6.596-.002-2.155-1.147-4.107-2.829-5.318z"
          fill="#72EF36"
        />
      </svg>
    </div>
    <div
      class="logo-text"
    >
      <svg
        height="64.291"
        viewBox="169.06 371.734 383.756 64.291"
        width="383.756"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M281.766 388.8h-9.92c-.794 0-1.19.396-1.19 1.189v19.049c0 10.318-3.572 15.477-11.112 15.477-2.778 0-5.159-.793-6.746-2.777-1.588-1.984-2.382-4.365-2.382-7.539v-23.812c0-.793-.396-1.189-1.19-1.189h-9.921c-.794 0-1.19.396-1.19 1.189v26.588c0 5.557 1.587 10.318 5.158 13.891 3.175 3.572 7.541 5.16 13.097 5.16 5.556 0 9.921-1.588 13.493-4.764.396-.396.794 0 .794.396v1.588c0 .793.396 1.189 1.19 1.189h9.921c.793 0 1.189-.396 1.189-1.189v-43.654c-.001-.395-.396-.792-1.191-.792zm67.861-1.588c-7.143 0-12.699 2.381-16.667 7.541-3.571-4.764-8.334-7.541-15.081-7.541-5.158 0-9.523 1.586-12.699 4.365-.396.396-.793 0-.793-.396v-1.588c0-.793-.396-1.189-1.191-1.189h-9.523c-.793 0-1.189.396-1.189 1.189v43.258c0 .793.396 1.189 1.189 1.189h9.922c.793 0 1.19-.396 1.19-1.189v-20.637c0-4.762.794-7.936 2.776-10.316 1.984-2.383 4.367-3.572 7.541-3.572 2.381 0 4.763.793 6.351 2.777 1.587 1.588 2.381 3.969 2.381 7.143v24.605c0 .795.396 1.189 1.19 1.189h9.922c.793 0 1.189-.395 1.189-1.189v-20.637c0-4.762.793-7.936 2.777-10.316 1.984-2.383 4.365-3.572 7.541-3.572 2.381 0 4.762.793 6.35 2.777 1.588 1.588 2.381 3.969 2.381 7.143v24.605c0 .795.396 1.189 1.19 1.189h9.524c.793 0 1.189-.395 1.189-1.189v-27.383c0-5.557-1.587-9.922-5.158-13.096-3.57-3.175-7.538-5.16-12.302-5.16zm57.941 36.114c-.396-.793-.793-.793-1.588-.793-2.381 1.189-4.364 1.588-6.35 1.588-4.365 0-6.746-2.779-6.746-7.939v-16.666c0-.795.396-1.191 1.19-1.191h12.698c.795 0 1.19-.396 1.19-1.189v-7.541c0-.793-.396-1.189-1.19-1.189h-12.698c-.794 0-1.19-.396-1.19-1.191v-11.508c0-.793-.396-1.191-1.189-1.191h-9.922c-.795 0-1.19.398-1.19 1.191v11.508c0 .795-.396 1.191-1.19 1.191h-7.539c-.795 0-1.191.396-1.191 1.189v7.541c0 .793.396 1.189 1.191 1.189h7.539c.795 0 1.19.396 1.19 1.191v17.857c0 5.953 1.588 10.715 4.762 13.494 3.177 3.174 7.541 4.365 13.097 4.365 3.572 0 7.145-.793 11.112-2.777.396-.398.794-.795.396-1.191l-2.382-7.938zm41.67-35.321h-2.381c-6.351 0-11.111 2.381-15.08 7.541-.397.396-.794 0-.794-.396v-5.16c0-.793-.794-1.586-1.589-1.586h-9.127c-.793 0-1.189.395-1.189 1.189v43.256c0 .795.396 1.191 1.189 1.191h9.922c.794 0 1.189-.396 1.189-1.191v-13.492c0-13.096 5.557-19.445 16.271-19.445h1.587c.396 0 1.19-.396 1.19-1.191l.396-9.523c-.393-.398-.791-.794-1.584-1.193zm28.176-.793c-6.746 0-12.302 2.381-16.668 6.746-4.365 4.365-6.746 10.318-6.746 17.461 0 7.145 2.381 12.701 6.746 17.066 4.366 4.363 9.922 6.746 16.668 6.746 5.159 0 9.523-1.191 13.493-3.572 3.571-1.984 5.953-5.158 7.938-9.127.396-.795 0-1.191-.794-1.588l-8.729-1.984c-.397 0-.794 0-1.191.396-2.381 3.572-5.952 5.557-10.715 5.557-2.777 0-5.159-.793-7.145-2.777-1.982-1.588-3.175-3.969-3.968-7.145 0-.396 0-.793.396-.793h33.731v-3.969c-.396-6.746-2.776-12.303-7.144-16.668-4.363-3.968-9.522-6.349-15.872-6.349zm9.922 19.049h-20.24c-.396 0-.793-.396-.396-.793.794-2.381 1.983-3.969 3.969-5.557 1.985-1.586 4.365-1.984 6.746-1.984s4.763.793 6.747 1.984c1.587 1.191 3.175 3.176 3.968 5.557-.001.397-.397.793-.794.793zm31.748 8.334h33.732v-3.969c-.396-6.746-2.778-12.303-7.145-16.668s-9.921-6.35-15.874-6.35c-6.746 0-12.303 2.381-16.668 6.748-4.365 4.363-6.746 10.316-6.746 17.461 0 7.143 2.381 12.697 6.746 17.062s9.922 6.748 16.668 6.748c5.159 0 9.524-1.189 13.493-3.572 3.571-1.984 5.953-5.158 7.937-9.127.397-.795 0-1.191-.793-1.588l-8.729-1.984c-.397 0-.794 0-1.191.396-2.381 3.572-5.952 5.557-10.715 5.557-2.777 0-5.159-.795-7.144-2.777-1.984-1.588-3.175-3.969-3.969-7.145-.396-.395.002-.792.398-.792zm0-9.127c.794-2.381 1.984-3.969 3.969-5.557 1.985-1.586 4.365-1.984 6.746-1.984s4.763.793 6.747 1.984c1.587 1.191 3.175 3.176 3.968 5.557 0 .396 0 .793-.396.793h-20.239c-.795 0-1.191-.396-.795-.793zm-289.305-7.144h-31.352c-.794 0-1.19.396-1.19 1.191v9.127c0 .793.396 1.189 1.19 1.189h19.843c.396 0 .793.398.396.795-1.19 3.967-3.175 7.143-6.35 9.523-3.175 2.381-7.144 3.57-11.905 3.57-5.557 0-10.318-1.982-14.287-5.953-3.969-3.969-5.953-8.729-5.953-14.285s1.984-10.318 5.557-14.287c3.571-3.971 8.73-5.953 14.286-5.953 7.144 0 12.699 2.777 16.668 7.936.397.398.794.398 1.588.398l8.334-5.557c.396-.396.793-1.189.396-1.588a22.932 22.932 0 0 0-8.334-7.936c-5.159-3.176-11.509-4.762-17.858-4.762-9.128 0-16.668 3.174-22.621 9.127-6.35 5.953-9.127 13.492-9.127 22.621 0 9.127 3.175 16.668 9.524 22.621 6.35 5.951 13.89 9.127 23.018 9.127 9.127 0 16.271-3.176 22.224-9.127 5.953-5.953 8.73-13.494 8.73-22.621v-4.365c-1.587.004-1.984-.791-2.777-.791z"
          fill="#F0ECE6"
        />
      </svg>
      <span
        class="hide-visually"
      >
        Gumtree
      </span>
    </div>
  </div>
</DocumentFragment>
`;
