import React, { forwardRef, useImperativeHandle } from 'react';
import { useDispatch } from 'react-redux';
import classnames from 'classnames';
import Icon from '../icon/icon';

const Controls = forwardRef<ControlsRef, Props>(
    ({ currentSlide, updateCurrentSlide, itemsShowing, slideTotal, setIsVideo, hasVideo }, ref) => {
        const dispatch = useDispatch();

        const onSlideNext = () => {
            const slide =
                currentSlide <= slideTotal - itemsShowing ? currentSlide + 1 : currentSlide;
            updateCurrentSlide(slide);
            slide === slideTotal && hasVideo && dispatch(setIsVideo(true));
        };

        const onSlidePrev = () => {
            const slide = currentSlide !== 1 ? currentSlide - 1 : 1;
            updateCurrentSlide(slide);
            hasVideo && dispatch(setIsVideo(false));
        };

        useImperativeHandle(ref, () => ({
            onSlideNext,
            onSlidePrev,
        }));

        return (
            <div className="carousel-controls-container">
                <button
                    type="button"
                    tabIndex={0}
                    className={`${classnames({
                        disabled: currentSlide === 1,
                    })} slide-panel prev`}
                    onClick={onSlidePrev}
                    data-q="carouselPrev"
                    aria-label="carousel previous"
                >
                    <Icon type="chevron-l" />
                </button>
                <button
                    type="button"
                    className={`${classnames({
                        disabled: currentSlide > slideTotal - itemsShowing,
                    })} slide-panel next`}
                    onClick={onSlideNext}
                    aria-label="carousel next"
                    data-q="carouselNext"
                >
                    <Icon type="chevron-r" />
                </button>
            </div>
        );
    }
);

Controls.displayName = 'Controls';

interface Props {
    currentSlide: number;
    hasVideo: boolean;
    itemsShowing: number;
    setIsVideo: SetIsVideo;
    slideTotal: number;
    updateCurrentSlide: (newTotal: number) => void;
}

export interface ControlsRef {
    onSlideNext(): void;
    onSlidePrev(): void;
}

type SetIsVideo = (isVideo: any) => {
    type: string;
    payload: {
        isVideo: any;
    };
};

export default Controls;
