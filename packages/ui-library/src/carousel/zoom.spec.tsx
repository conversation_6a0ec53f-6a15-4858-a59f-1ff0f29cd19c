import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { trackV2 } from '@gumtree/shared/src/util/track-ga-event';

import Zoom from './zoom';

jest.mock('@gumtree/shared/src/util/track-ga-event', () => ({
    trackV2: jest.fn(),
}));

type Props = React.ComponentProps<typeof Zoom>;

const renderZoom = (extraProps = {} as Props) => render(<Zoom {...extraProps} />);

describe('Zoom component', () => {
    it('renders the component', () => {
        const SUT = renderZoom();
        expect(SUT.asFragment()).toMatchInlineSnapshot(`
            <DocumentFragment>
              <button
                class="carousel-open"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="icon icon--magnifying-glass-zoom css-0 eom5h670"
                />
              </button>
            </DocumentFragment>
        `);
    });

    it('calls GA event on click', () => {
        renderZoom();
        const button = screen.getByRole('button');
        userEvent.click(button);

        expect(trackV2).toHaveBeenCalledWith('SliderFullScreen', 'loop');
    });

    it('opens dialog at the correct slide number', () => {
        const openDialog = jest.fn();
        renderZoom({ openDialog, currentSlide: 2 });
        const button = screen.getByRole('button');
        userEvent.click(button);

        expect(openDialog).toHaveBeenCalledWith(2);
    });
});
