import React, { useEffect, useState, useRef } from 'react';
import Swipe from 'react-easy-swipe';
import classnames from 'classnames';

import { trackV2 } from '@gumtree/shared/src/util/track-ga-event';
import { trackGA4Event } from '@gumtree/shared/src/util/ga4-shared';
import { DisplayAdSlotDivNames } from '@gumtree/third-party-ads/src/framework/dfp/ad-config/slot-configs';

import { runOnBreakpointChange, getCurrentBreakpoint } from '../utils/breakpoints-service';

import { getKeyCode } from '../utils/key-events-service';

import Slider, { SliderProps } from './slider';
import Controls, { ControlsRef } from './controls';
import Counter from './counter';
import Zoom from './zoom';

import './carousel.scss';
import ConditionalWrapper from '../conditional-wrapper/conditional-wrapper';
import useWindowWidth from '../utils/use-window-size';
import { qaAttribute } from '../utils/qa-service';

export const triggerSlideHandlers = (
    currentSlideIndex: number,
    nextSlideIndex: number,
    slides: SliderProps['slides']
) => {
    const upcomingSlideIndex = nextSlideIndex - 1; // "-1" because parameter index starts at 1
    const upcomingSlide = slides[upcomingSlideIndex];
    const isSlideToNext = nextSlideIndex > currentSlideIndex;

    if (upcomingSlide) {
        if (isSlideToNext && upcomingSlide.onArrivalFromLeft) {
            upcomingSlide.onArrivalFromLeft();
        }
        if (!isSlideToNext && upcomingSlide.onArrivalFromRight) {
            upcomingSlide.onArrivalFromRight();
        }
    }
};

export const updateCounterVisibility = (
    nextSlideIndex: number,
    slides: SliderProps['slides'],
    setIsCounterVisible: React.Dispatch<boolean>
) => {
    const upcomingSlideIndex = nextSlideIndex - 1; // "-1" because parameter index starts at 1
    const upcomingSlide = slides[upcomingSlideIndex];
    setIsCounterVisible(!upcomingSlide?.invisibleToSlideCounter);
};

export const trackSlideChange = (
    prevSlide: number,
    nextSlide: number,
    trackingContext: Props['trackingContext'],
    slides: SliderProps['slides']
) => {
    if (trackingContext === 'VIPImages') {
        const zeroBasedIndex = nextSlide - 1;
        const slide = slides[zeroBasedIndex];
        const isGalleryMpu =
            slide.type === 'display-ad' && slide.slotId === DisplayAdSlotDivNames.galleryMPU;

        trackGA4Event<GA4.NavigateCarousel>({
            event: 'image_scroll',
            linkText: `${nextSlide > prevSlide ? 'next' : 'prev'} ${nextSlide}`,
            imageScrolledTo: nextSlide,
            galleryMpuViewed: isGalleryMpu,
        });
    } else if (trackingContext === 'StaySafe') {
        trackGA4Event<GA4.NavigateSafetyTips>({
            event: 'safety_tips_scroll',
            linkText: `${nextSlide > prevSlide ? 'next' : 'prev'} ${nextSlide}`,
        });
    }
};

/** Carousel for images and text at the base of all other carousels */

const Carousel = ({
    slides,
    hasControls,
    hasCounter,
    hasIcon,
    device,
    onItemClick,
    openDialog,
    hasZoom,
    showNextSlidePreview,
    isDialogOpen = false,
    isSimilarResults,
    hasCounterDots,
    imageAltTitle,
    includeLastSlideInPeek,
    tabs,
    tabContainer: TabContainer,
    showFullScreenOnClick,
    setIsVideo = () => {},
    itemsToShow = 1,
    slideNumber = 1,
    isPeekWidth,
    breakpoints,
    showNextSlidePreviewWidth = 100,
    endTracking = '',
    hasVideo = false,
    trackingContext,
}: Props) => {
    const [currentSlide, setCurrentSlide] = useState(slideNumber);
    const [_itemWidthPercentage, setItemWidthPercentage] = useState<number>(NaN);
    const [carouselItems, setCarouselItems] = useState(itemsToShow);
    const [loaded, setLoaded] = useState(false);
    const [isCounterVisible, setIsCounterVisible] = useState(true);

    const slideControl = useRef<ControlsRef>(null);

    const { width } = useWindowWidth();

    function getItemWidthPercentage() {
        return showNextSlidePreview === true ? showNextSlidePreviewWidth : 100;
    }

    const onSwipeSlide = (direction: 'left' | 'right') => {
        if (!slideControl || !slideControl.current) {
            return;
        }

        if (direction === 'left') {
            slideControl.current.onSlideNext();
        } else {
            slideControl.current.onSlidePrev();
        }
    };

    const setResponsiveItems = () => {
        if (breakpoints.length > 0) {
            const breakpoint = getCurrentBreakpoint();
            breakpoint &&
                breakpoints.forEach((value) => {
                    if (value.size > breakpoint.min && value.size < breakpoint.max) {
                        setItemWidthPercentage(100 / value.items);
                        setCarouselItems(value.items);
                    }
                });
        }
    };

    const getMobilePeek = (shouldReduceWidth, mobile, isPeek) => {
        return shouldReduceWidth && mobile && isPeek ? { width: isPeekWidth } : {};
    };

    const addLoad = () => {
        setLoaded(true);
    };

    const updateCurrentSlide = (nextSlide: number) => {
        triggerSlideHandlers(currentSlide, nextSlide, slides);
        updateCounterVisibility(nextSlide, slides, setIsCounterVisible);
        setCurrentSlide(nextSlide);
        trackSlideChange(currentSlide, nextSlide, trackingContext, slides);
    };

    const handleNavigationKeys = (event) => {
        event.preventDefault();

        if (!slideControl || !slideControl.current) {
            return;
        }

        const arrow = getKeyCode(event);

        if (arrow === 'ArrowLeft') {
            slideControl.current.onSlidePrev();
        } else if (arrow === 'ArrowRight') {
            slideControl.current.onSlideNext();
        }
    };

    const calculateSlideWidth = () => {
        const slide = currentSlide - 1;
        const itemWidthPercentage = getItemWidthPercentage();
        const itemOffset = slide === 0 ? 0 : (100 - itemWidthPercentage) / 2;
        const itemTransform = slide * -(itemWidthPercentage / carouselItems) + itemOffset;

        return `translateX(calc(${itemTransform}%))`;
    };

    const calculateCounterTotal = () => {
        const countableSlides = slides.filter(
            ({ invisibleToSlideCounter }) => !invisibleToSlideCounter
        );
        return Math.ceil(countableSlides.length / carouselItems) + (carouselItems > 1 ? 1 : 0);
    };

    const onSlideNumberChange = (id: number) => {
        updateCurrentSlide(id);
    };

    useEffect(() => {
        setResponsiveItems();
        addLoad();
    }, []);

    useEffect(() => {
        if (breakpoints.length > 0) {
            runOnBreakpointChange(setResponsiveItems);
        }
    }, [width]);

    useEffect(() => {
        if (currentSlide > slides.length - carouselItems) {
            trackV2(endTracking, String(currentSlide));
        }
    }, [currentSlide]);

    const transform = calculateSlideWidth();
    const mobilePeek = getMobilePeek(
        carouselItems === 1
            ? currentSlide !== slides.length || includeLastSlideInPeek
            : currentSlide + 1 !== slides.length,
        device === 'mobile',
        showNextSlidePreview
    );
    const flexBasis = `${getItemWidthPercentage() / carouselItems}%`;

    return (
        <Swipe
            className={`${classnames({
                loaded,
            })} carousel-container`}
            onSwipeLeft={() => onSwipeSlide('left')}
            allowMouseEvents
            onSwipeRight={() => onSwipeSlide('right')}
            {...qaAttribute('carousel')}
        >
            {!!tabs && tabs.length > 0 && (
                <ConditionalWrapper
                    condition={!!TabContainer}
                    wrapper={(wrappedChildren) =>
                        React.createElement(TabContainer!, { currentSlide }, wrappedChildren)
                    }
                >
                    {(props) =>
                        tabs.map((tab, i) => {
                            // each tab is an array of it's id and React node [id,node]
                            const [id, Tab] = tab;
                            return (
                                <Tab
                                    key={id}
                                    onClick={() => updateCurrentSlide(i + 1)}
                                    className={i + 1 === currentSlide ? 'tab-active' : ''}
                                    tabActive={i + 1 === currentSlide}
                                    tabCount={tabs.length}
                                    {...props} // passes through props from tabContainer
                                />
                            );
                        })
                    }
                </ConditionalWrapper>
            )}
            <div
                role="listbox"
                tabIndex={0}
                className="carousel-inner remove-outline-onfocus"
                onKeyDown={handleNavigationKeys}
                data-testid="carousel"
                style={{ transform, ...mobilePeek }}
                {...(isSimilarResults && { 'data-q': 'similarresults' })}
            >
                <Slider
                    slides={slides}
                    currentSlide={currentSlide}
                    isDialogOpen={isDialogOpen}
                    flexBasis={flexBasis}
                    imageAltTitle={imageAltTitle}
                    openLightbox={openDialog}
                    itemsToShow={carouselItems}
                    showNextSlidePreview={
                        device !== 'desktop' && showNextSlidePreview ? true : false
                    }
                    showFullScreenOnClick={showFullScreenOnClick}
                    onClick={onItemClick}
                />
            </div>

            {hasControls && slides.length > 1 && (
                <Controls
                    currentSlide={currentSlide}
                    ref={slideControl}
                    itemsShowing={carouselItems}
                    slideTotal={slides.length}
                    updateCurrentSlide={updateCurrentSlide}
                    setIsVideo={setIsVideo}
                    hasVideo={hasVideo}
                />
            )}
            {(hasCounterDots || hasCounter) && isCounterVisible && (
                <Counter
                    itemsShowing={calculateCounterTotal()}
                    currentSlide={currentSlide}
                    onSlideNumberChange={onSlideNumberChange}
                    hasIcon={hasIcon}
                    hasCounterDots={hasCounterDots}
                />
            )}
            {hasZoom && <Zoom openDialog={openDialog} currentSlide={currentSlide} />}
        </Swipe>
    );
};

export interface Props {
    breakpoints: any[];
    /** tracking action name for when the carousel changes with a click */
    device?: string;
    /** tracking action name when the carousel reaches the end of the carousel */
    endTracking?: string;
    hasControls?: boolean;
    hasCounter?: boolean;
    hasCounterDots?: boolean;
    hasIcon?: boolean;
    hasVideo?: boolean;
    hasZoom?: boolean;
    imageAltTitle?: string;
    /** Stops the last slide from becoming full width when isPeek */
    includeLastSlideInPeek?: boolean;
    isDialogOpen?: boolean;
    isFullScreen?: boolean;
    isPeekWidth?: string;
    isSimilarResults?: boolean;
    itemsToShow?: number;
    onItemClick?: (event: React.MouseEvent, item: any) => void;
    openDialog?(currentSlide?: number): void;
    setIsVideo?(): any;
    showFullScreenOnClick?: boolean;
    /** Shows a portion of the next slide visible in the carousel */
    showNextSlidePreview?: boolean;
    /** % width to show of card in view */
    showNextSlidePreviewWidth?: number;
    slideNumber?: number;
    slides: SliderProps['slides'];
    /** a custom container for the tabs */
    tabContainer?: React.FC<React.PropsWithChildren<{ currentSlide: number }>>;
    /** tab titles for the carousel slides [id, node to show] */
    tabs?: [id: string | undefined, node: React.FC<any>][];
    trackingContext?: CarouselTrackingContext;
}

Carousel.defaultProps = {
    device: 'desktop',
    breakpoints: [],
    hasControls: false,
    hasCounter: false,
    hasCounterDots: false,
    hasIcon: false,
    imageAltTitle: '',
    includeLastSlideInPeek: false,
    isPeekWidth: '95%',
    showFullScreenOnClick: null,
    showNextSlidePreview: false,
    hasZoom: false,
    isFullScreen: false,
    isSimilarResults: false,
    onItemClick: undefined,
    openDialog: () => {},
    tabContainer: null,
    tabs: [],
};

export type CarouselTrackingContext = 'VIPImages' | 'StaySafe';

export default Carousel;
