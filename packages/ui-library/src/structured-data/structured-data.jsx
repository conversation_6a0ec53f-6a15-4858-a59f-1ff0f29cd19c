/* eslint-disable react/no-danger */
import React from 'react';
import PropTypes from 'prop-types';

const StructuredData = ({ data }) => {
    if (!data) {
        return <></>
    }
    
    return <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
    />
};

StructuredData.defaultProps = {
    data: {}
};

StructuredData.propTypes = {
    data: PropTypes.object
};

export default StructuredData;
