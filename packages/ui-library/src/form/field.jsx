// TODO clean
/* eslint-disable jsx-a11y/label-has-for */

import React, { Component } from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';

import Input from './input';
import Select from './select';

import './field.scss';

// Prioritized error types
const ERROR_TYPES = [
    'customError', // error set with setCustomValidity()
    'valid', // any error type
    'badInput', // input that the browser is unable to convert
    'valueMissing', // required
    'typeMismatch', // type
    'patternMismatch', // pattern
    'tooLong', // maxlength
    'tooShort', // minlength
    'rangeOverflow', // max
    'rangeUnderflow', // min
    'stepMismatch', // step
];

class Field extends Component {
    state = {
        errorMessage: '',
        isInvalid: false,
    };

    getErrorMessage = () => {
        const { errorMessages } = this.props;

        // Input is not mounted or validity api is not available
        if (!this.input || !this.input.validity) {
            return '';
        }

        // Current validity state that holds
        // all error types states
        const validityState = this.input.validity;

        // Find most relevant error type to display
        const errorDisplayed = ERROR_TYPES.find((errorType) => validityState[errorType]);

        // Return the most relevant error message
        return errorMessages[errorDisplayed];
    };

    handleInput = (event) => {
        this.setState({
            isInvalid: !(event.target.checkValidity && event.target.checkValidity()),
            errorMessage: this.getErrorMessage(),
        });
        const { onInput } = this.props;
        onInput(event);
    };

    handleInputRef = (element) => {
        this.input = element;
        const { inputRef } = this.props;
        inputRef(element);
    };

    handleInvalid = (event) => {
        this.setState({
            isInvalid: true,
            errorMessage: this.getErrorMessage(),
        });
        const { onInvalid } = this.props;
        onInvalid(event);
    };

    render() {
        // errorMessages is not a valid prop for Input or Select, hence omitting it
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { id, inline, label, labelHidden, options, errorMessages, ...props } = this.props;
        const { errorMessage, isInvalid } = this.state;
        const TagName = !inline ? 'div' : 'span';
        const fieldClasses = classnames('field', {
            'field--inline': inline,
            'is-invalid': isInvalid,
        });
        const Element = !options ? Input : Select;
        const elementProps = {};

        if (options) {
            elementProps.options = options;
        }

        if (labelHidden) {
            elementProps['aria-label'] = label;
        }

        return (
            <TagName className={fieldClasses}>
                {label && !labelHidden && <label htmlFor={id}>{label}</label>}
                <Element
                    {...props}
                    id={id}
                    inline={inline}
                    inputRef={this.handleInputRef}
                    onInput={this.handleInput}
                    onInvalid={this.handleInvalid}
                    {...elementProps}
                />
                {isInvalid && errorMessage && (
                    <TagName className="field-error">{errorMessage}</TagName>
                )}
            </TagName>
        );
    }
}

Field.propTypes = {
    defaultValue: PropTypes.string,
    errorMessages: PropTypes.object,
    id: PropTypes.string.isRequired,
    inline: PropTypes.bool,
    inputRef: PropTypes.func,
    label: PropTypes.string,
    labelHidden: PropTypes.bool,
    onInput: PropTypes.func,
    onInvalid: PropTypes.func,
    options: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
    value: PropTypes.string,
};

Field.defaultProps = {
    defaultValue: undefined,
    errorMessages: {},
    inline: false,
    inputRef: () => {},
    label: null,
    labelHidden: false,
    onInput: () => {},
    onInvalid: () => {},
    options: null,
    value: '',
};

export default Field;
