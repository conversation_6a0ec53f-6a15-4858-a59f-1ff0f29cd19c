@import "../base/color-variables.scss";
@import "../base/grid.scss";
@import "./input.scss";

.select {
    @extend .input-text;
    appearance: none;
    background-color: $branch--10;

    & + .icon {
        position: absolute;
        padding: $gutter-size--medium;
        top: 0;
        right: 0;
        pointer-events: none;
    }

    &:disabled + .icon {
        color: $bark--40;
    }

    &::-ms-expand {
        display: none;
    }
}
