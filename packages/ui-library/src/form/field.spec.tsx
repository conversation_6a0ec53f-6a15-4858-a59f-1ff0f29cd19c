import React from 'react';
import { render, screen } from '@testing-library/react';

import Field from './field';

type Props = React.ComponentProps<typeof Field>;

const defaultProps = {
    id: 'field-id'
}

const renderField = (extraProps = {} as Props) => render(<Field {...defaultProps} {...extraProps} />);

describe('Field component', () => {
    it('renders', () => {
        const SUT = renderField({ id: 'field', title: 'field title', label: 'field label' });
        expect(SUT.asFragment()).toMatchSnapshot();
    });

    it('has a label', () => {
        renderField({ id: 'field', title: 'field title', label: 'field label' });
        expect(screen.getByRole('textbox', { name: 'field label' })).toBeInTheDocument();
    });

    it('can have no label', () => {
        renderField({ id: 'field', title: 'field title' });
        expect(screen.queryByRole('textbox', { name: 'field label' })).not.toBeInTheDocument();
    });

    it('can have a hidden label', () => {
        renderField({ id: 'field', title: 'field title', label: 'field label', labelHidden: true });
        expect(screen.getByRole('textbox', { name: 'field label' })).toBeInTheDocument();
    });

    it('can be a select dropdown', () => {
        renderField({ options: ['7', '6', '3'], label: 'field label' });
        expect(screen.getByRole('option', { name: '7' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: '6' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: '3' })).toBeInTheDocument();
    });
});
