import { render } from '@testing-library/react';
import React from 'react';
import SelectModal from './select-modal';

describe('select-modal', () => {
    it('render', () => {
        const rendered = render(
            <SelectModal
                triggerText="Select your feature"
                modalHeader={<div>This is header</div>}
                elName="elName"
                selectedValue={null}
                setSelectedValue={() => {}}
                selectedDisplayName={null}
                setSelectedDisplayName={() => {}}
                setHasSelectedValue={() => {}}
                isInvalid={false}
                setIsInvalid={() => {}}
                handleFilter={() => {}}
                setSearchTerm={() => {}}
            >
                <h1>Hello World!</h1>
            </SelectModal>
        );

        expect(rendered.asFragment()).toMatchSnapshot();
    });
});
