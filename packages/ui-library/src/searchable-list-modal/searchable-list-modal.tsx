import React, { useState } from 'react';
import { ListData } from '../radio-list/radio-list';
import SelectModal from '../select-modal/select-modal';
import { qaAttribute } from '../utils/qa-service';
import {
    SearchableListFormLabel,
    SearchableListFormRow,
    SelectModalTrigger,
} from './searchable-list-modal.style';

const SearchableListModal = ({
    fieldTitle,
    mandatory,
    modalTitle,
    placeholder,
    triggerText,
    listTitle,
    listData,
    listName,
    elName,
    selectedValue,
    selectedDisplayName,
    isInvalid,
    ga4EventSearch,
    saveChanges,
    className,
    tooltipMessage,
    noResultsImg,
}: {
    fieldTitle: string;
    mandatory?: boolean;
    modalTitle: string;
    placeholder: string;
    triggerText: string;
    listTitle: string;
    listData: ListData;
    listName: string;
    elName: string;
    selectedValue: string | undefined;
    selectedDisplayName: string | undefined;
    isInvalid: boolean;
    ga4EventSearch: Function;
    saveChanges: Function;
    className?: string;
    tooltipMessage?: string | React.ReactNode;
    noResultsImg?: React.ReactNode;
}) => {
    const [isModalOpen, setIsModalOpen] = useState(false);

    const onCloseModal = () => {
        setIsModalOpen(false);
    };

    const onSave = (selectedValue, selectedDisplayName) => {
        setIsModalOpen(false);

        saveChanges(selectedValue, selectedDisplayName);
    };

    return (
        <SearchableListFormRow className={className}>
            <SearchableListFormLabel {...qaAttribute(`${elName}-field-title`)}>
                {fieldTitle} {mandatory && <sup>*</sup>}
            </SearchableListFormLabel>
            <SelectModalTrigger
                type="button"
                onClick={() => setIsModalOpen(true)}
                selectedDisplayName={selectedDisplayName}
                isInvalid={isInvalid}
                {...qaAttribute(`${elName}-modal-trigger`)}
            >
                {selectedDisplayName ? selectedDisplayName : triggerText}
            </SelectModalTrigger>
            {isModalOpen && (
                <SelectModal
                    elName={elName}
                    selectedValue={selectedValue}
                    isModalOpen={isModalOpen}
                    onCloseModal={onCloseModal}
                    noResultsImg={noResultsImg}
                    tooltipMessage={tooltipMessage}
                    listTitle={listTitle}
                    listData={listData}
                    listName={listName}
                    modalTitle={modalTitle}
                    placeholder={placeholder}
                    ga4EventSearch={ga4EventSearch}
                    onSave={onSave}
                />
            )}
        </SearchableListFormRow>
    );
};

export default SearchableListModal;
