import { getContainerStyle, getImageStyle } from './image-service';

describe('Image service', () => {
    describe('getContainerStyle', () => {
        it('default empty styles', () => {
            const SUT = getContainerStyle();
            expect(SUT).toEqual({});
        });

        it('height style', () => {
            const height = 200;
            const SUT = getContainerStyle(height, undefined);
            expect(SUT).toHaveProperty('height', `${height}px`);
            expect(SUT).not.toHaveProperty('width');
        });

        it('width style', () => {
            const width = 100;
            const SUT = getContainerStyle(undefined, width);
            expect(SUT).toHaveProperty('width', `${width}px`);
            expect(SUT).not.toHaveProperty('height');
        });

        it('height and width styles', () => {
            const height = 200;
            const width = 100;
            const SUT = getContainerStyle(height, width);
            expect(SUT).toHaveProperty('height', `${height}px`);
            expect(SUT).toHaveProperty('width', `${width}px`);
        });
    });

    describe('getImageStyle', () => {
        it('default empty styles', () => {
            const SUT = getImageStyle();
            expect(SUT).toEqual({});
        });

        it('when no image present', () => {
            const container = { offsetWidth: 300, offsetHeight: 300 };
            const SUT = getImageStyle(container);
            expect(SUT).toEqual({});
        });

        it('when no container present', () => {
            const image = { offsetWidth: 300, offsetHeight: 300 };
            const SUT = getImageStyle(undefined, image);
            expect(SUT).toEqual({});
        });

        it('when container ratio == image ratio and image width == container width', () => {
            const container = { offsetWidth: 300, offsetHeight: 300 };
            const image = { offsetWidth: 300, offsetHeight: 300 };
            const SUT = getImageStyle(container, image);
            expect(SUT).toEqual({ minHeight: '100%' });
        });

        it('when container ratio == image ratio and image width > container width', () => {
            const container = { offsetWidth: 300, offsetHeight: 300 };
            const image = { offsetWidth: 400, offsetHeight: 400 };
            const SUT = getImageStyle(container, image);
            expect(SUT).toEqual({ maxHeight: '100%' });
        });

        it('when container ratio == image ratio and image width < container width', () => {
            const container = { offsetWidth: 300, offsetHeight: 300 };
            const image = { offsetWidth: 200, offsetHeight: 200 };
            const SUT = getImageStyle(container, image);
            expect(SUT).toEqual({ minHeight: '100%' });
        });

        it('when container ratio > image ratio and image width == container width', () => {
            const container = { offsetWidth: 400, offsetHeight: 300 };
            const image = { offsetWidth: 400, offsetHeight: 400 };
            const SUT = getImageStyle(container, image);
            expect(SUT).toEqual({  minWidth: '100%' });
        });

        it('when container ratio > image ratio and image width > container width', () => {
            const container = { offsetWidth: 400, offsetHeight: 300 };
            const image = { offsetWidth: 500, offsetHeight: 500 };
            const SUT = getImageStyle(container, image);
            expect(SUT).toEqual({ maxWidth: '100%' });
        });

        it('when container ratio > image ratio and image width < container width', () => {
            const container = { offsetWidth: 400, offsetHeight: 300 };
            const image = { offsetWidth: 300, offsetHeight: 300 };
            const SUT = getImageStyle(container, image);
            expect(SUT).toEqual({ minWidth: '100%' });
        });

        it('when container ratio < image ratio and image height == container height', () => {
            const container = { offsetWidth: 300, offsetHeight: 400 };
            const image = { offsetWidth: 400, offsetHeight: 400 };
            const SUT = getImageStyle(container, image);
            expect(SUT).toEqual({ minHeight: '100%' });
        });

        it('when container ratio < image ratio and image height > container height', () => {
            const container = { offsetWidth: 300, offsetHeight: 400 };
            const image = { offsetWidth: 450, offsetHeight: 450 };
            const SUT = getImageStyle(container, image);
            expect(SUT).toEqual({ maxHeight: '100%' });
        });

        it('when container ratio < image ratio and image height < container height', () => {
            const container = { offsetWidth: 300, offsetHeight: 400 };
            const image = { offsetWidth: 300, offsetHeight: 300 };
            const SUT = getImageStyle(container, image);
            expect(SUT).toEqual({ minHeight: '100%' });
        });
    });
});
