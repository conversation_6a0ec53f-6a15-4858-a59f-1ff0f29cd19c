import React from 'react';
import { render, screen } from '@testing-library/react';
import Portal from './portal';

const mockTestId = "mock-test-id";
const testElement = <div data-testid={mockTestId}>mocked div</div>

describe('Portal component', () => {
    it('renders child elements', () => {
        render(<Portal>{testElement}</Portal>);
        const childElement = screen.getByTestId(mockTestId);
        expect(childElement).toBeTruthy();
    })

    it('renders as a last element in document body', () => {
        render(<Portal>{testElement}</Portal>);
        const bodyElements = document.body.children;
        const lastBodyChild = bodyElements[bodyElements.length - 1];
        const portalContent = lastBodyChild.querySelector(`[data-testid="${mockTestId}"]`);
        expect(portalContent).toBeTruthy();
    })
})
