import { FC, useState, useLayoutEffect } from "react";
import { createPortal } from "react-dom";

interface PortalProps {
    /** Represents content that will be rendered by react portal */
    children: React.ReactNode;
}

const Portal: FC<PortalProps> = ({ children }) => {
    const [container] = useState(() => document.createElement('div'));

    useLayoutEffect(() => {
        document.body.appendChild(container);
        return () => {
            document.body.removeChild(container);
        }
    }, [])

    return createPortal(children, container);
};

export default Portal;
