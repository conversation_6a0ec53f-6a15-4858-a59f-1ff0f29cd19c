import { css } from "@emotion/react";
import { boxSizes, colors, fontSizes, fontWeights, gutterSizes } from "../base/theme";

export const buttonCss = ({
    display,
    dark,
    bottomLabel,
    fullWidth,
    fullFlex,
    onlyIcon,
    rightIcon,
    color,
    ...theme
}) => css`
    display: inline-flex;
    appearance: none;
    box-sizing: border-box;
    cursor: pointer;
    align-items: center;
    text-align: center;
    user-select: none;
    white-space: nowrap;
    text-transform: none;
    overflow: visible;

    background-color: transparent;
    border: ${boxSizes.borderWidth} solid transparent;
    border-radius: ${boxSizes.borderRadius};
    color: ${colors.bark};
    font-size: ${fontSizes.base};
    font-weight: ${fontWeights.normal};
    height: ${boxSizes.boxHeight};
    line-height: 20px;
    padding: ${gutterSizes.medium10} ${gutterSizes.medium};
    transition: background-color 0.222s ease-out;

    .icon {
        margin-right: ${gutterSizes.small};
        position: relative;
        top: 0;
        vertical-align: middle;
    }

    &:active,
    &:hover {
        color: ${colors.barkDark};
    }

    & + & {
        margin-left: ${gutterSizes.large};
    }

    ${dark &&
    css`
        color: ${colors.white};
        &:active,
        &:hover {
            color: ${colors.bark40};
        }
    `}

    /* Display variants using Emotion conditional logic */
    ${display === "primary" &&
    css`
        color: ${theme.palette.primary.mainContrastText};
        background-color: ${theme.palette.primary.main};
        border-color: transparent;
        &:active,
        &:hover {
            color: ${theme.palette.primary.darkContrastText};
            background-color: ${theme.palette.primary.dark};
        }
    `}

    ${display === "secondary" &&
    css`
        color: ${theme.palette.secondary.mainContrastText};
        background-color: ${theme.palette.secondary.main};
        border-color: ${theme.palette.secondary.mainContrastText};
        &:active,
        &:hover {
            color: ${theme.palette.secondary.darkContrastText};
            background-color: ${theme.palette.secondary.dark};
        }
    `}
    ${display === "tertiary" &&
    css`
        color: ${theme.palette.secondary.mainContrastText};
        background-color: ${theme.palette.secondary.main};
        border-color: ${theme.palette.secondary.mainContrastText};
        &:active,
        &:hover {
            color: ${theme.palette.secondary.darkContrastText};
            background-color: ${theme.palette.secondary.dark};
        }
    `}
    ${display === "quarternary" &&
    css`
        color: ${theme.palette.secondary.mainContrastText};
        background: ${theme.palette.secondary.main};
        transition: background-color 222ms ease-out;
        border: 1px solid ${theme.palette.secondary.mainContrastText};
        &.selected,
        &:hover {
            background: ${theme.palette.secondary.dark};
            color: ${theme.palette.secondary.darkContrastText};
        }
    `}

    &[disabled] {
        color: ${theme.palette.disabled.mainContrastText};
        background-color: ${theme.palette.disabled.main};
        border-color: ${theme.palette.disabled.border};
    }

    ${fullFlex &&
    css`
        display: flex;
        width: 100%;
    `}

    ${fullWidth &&
    css`
        display: block;
        width: 100%;
        & + & {
            margin-left: 0;
            margin-top: ${gutterSizes.large};
        }
    `} 

    ${onlyIcon &&
    css`
        .icon {
            margin-right: 0;
        }
        & + & {
            margin-left: ${gutterSizes.small};
        }
    `} 

    ${bottomLabel &&
    css`
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        height: auto;
        font-size: ${fontSizes.small14};
        line-height: 1;
        .icon {
            margin-right: 0;
            margin-bottom: ${gutterSizes.small};
            top: 0;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    `} 

    ${rightIcon &&
    css`
        .icon {
            margin-right: 0;
            margin-left: ${gutterSizes.small};
        }
    `} 

    ${color === "green" &&
    css`
        color: ${colors.leaf};
    `}
`;
