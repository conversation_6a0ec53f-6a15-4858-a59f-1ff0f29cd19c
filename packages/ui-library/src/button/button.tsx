import classnames from 'classnames';
import React, { ReactNode, FC } from 'react';
import { useTheme } from '@emotion/react';
import Spinner from '../spinner/spinner';
import { buttonCss } from './button.style';

/** Base button component used with different themes and can have icons too */
const Button: FC<Props> = ({
    bottomLabel = false,
    className = '',
    color = '',
    dark = false,
    disabled = false,
    display = null,
    fullWidth = false,
    fullFlex = false,
    href,
    icon = null,
    iconPosition = 'left',
    isLoading,
    label = '',
    loadingText = 'Loading...',
    noFollow,
    onClick = () => {},
    secondIcon = null,
    type = null,
    shouldOpenNewTab = false,
    tabIndex,
    ...props
}) => {
    const theme = useTheme();

    const themedStyles = buttonCss({
        ...theme,
        display,
        dark,
        bottomLabel,
        fullWidth,
        fullFlex,
        onlyIcon: icon && !label,
        rightIcon: iconPosition === 'right',
        color,
    });
    const Elem = href ? 'a' : 'button';

    const classes = classnames(
        {
            button: true,
            [`button--${display}`]: display,
        },
        className
    );

    return (
        <Elem
            className={classes}
            disabled={disabled}
            href={href}
            rel={`${noFollow ? 'nofollow ' : ''}noopener noreferrer`}
            {...(shouldOpenNewTab && href ? { target: '_blank' } : {})}
            onClick={onClick}
            type={type || undefined}
            tabIndex={tabIndex}
            {...props}
            css={themedStyles}
        >
            {(!iconPosition || iconPosition === 'left') && icon}
            {isLoading ? loadingText : label}
            {iconPosition === 'right' && icon}
            {secondIcon}
            {isLoading && <Spinner />}
        </Elem>
    );
};

export interface Props extends React.HTMLAttributes<HTMLButtonElement | HTMLAnchorElement> {
    bottomLabel?: boolean;
    className?: string;
    color?: string;
    dark?: boolean;
    disabled?: boolean;
    display?: 'primary' | 'secondary' | 'tertiary' | 'quarternary' | null;
    fullWidth?: boolean;
    fullFlex?: boolean;
    href?: string;
    icon?: ReactNode | null;
    id?: string;
    secondIcon?: ReactNode | null;
    iconPosition?: 'left' | 'right';
    label?: string | React.ReactElement;
    noFollow?: boolean;
    onClick?: React.MouseEventHandler;
    type?: 'button' | 'submit' | 'reset' | null;
    shouldOpenNewTab?: boolean;
    isLoading?: boolean;
    loadingText?: string;
    tabIndex?: number;
}

export default Button;
