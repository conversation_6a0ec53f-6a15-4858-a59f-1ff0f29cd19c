import styled from "@emotion/styled";

import { mediaQuery, breakpoints, gutterSizes, fontSizes, colors } from "../base/theme";

export const WizardContainer = styled.div`
    margin-bottom: ${gutterSizes.large};
    justify-content: center;
    align-items: center;
`;

export const Name = styled.span`
    font-size: ${fontSizes.smaller};
    width: 120px;
    text-align: center;
    ${mediaQuery.from(breakpoints.large)} {
        font-size: ${fontSizes.large};
        width: 200px;
    }
`;

export const WizardStepContainer = styled.div<{
    selected: boolean;
    isNextStageSelected: boolean;
    isLast: boolean;
}>`
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 120px;
    ${mediaQuery.from(breakpoints.large)} {
        width: 200px;
    }
    ${(props) =>
        props.isLast !== true &&
        `&:after {
            content: "";
            display: block;
            position: relative;
            top: -16px;
            left: 60px;
            width: 64px;
            border-bottom: 4px dashed ${colors.blade10};
            ${mediaQuery.from(breakpoints.large)} {
                left: 100px;
                width: 150px;
            }
        }
    `}

    ${(props) =>
        props.selected &&
        props.isLast !== true &&
        `&:after {
            top: -30px;
            left: 66px;
            width: 54px;
            ${mediaQuery.from(breakpoints.large)} {
                left: 107px;
                width: 135px;
            }
        }
    `}

    ${(props) =>
        props.isNextStageSelected &&
        `&:after {
            top: -16px;
            left: 53px;
            width: 57px;
            ${mediaQuery.from(breakpoints.large)} {
                left: 92px;
                width: 140px;
            }
        }
    `}
`;

const SelectedDiameter = 54;
const Diameter = 31;

export const NumberCircle = styled.div<{
    selected: boolean;
    complete: boolean;
    pending?: boolean;
    failed?: boolean;
}>`
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    width: ${(props) => (props.selected ? `${SelectedDiameter}px` : `${Diameter}px`)};
    height: ${(props) => (props.selected ? `${SelectedDiameter}px` : `${Diameter}px`)};
    padding: ${gutterSizes.small};

    text-align: center;
    color: ${colors.white};
    background-color: ${colors.blade};
    box-shadow: 0 0 0 3px ${colors.white};

    font-size: 16px;
    line-height: 19px;
    font-weight: bold;
    color: ${colors.white};

    ${(props) =>
        props.selected === true &&
        `background: ${colors.leafDark};
        border: 3px solid ${colors.green};
        font-size: ${fontSizes.xlarger};
    `}

    ${(props) =>
        props.complete === true &&
        `background: ${colors.green};
    `}

    ${(props) =>
        props.failed === true &&
        `background: ${colors.berryDark};
        border: 3px solid ${colors.berry};
    `}

    ${(props) =>
        props.pending === true &&
        `background: ${colors.blue};
        border: 3px solid ${colors.blue40};
    `}
`;

export const Stages = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
`;
