import React from 'react';
import { FeatureType } from '../ad-feature-card/ad-feature-card';
import HintTooltip from '../hint-tooltip/hint-tooltip';
import { ExpiresContainer } from './ad-feature-expires.style';

const AdFeatureExpires = ({
    type,
    expires,
    bumpTimes,
}: {
    type: FeatureType;
    expires?: any;
    bumpTimes?: number;
}) => {
    const numOfDaysLeft = expires?.includes('left') ? expires.replace(' left', '') : '';
    const bumpUpLabel = `Becomes available 1h after ${bumpTimes === 0 ? 'posting' : 'bump up'}`;

    const getHintText = (type) => {
        switch (type) {
            case 'featured':
                return `Your ad is already in the section at the top of category page in the list of Featured ads. You will be able to make it Featured again in ${numOfDaysLeft}.`;
            case 'urgent':
                return `Your ad already has an Urgent label. The option to make your ad Urgent again will become available in ${numOfDaysLeft}.`;
            case 'spotlight':
                return numOfDaysLeft
                    ? `Your ad already appears in the list of Spotlight ads on the Homepage. The option to make your ad Spotlight again will become available in ${numOfDaysLeft}.`
                    : 'Please add at least 1 image to your ad to be able to Spotlight it.';
            case 'bumpup':
                return bumpTimes === 0
                    ? 'You will be able to bump up your ad 1h after posting. This will move your ad to the top of search results as if it was just posted.'
                    : 'Your ad was Bumped up recently. You will be able to bump it up again 1h after last bump up.';
            default:
                return '';
        }
    };

    return (
        <ExpiresContainer data-testid="expires">
            <span>{type === 'bumpup' ? bumpUpLabel : expires}</span>
            <HintTooltip
                content={getHintText(type)}
                triggerColor="grey"
                closeBtnColor="lightGrey"
                theme="white"
                elName={`${type}-expires`}
                hasCloseBtn={false}
                align="right"
            />
        </ExpiresContainer>
    );
};

export default AdFeatureExpires;
