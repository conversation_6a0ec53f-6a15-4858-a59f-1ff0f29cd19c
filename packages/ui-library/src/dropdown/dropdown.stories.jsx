import React from 'react';

import Dropdown from './dropdown';

const links = (sellerUrl) => [
    {
        description: 'Motors',
        href: `${sellerUrl}/postad`,
        ariaLabel: 'Post a motors ad',
        gaEvent: '',
        icon: 'car',
    },
    {
        description: 'For Sale',
        href: `${sellerUrl}/postad`,
        ariaLabel: 'Post an advert in all categories',
        gaEvent: '',
        icon: 'tag',
    },
    {
        description: 'Property',
        href: `${sellerUrl}/postad`,
        ariaLabel: 'Post a property ad',
        gaEvent: '',
        icon: 'house',
    },
    {
        description: 'Jobs',
        href: `${sellerUrl}/postad`,
        ariaLabel: 'Post a job ad',
        gaEvent: '',
        icon: 'tie',
    },
    {
        description: 'Services',
        href: `${sellerUrl}/postad`,
        ariaLabel: 'Post a services ad',
        gaEvent: '',
        icon: 'spanner',
    },
    {
        description: 'Community',
        href: `${sellerUrl}/postad`,
        ariaLabel: 'Post a community ad',
        gaEvent: '',
        icon: 'people',
    },
    {
        description: 'Pets',
        href: `${sellerUrl}/postad`,
        ariaLabel: 'Post a pets ad',
        gaEvent: '',
        icon: 'paw',
    },
];

export default {
    title: 'Navigation/dropdown',
    component: Dropdown,
};

export const Default = () => (
    <Dropdown links={links('https://my.gumtree.com')}>Click me to toggle dropdown</Dropdown>
);
