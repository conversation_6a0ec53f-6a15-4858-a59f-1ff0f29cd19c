import React from 'react';
import { useFeatureValue } from '@growthbook/growthbook-react';
import { GrowthBookFeature } from '@gumtree/shared/src/model/experiment';
import {
    getUrgentLightboxText,
    getUrgentLightboxTitle,
} from '@gumtree/shared/src/constants/placing';
import Lightbox from '../../lightbox/lightbox';

import newPromoteUrgentJpg from './img/new-promote-urgent.jpg';
import appViewPromoteUrgentJpg from './img/app-view-promote-urgent.png';

export default function UrgentLightbox({ isOpen, close }: Readonly<Props>) {
    const newFeatureCopyVariant = useFeatureValue(GrowthBookFeature.NEW_FEATURES_COPY, 'A');
    const title = getUrgentLightboxTitle(newFeatureCopyVariant);
    const description = getUrgentLightboxText(newFeatureCopyVariant);

    return (
        <Lightbox
            isOpen={isOpen}
            onClose={close}
            title={title}
            description={description}
            webImage={newPromoteUrgentJpg}
            appViewImage={appViewPromoteUrgentJpg}
            qaAttributePrefix="urgent"
        />
    );
}

interface Props {
    isOpen: boolean;
    close(): void;
}
