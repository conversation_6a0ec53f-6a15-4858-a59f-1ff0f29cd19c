import React, { useState } from 'react';
import { useFeatureValue } from '@growthbook/growthbook-react';
import {
    getBumpUpCTA,
    getFeaturedCTA,
    getFeaturedEffectivenessByProduct,
    getSpotlightCTA,
    getUrgentCTA,
} from '@gumtree/shared/src/constants/placing';
import type { PriceMetadataValue } from '@gumtree/shared/src/types/syi';
import { GrowthBookFeature } from '@gumtree/shared/src/model/experiment';

import { AdFeatureLabel } from '../ad-feature-label/ad-feature-label';
import {
    AdFeatureCardContainer,
    BottomContainer,
    Price,
    PricesContainer,
    PricesDropdownContainer,
    Recommended,
    RecommendedDesktopContainer,
    RecommendedMobileContainer,
    TopContainer,
} from './ad-feature-card.style';
import Checkbox from '../checkbox/checkbox';
import BoltIcon from './bolt-icon';
import ThemeAwareButton from '../theme-aware-button/theme-aware-button';
import FeaturedLightbox from './lightbox/featured-lightbox';
import UrgentLightbox from './lightbox/urgent-lightbox';
import SpotlightLightbox from './lightbox/spotlight-lightbox';
import BumpUpLightbox from './lightbox/bump-up-lightbox';
import { Select } from '../select/select';
import { qaAttribute } from '../utils/qa-service';
import AdFeatureExpires from '../ad-feature-expires/ad-feature-expires';

export default function AdFeatureCard({
    adId,
    type,
    expires,
    prices,
    isLocked = false,
    isRecommended = false,
    bumpTimes,
    onChange,
}: {
    adId: string;
    type: FeatureType;
    expires?: any;
    prices: Price[];
    isLocked?: boolean;
    isRecommended?: boolean;
    bumpTimes?: number;
    onChange: Function;
}) {
    const newFeatureCopyVariant = useFeatureValue(GrowthBookFeature.NEW_FEATURES_COPY, 'A');

    const [descriptionNum, setDescriptionNum] = useState(
        getFeaturedEffectivenessByProduct('FEATURE_3_DAY', newFeatureCopyVariant)
    );
    const [isViewingExample, setIsViewingExample] = useState(false);
    const Lightbox = viewExampleLightboxes[type];

    const featureCheckbox = React.useRef<HTMLInputElement>(null);
    const priceSelect = React.useRef<HTMLSelectElement>(null);

    const onOptionChange = () => {
        const isFeatureSelected = featureCheckbox?.current?.checked;

        let selectedPrice;

        if (isFeatureSelected) {
            const isMultiSelect = prices.length > 1;

            if (isMultiSelect) {
                selectedPrice = prices.find((price) => price.id === priceSelect?.current?.value);
            } else {
                selectedPrice = prices[0];
            }
        }

        onChange(adId, type, selectedPrice);
    };

    const forceCheckboxChange = () => {
        if (!isLocked && featureCheckbox && featureCheckbox.current) {
            featureCheckbox.current.checked = !featureCheckbox.current.checked;
            onOptionChange();
        }
    };

    const descriptions = {
        featured: getFeaturedCTA(newFeatureCopyVariant, descriptionNum),
        urgent: getUrgentCTA(newFeatureCopyVariant),
        spotlight: getSpotlightCTA(newFeatureCopyVariant),
        bumpup: getBumpUpCTA(),
    };

    return (
        <AdFeatureCardContainer {...qaAttribute('ad-feature-card-container')} isLocked={isLocked}>
            <TopContainer>
                <Checkbox
                    id={`${adId}_${type}`}
                    disabled={isLocked}
                    ref={featureCheckbox}
                    data-q={`promote-ad-checkbox-${type}`}
                    onChange={() => {
                        onOptionChange();
                    }}
                />
                <AdFeatureLabel
                    type={type}
                    isLocked={isLocked}
                    onClick={() => {
                        forceCheckboxChange();
                    }}
                />
                {isRecommended && (
                    <RecommendedDesktopContainer
                        onClick={() => {
                            forceCheckboxChange();
                        }}
                    >
                        <Recommended
                            isLocked={isLocked}
                            className="recommended-flag"
                            data-testid="recommended-flag"
                        >
                            <BoltIcon />
                            Recommended
                        </Recommended>
                    </RecommendedDesktopContainer>
                )}
                {(expires || (type === 'bumpup' && isLocked)) && (
                    <AdFeatureExpires type={type} expires={expires} bumpTimes={bumpTimes} />
                )}
                {!expires && !isLocked && (
                    <PricesContainer>
                        {prices.length === 1 && (
                            <>
                                <span>{prices[0].label && `${prices[0].label} - `}</span>
                                <Price>£{prices[0].value.toFixed(2)}</Price>
                            </>
                        )}

                        {prices.length >= 2 && (
                            <PricesDropdownContainer>
                                <Select
                                    ref={priceSelect}
                                    onChange={(e) => {
                                        setDescriptionNum(
                                            getFeaturedEffectivenessByProduct(
                                                e.currentTarget.value as PriceMetadataValue,
                                                newFeatureCopyVariant
                                            )
                                        );
                                        onOptionChange();
                                    }}
                                    data-q="promote-ad-prices-dropdown"
                                >
                                    {prices.map((price) => {
                                        return (
                                            <option key={price.id} value={price.id}>
                                                {getLabelForPrice(price)}
                                            </option>
                                        );
                                    })}
                                </Select>
                            </PricesDropdownContainer>
                        )}
                    </PricesContainer>
                )}
            </TopContainer>
            <BottomContainer>
                {isRecommended && (
                    <RecommendedMobileContainer
                        onClick={() => {
                            forceCheckboxChange();
                        }}
                    >
                        <Recommended
                            isLocked={isLocked}
                            className="recommended-flag"
                            data-testid="recommended-flag"
                        >
                            <BoltIcon />
                            Recommended
                        </Recommended>
                    </RecommendedMobileContainer>
                )}
                <div>{descriptions[type]}</div>
                <ThemeAwareButton
                    type="link"
                    onClick={() => {
                        setIsViewingExample(true);
                    }}
                >
                    View example
                </ThemeAwareButton>
            </BottomContainer>
            <Lightbox
                isOpen={isViewingExample}
                close={() => {
                    setIsViewingExample(false);
                }}
            />
        </AdFeatureCardContainer>
    );
}

const viewExampleLightboxes = {
    featured: FeaturedLightbox,
    urgent: UrgentLightbox,
    spotlight: SpotlightLightbox,
    bumpup: BumpUpLightbox,
};

function getLabelForPrice(price: Price) {
    let singlePriceText = price.label ? `${price.label} - ` : '';
    singlePriceText += `£${price.value.toFixed(2)}`;
    return singlePriceText;
}

export type Price = {
    id: string;
    label: string;
    value: number;
};

export type FeatureType = 'featured' | 'urgent' | 'spotlight' | 'bumpup';
