import React from 'react';
import { render } from '@testing-library/react';

import Prompt from './prompt';

type Props = React.ComponentProps<typeof Prompt>;

const renderPrompt = (extraProps = {} as Props) => render(<Prompt {...extraProps} />);

describe('Prompt component', () => {
    it('renders', () => {
        const children = 'Prompt child';
        const SUT = renderPrompt({ children });
        expect(SUT.asFragment()).toMatchInlineSnapshot(`
            <DocumentFragment>
              <div
                class="prompt"
              >
                Prompt child
              </div>
            </DocumentFragment>
        `);
    });
});
