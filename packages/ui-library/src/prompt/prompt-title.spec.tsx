import React from 'react';
import { render } from '@testing-library/react';

import PromptTitle from './prompt-title';

type Props = React.ComponentProps<typeof PromptTitle>;

const renderPromptTitle = (extraProps = {} as Props) => render(<PromptTitle {...extraProps} />);

describe('Prompt Title', () => {
    it('renders', () => {
        const children = 'Title child';
        const SUT = renderPromptTitle({ children });
        expect(SUT.asFragment()).toMatchInlineSnapshot(`
            <DocumentFragment>
              <p
                class="prompt-title"
              >
                Title child
              </p>
            </DocumentFragment>
        `);
    });
});
