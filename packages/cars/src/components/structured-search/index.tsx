import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useTheme } from '@emotion/react';
import { Button, Field, Icon, TypeAhead } from '@gumtree/ui-library';
import {
    getMotorsPriceFilterOptions,
    MAX_LABEL,
    MIN_LABEL,
} from '@gumtree/shared/src/constants/price-ranges';
import { formatNumber } from '@gumtree/ui-library/src/utils/currency-service';
import { trackGtmEvent } from '@gumtree/shared/src/util/gtm-tracker';
import { qaAttribute } from '@gumtree/ui-library/src/utils/qa-service';
import { trackGA4PreNav } from '@gumtree/shared/src/util/ga4-shared';

import {
    CallToActionButtonContainer,
    Container,
    KeywordContainer,
    LocationContainer,
    MakeContainer,
    ModelContainer,
    PriceMaxContainer,
    PriceMinContainer,
    ShowMoreContainer,
    locationFieldCss,
} from './index.style';
import { DefaultValue, initialMake, initialModel, initialSelectValue } from './constants';
import { fetchCanonicalSearchUrl } from './fetch-canonical-search-url';
import {
    fetchInventoryDataAndUpdateView,
    debouncedFetchInventoryDataAndUpdateView,
    debouncedUpdateSearchSuggestion,
} from './view-updater';
import { CarsState } from '../../reducers';

const minPriceRanges = getMotorsPriceFilterOptions(true);
const maxPriceRanges = getMotorsPriceFilterOptions(false);

const showLocationSuggestions = (location: string) => location.length >= 2;

export default function StructuredSearch() {
    const searchBarLocation = useSelector((state: CarsState) => state.searchBar.location);

    const [minPrice, setMinPrice] = useState<string | DefaultValue>(minPriceRanges.at(0)?.value);
    const [maxPrice, setMaxPrice] = useState<string | DefaultValue>(maxPriceRanges.at(0)?.value);
    const [keyword, setKeyword] = useState('');
    const [abundance, setAbundance] = useState(0);
    const [make, setMake] = useState<string>(initialSelectValue);
    const [model, setModel] = useState<string>(initialSelectValue);
    const [location, setLocation] = useState<string>('');
    const [isMoreOptionsVisible, setIsMoreOptionsVisible] = useState<boolean>(true);

    const [makes, setMakes] = useState([initialMake]);
    const [models, setModels] = useState([initialModel]);
    const [locationSuggestions, setLocationSuggestions] = useState([]);

    const theme = useTheme();

    useEffect(() => {
        fetchInventoryDataAndUpdateView(
            { make, model, minPrice, maxPrice, keyword, location },
            { setMakes, setModels, setAbundance }
        );
    }, [minPrice, maxPrice, make, model]);

    useEffect(() => {
        debouncedFetchInventoryDataAndUpdateView(
            { make, model, minPrice, maxPrice, keyword, location },
            { setMakes, setModels, setAbundance }
        );
    }, [keyword, location]);

    useEffect(() => {
        if (searchBarLocation?.value) {
            setLocation(searchBarLocation?.value);
        }
    }, [searchBarLocation]);

    const handleSearchClick = () => {
        Promise.all([
            fetchCanonicalSearchUrl({
                make,
                model,
                minPrice,
                maxPrice,
                keyword,
                location,
            }),
            trackGA4PreNav<GA4.SubmitCarSearchEvent>({
                event: 'submit_car_search',
                search: {
                    location: location?.length ? location : undefined,
                    vehicleMake: make === initialSelectValue ? undefined : make,
                    vehicleModel: model === initialSelectValue ? undefined : make,
                    minPrice: minPrice === MIN_LABEL ? undefined : minPrice,
                    maxPrice: maxPrice === MAX_LABEL ? undefined : maxPrice,
                    term: keyword.length ? keyword : undefined,
                    category: 'cars',
                    searchResults: abundance,
                },
            }),
        ]).then(([canonicalUrl]) => {
            window.location.assign(canonicalUrl);
        });
    };

    return (
        <Container>
            <MakeContainer>
                <Field
                    id="select-make"
                    label="Make"
                    value={make}
                    options={makes}
                    onInput={(e) => {
                        setMake(e.target.value);
                        setModel(initialSelectValue);
                    }}
                />
            </MakeContainer>
            <ModelContainer>
                <Field
                    id="select-model"
                    label="Model"
                    value={model}
                    options={models}
                    onInput={(e) => {
                        setModel(e.target.value);
                    }}
                />
            </ModelContainer>
            <PriceMinContainer>
                <Field
                    id="select-price-min"
                    label="Price range"
                    options={minPriceRanges}
                    value={minPrice}
                    onInput={(e) => {
                        setMinPrice(e.target.value);
                    }}
                />
            </PriceMinContainer>
            <PriceMaxContainer>
                <Field
                    id="select-price-max"
                    label="&nbsp;"
                    options={maxPriceRanges}
                    value={maxPrice}
                    onInput={(e) => {
                        setMaxPrice(e.target.value);
                    }}
                />
            </PriceMaxContainer>
            <LocationContainer>
                <span>Location</span>
                <div css={locationFieldCss(theme)}>
                    <Icon type="beacon" size="medium" className="location-input-icon" />
                    <TypeAhead
                        inputProps={{
                            'aria-label': 'Location',
                            onInput: (e) => {
                                const { value } = e.target;
                                if (showLocationSuggestions(location)) {
                                    debouncedUpdateSearchSuggestion(value, setLocationSuggestions);
                                }
                            },
                            placeholder: 'Postcode or location',
                            ...qaAttribute('search-location-field'),
                        }}
                        id="header-search-location"
                        limit={10}
                        name="search_location"
                        onChange={(value) => {
                            setLocation(value);
                        }}
                        defaultValue={searchBarLocation?.value || ''}
                        typeAheadIcon={<Icon type="beacon" size="medium" />}
                        options={showLocationSuggestions(location) ? locationSuggestions : []}
                        titleOfRecentHistory="Recent locations"
                        showClearAllButton={false}
                    />
                </div>
            </LocationContainer>
            <CallToActionButtonContainer>
                <Button
                    display="primary"
                    label={`Search Cars (${formatNumber(abundance)})`}
                    onClick={handleSearchClick}
                    fullWidth
                />
            </CallToActionButtonContainer>
            <KeywordContainer>
                {isMoreOptionsVisible ? (
                    <ShowMoreContainer
                        id="showMoreContainer"
                        onClick={() => {
                            trackGtmEvent({
                                action: 'MoreOptionsClicked',
                                label: '',
                            });
                            setIsMoreOptionsVisible(false);
                        }}
                    >
                        <span>More options</span>
                        <Icon type="chevron-d" size="smallest" className="chevron" />
                    </ShowMoreContainer>
                ) : (
                    <>
                        <span>Keyword</span>
                        <Field
                            id="keyword-field-value"
                            placeholder="eg. 4x4, spares or repairs, left-hand drive..."
                            value={keyword}
                            onInput={(e) => {
                                setKeyword(e.target.value);
                            }}
                        />
                    </>
                )}
            </KeywordContainer>
        </Container>
    );
}
