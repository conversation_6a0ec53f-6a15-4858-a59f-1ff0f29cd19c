import React from 'react';
import { Section, Title } from './page-section.style';

export interface PageSectionProps {
    title: React.ReactNode;
    children: React.ReactNode;
    className?: string;
}

function PageSection({ title, children, className }: Readonly<PageSectionProps>) {
    return (
        <Section className={className}>
            <Title>{title}</Title>
            {children}
        </Section>
    );
}

export default PageSection;
