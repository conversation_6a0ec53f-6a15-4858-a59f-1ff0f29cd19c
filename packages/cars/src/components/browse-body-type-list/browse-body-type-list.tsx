import React from 'react';
import { useTheme } from '@emotion/react';
import { trackGA4AndNavigate } from '@gumtree/shared/src/util/ga4-shared';
import { getLinkHostname } from '@gumtree/shared/src/util/ga4-bff';
import { List, Link, IconWrapper, listItemCss } from './browse-body-type-list.style';
import hatchback from './images/hatchback.svg';
import sedan from './images/sedan.svg';
import wagon from './images/wagon.svg';
import suv from './images/suv.svg';
import coupe from './images/coupe.svg';
import convertible from './images/convertible.svg';

const items = [
    {
        title: 'Hatchback',
        svg: hatchback,
        link: '/cars-vans-motorbikes/cars/hatchback',
    },
    {
        title: 'Saloon',
        svg: sedan,
        link: '/cars-vans-motorbikes/cars/saloon',
    },
    {
        title: 'Estate',
        svg: wagon,
        link: '/cars-vans-motorbikes/cars/estate',
    },
    {
        title: 'MPV',
        svg: suv,
        link: '/cars-vans-motorbikes/cars/mpv',
    },
    {
        title: 'Coupe',
        svg: coupe,
        link: '/cars-vans-motorbikes/cars/coupe',
    },
    {
        title: 'Convertible',
        svg: convertible,
        link: '/cars-vans-motorbikes/cars/convertible',
    },
];

function BrowseBodyTypeList({ sectionTitle }: { sectionTitle: string }) {
    const theme = useTheme();

    return (
        <List>
            {items.map(({ title, link, svg: Svg }, index) => (
                <li key={title} css={listItemCss(theme)}>
                    <Link
                        href={link}
                        onClick={(e) =>
                            trackGA4AndNavigate<GA4.ClickCard>({
                                href: link,
                                mouseEvent: e,
                                payload: {
                                    event: 'click_card',
                                    linkDomain: getLinkHostname(link),
                                    cardPosition: index + 1,
                                    linkSection: sectionTitle,
                                    linkURL: link,
                                    linkText: undefined,
                                },
                            })
                        }
                    >
                        <IconWrapper>
                            <Svg />
                        </IconWrapper>
                        {title}
                    </Link>
                </li>
            ))}
        </List>
    );
}

export default BrowseBodyTypeList;
