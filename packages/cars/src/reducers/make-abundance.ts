export type MakeAbundance = {
    name: string;
    abundance: number;
    url: string;
}[];

export const UPDATE_MAKE_ABUNDANCE = 'UPDATE_MAKE_ABUNDANCE';

export const updateMakeAbundance = (payload: MakeAbundance) => ({
    type: UPDATE_MAKE_ABUNDANCE,
    payload,
});

export default (state = [], action) => {
    return action.type === UPDATE_MAKE_ABUNDANCE ? [...state, ...action.payload] : state;
};
