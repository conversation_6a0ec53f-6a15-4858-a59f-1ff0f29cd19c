import React from 'react';
import { useSelector } from 'react-redux';
import { ReplyConfirmationState } from '../../reducers';
import {
    StyledHeader,
    StyledH1,
    StyledDetailsWrapper,
    StyledAreaText,
    StyledPriceText,
} from './styles';

const Header = ({ className = '' }) => {
    const { title, area, price } = useSelector(
        (state: ReplyConfirmationState) => state.replyConfirmation
    );

    return (
        <StyledHeader className={className}>
            <StyledH1>{title}</StyledH1>
            <StyledDetailsWrapper>
                {area && <StyledAreaText>{area}</StyledAreaText>}

                {price && (
                    <StyledPriceText>
                        <meta itemProp="currency" content="GBP" />
                        <span itemProp="price">{price}</span>
                    </StyledPriceText>
                )}
            </StyledDetailsWrapper>
        </StyledHeader>
    );
};

export default Header;
