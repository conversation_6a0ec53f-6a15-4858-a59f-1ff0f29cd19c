import type { Unpacked } from '@gumtree/shared/src/model/generic.model';
import buyerTransform from '@gumtree/web-bff/src/data/buyer/transform';
import { categoriesPageTransform } from './categories-page-transform';

const transform = async (data: any) => {
    return {
        ...buyerTransform(data),
        categoriesPageData: categoriesPageTransform(data),
    };
};

export default transform;

export type Transformed = Unpacked<ReturnType<typeof transform>>;
