import React from 'react';
import { css } from '@emotion/react';
import { shallowEqual, useSelector } from 'react-redux';

import { trackGA4PreNav } from '@gumtree/shared/src/util/ga4-shared';
import {
    breakpoints,
    fontSizes,
    gutterSizes,
    mediaQuery,
} from '@gumtree/ui-library/src/base/theme';
import type { DeviceTypes } from '@gumtree/shared/src/types/client-data';

import GooglePlayBadge from '@gumtree/shell/src/footer/google-play-badge.svg';
import AppStoreBadge from '@gumtree/shell/src/footer/app-store-badge.svg';
import DesktopHero from './desktop_hero.jpg';
import DesktopPhone1 from './desktop-phone-1.gif';
import DesktopPhone2 from './desktop-phone-2.jpg';
import DesktopPhone3 from './desktop-phone-3.gif';
import DesktopPhone4 from './desktop-phone-4.gif';

import type { StaticPagesState } from '../../reducers';

export default function Apps() {
    const { device } = useSelector(
        ({ baseConfig }: StaticPagesState) => ({
            device: baseConfig.device.type,
        }),
        shallowEqual
    );

    return (
        <div css={rootCss} className="apps-landing">
            <div className="apps-landing-container">
                <h1 className="apps-landing-title" data-q="apps-title">
                    Get the best of Gumtree's buying and selling community with the app.
                </h1>
                <div className="apps-landing-hero-image" />
                <AppsButtons device={device} />
                <div className="apps-landing-image-text">
                    <div className="apps-landing-image-text-copy">
                        <h2 className="copy-title">Be the first to get what you want.</h2>
                        <p className="copy-descr">
                            Whether buying or selling, receive messages as soon as they're sent.
                        </p>
                    </div>
                    <div className="apps-landing-image-text-image">
                        <div className="text-image phone-1" />
                    </div>
                </div>
            </div>
            <div className="apps-landing-colour-banner">
                <h2 className="banner-title">Stay connected 24/7.</h2>
                <p className="banner-descr">
                    Make the most out of the Gumtree app, at home or on the go.
                </p>
            </div>
            <div className="apps-landing-container">
                <div className="apps-landing-image-text">
                    <div className="apps-landing-image-text-image">
                        <div className="text-image phone-2" />
                    </div>
                    <div className="apps-landing-image-text-copy">
                        <h2 className="copy-title">See local listings, wherever you go.</h2>
                        <p className="copy-descr">The neighbourhood's good on the Gumtree app.</p>
                    </div>
                </div>
                <hr />
                <div className="apps-landing-image-text">
                    <div className="apps-landing-image-text-copy">
                        <h2 className="copy-title">After a good deal?</h2>
                        <p className="copy-descr">
                            With search alerts going straight to your phone, you'll be first in line
                            for all the things you really love.
                        </p>
                    </div>
                    <div className="apps-landing-image-text-image">
                        <div className="text-image phone-3" />
                    </div>
                </div>
                <div className="apps-landing-image-text mobile-stack">
                    <div className="apps-landing-image-text-image">
                        <div className="text-image phone-4" />
                    </div>
                    <div className="apps-landing-image-text-copy">
                        <h2 className="copy-title">After a simple sale?</h2>
                        <p className="copy-descr">
                            Get selling in seconds. Post an ad, straight from your phone.
                        </p>
                    </div>
                </div>
                <h2 className="apps-landing-title">Download now</h2>
                <AppsButtons device={device} />
            </div>
        </div>
    );
}

function AppsButtons({ device }: { device: DeviceTypes | undefined }) {
    return (
        <div className="apps-landing-button-container">
            <a
                href={buildUrl('android', device)}
                onClick={() =>
                    trackGA4PreNav({
                        event: 'ga3_legacy_event',
                        legacyEvent: {
                            name: 'android_info_page',
                            label: undefined,
                            extra: undefined,
                        },
                    })
                }
                className="apps-landing-download-button android-button"
                data-q="android-app-store-link"
            >
                <GooglePlayBadge style={{ width: '100%', height: '100%' }} />
            </a>
            <a
                href={buildUrl('ios', device)}
                onClick={() =>
                    trackGA4PreNav({
                        event: 'ga3_legacy_event',
                        legacyEvent: { name: 'iOS_info_page', label: undefined, extra: undefined },
                    })
                }
                className="apps-landing-download-button ios-button"
                data-q="ios-app-store-link"
            >
                <AppStoreBadge style={{ width: '100%', height: '100%' }} />
            </a>
        </div>
    );
}

function buildUrl(os: string, device?: string) {
    const url =
        'https://app.adjust.com/ez1o5rv?campaign=App%20Store%20Button&adgroup=App%20Info%20Page&creative=V2';
    if (device === 'desktop' && os === 'ios') {
        return `${url}&redirect=https://itunes.apple.com/gb/app/gumtree/id487946174?mt=8`;
    } else if (device === 'desktop' && os === 'android') {
        return `${url}&redirect=https://play.google.com/store/apps/details?id=com.gumtree.android&hl=en_GB`;
    }
    return url;
}

const rootCss = css`
    .apps-landing-container {
        text-align: center;
        width: 80%;
        margin: 0 auto;

        ${mediaQuery.until(breakpoints.small)} {
            width: 100%;
        }

        ${mediaQuery.from(breakpoints.xlarge)} {
            width: 1024px;
        }
    }

    .apps-landing-title {
        font-size: ${fontSizes.iconLarge};
        width: 50%;
        margin: 36px auto;
        line-height: 1.4 !important;

        ${mediaQuery.until(breakpoints.medium)} {
            font-size: ${fontSizes.xlarger};
            width: 100%;
            margin: 0;
            padding: ${gutterSizes.large18};
        }
        
        ${mediaQuery.between(breakpoints.medium, breakpoints.large)} {
            width: 75%;
        }
    }

    .apps-landing-hero-image {
        width: 100%;
        min-height: 500px;
        background-image: url(${DesktopHero});
        background-repeat: no-repeat;
        background-position: center 12px;
        background-size: 800px;
    }

    .apps-landing-button-container {
        display: flex;
        justify-content: center;
        margin-bottom: ${gutterSizes.xlarge};

        ${mediaQuery.until(breakpoints.small)} {
            padding: ${gutterSizes.medium};
        }
    }

    .apps-landing-download-button {
        width: 200px;
        height: 70px;
        background-repeat: no-repeat;
        background-size: 200px;

        ${mediaQuery.until(breakpoints.medium)} {
            background-size: 100%;
        }

        &.android-button {
            margin-right: ${gutterSizes.medium};
        }
    }

    .apps-landing-image-text {
        display: flex;
        padding-top: 36px;
        flex-wrap: wrap;

        ${mediaQuery.until(breakpoints.small)} {
            padding-top: 0;
            padding-bottom: ${gutterSizes.xlarge};
        }

        &.mobile-stack {
            ${mediaQuery.until(breakpoints.small)} {
                flex-direction: column-reverse;
            }
        }

        .apps-landing-image-text-copy {
            flex: 1;
            align-self: center;

            ${mediaQuery.until(breakpoints.small)} {
                flex: auto;
            }

            .copy-title {
                font-size: 28px;
                width: 50%;
                text-align: left;
                margin: 0 auto;

                ${mediaQuery.until(breakpoints.small)} {
                    width: 75%;
                    text-align: center;
                }

                ${mediaQuery.between(breakpoints.small, breakpoints.large)} {
                    width: 75%;
                }
            }

            .copy-descr {
                font-size: ${fontSizes.large};
                width: 50%;
                margin: ${gutterSizes.large18} auto;
                text-align: left;

                ${mediaQuery.until(breakpoints.small)} {
                    width: 75%;
                    text-align: center;
                }

                ${mediaQuery.between(breakpoints.small, breakpoints.large)} {
                    width: 75%;
                }
            }
        }

        .apps-landing-image-text-image {
            width: 100%;
            height: 500px;
            flex: 1;

            ${mediaQuery.until(breakpoints.medium)} {
                height: 350px;
            }

            ${mediaQuery.between(breakpoints.medium, breakpoints.large)} {
                height: 400px;
            }

            .text-image {
                height: inherit;
                background-repeat: no-repeat;
                background-position: center 12px;
                background-size: 300px;

                ${mediaQuery.between(breakpoints.small, breakpoints.large)} {
                    background-size: 100%;
                }

                &.phone-1 {
                    background-image: url(${DesktopPhone1});
                }

                &.phone-2 {
                    background-image: url(${DesktopPhone2});
                }

                &.phone-3 {
                    background-image: url(${DesktopPhone3});
                }

                &.phone-4 {
                    background-image: url(${DesktopPhone4});
                }
            }
        }
    }

    .apps-landing-colour-banner {
        background-color: #f0ece6;
        text-align: center;
        padding: 36px 0;

        ${mediaQuery.until(breakpoints.small)} {
            padding: ${gutterSizes.xlarge};
        }

        .banner-title {
            font-size: 28px;
        }

        .banner-descr {
            font-size: ${fontSizes.medium};
        }
    }
`;
