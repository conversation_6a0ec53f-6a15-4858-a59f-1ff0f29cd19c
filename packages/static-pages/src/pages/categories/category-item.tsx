import React, { FC } from 'react';
import styled from '@emotion/styled';
import { Icon } from '@gumtree/ui-library';

interface CategoryItemProps {
    url: string;
    text: string;
    anchorProps?: { [key: string]: any };
    hasSubCategories?: boolean;
}

const StyledAnchor = styled.a`
    display: flex;
    justify-content: space-between;
    color: #3c3241;
    padding: 12px;
    border-bottom: 1px solid #d8d6d9;

    &:hover {
        .link-text {
            text-decoration: underline;
        }
    }
`;

const CategoryItem: FC<CategoryItemProps> = ({
    url,
    text,
    hasSubCategories = false,
    anchorProps = {},
}) => (
    <li>
        <StyledAnchor href={url} {...anchorProps}>
            <span className="link-text">{text}</span>
            {hasSubCategories && <Icon type="chevron-r" />}
        </StyledAnchor>
    </li>
);

export default CategoryItem;
