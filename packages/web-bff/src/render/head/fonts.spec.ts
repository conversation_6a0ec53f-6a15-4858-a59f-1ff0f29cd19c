import { renderToString } from 'react-dom/server.node';
import Fonts from './fonts';

jest.mock('@gumtree/shared/src/manifest-loader/manifest-loader', () => {
    return {
        getManifestPath: jest.fn((fileName) => fileName),
    };
});

it('generates fonts', () => {
    const expected = `<style>
@font-face {
    font-family: gumicon;
    font-display: swap;
    src: url("packages/ui-library/src/icon/fonts/gumtree.woff2") format("woff2"), url("packages/ui-library/src/icon/fonts/gumtree.woff") format("woff"), url("packages/ui-library/src/icon/fonts/gumtree.ttf") format("truetype");
}
</style>`;
    expect(renderToString(Fonts())).toEqual(expected);
});
