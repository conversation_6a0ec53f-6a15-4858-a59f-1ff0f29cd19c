import React from 'react';
import {
    getNextPageUrl,
    getPreviousPageUrl,
} from '@gumtree/ui-library/src/pagination/pagination-service';
import type { AdsPaginationState } from "@gumtree/shared/src/types/srp";

export default function PaginationScript({ adsPagination }: { adsPagination: AdsPaginationState }) {
    if (typeof adsPagination === 'object') {
        const { firstPagePath, pagePath, currentPage, numberOfPages } = adsPagination;
        if (numberOfPages > 1) {
            const hasPreviousPage = currentPage > 1;
            const hasNextPage = currentPage < numberOfPages;

            const previousPageUrl = getPreviousPageUrl({
                pagePath,
                currentPage,
                firstPagePath,
            });

            const nextPageUrl = getNextPageUrl({
                pagePath,
                currentPage,
                firstPagePath,
            });

            return <>
                {hasPreviousPage && <link rel="prev" href={previousPageUrl} />}
                {hasNextPage && <link rel="next" href={nextPageUrl} />}
            </>;
        }
    }

    return null;
}
