import xss from 'xss';

export default function filterQueryParams(str: string): string {
    let url;

    try {
        url = new URL(str);
    } catch (_e) {
        return '';
    }
    const params = new URLSearchParams(url.search);
    const pageParams = params.get('page'); // get page number

    if (pageParams === '1' || pageParams === null) {
        return `${url.origin}${url.pathname}`;
    } else {
        return `${url.origin}${url.pathname}?page=${xss(pageParams)}`;
    }
}
