import React from 'react';

const getScript = (comScore?: ComScoreData) => {
    const urlAppend = (comScore && comScore.options && comScore.options.url_append) || '';
    // <!-- noscript comScore Tag -->
    return (
        <noscript>
            <img src={`https://sb.scorecardresearch.com/p?c1=2&c2=7849854&cv=2.0&cj=1&${urlAppend}`} alt=""/>
        </noscript>
    );
    // <!-- End noscript Tag -->
};

export default function ComScoreScript({ comScore }: { comScore: ComScoreData }) {
    return comScore ? getScript(comScore) : null;
}

type ComScoreData = { options?: { url_append: any; }; };
