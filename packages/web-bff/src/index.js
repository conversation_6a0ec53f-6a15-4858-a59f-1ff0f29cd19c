if (process.env.NODE_ENV !== 'production') {
    /**
     * In development bff is isolated from webpack,
     * so we must simulate file-loader, assuming:
     * - publicPath is `/assets/`.
     * - file-loader name is `[path][name].[ext]`.
     *
     * In production, webpack is used to build the server,
     * so we don't need to do this.
     */
    const { resolve, relative } = require('path');
    const rootDir = resolve(__dirname, '../../..');

    const assetsPrefix = '/assets/frontend/';

    const setAssetExport = (mod, filename) => {
        mod.exports = `${assetsPrefix}${relative(rootDir, filename)}`;
    };

    // Override loading for static assets
    ['ico', 'jpg', 'gif', 'png', 'xml', 'woff2', 'woff', 'ttf', 'svg'].forEach((ext) => {
        require.extensions[`.${ext}`] = (imgModule, filename) => {
            setAssetExport(imgModule, filename);
        };
    });

    // Special handling for *.external.js
    const externalJsRegex = /\.external\.js$/;
    const defaultJsLoader = require('module')._extensions['.js'];

    require.extensions['.js'] = (mod, filename) => {
        if (externalJsRegex.test(filename)) {
            // Export path for *.external.js files
            setAssetExport(mod, filename);
        } else {
            // Use default loader for other .js files
            defaultJsLoader(mod, filename);
        }
    };

    // Transpile on-the-fly
    require('@babel/register')({
        extensions: ['.js', '.jsx', '.ts', '.tsx'],
        cache: true, // Faster restarts
        only: [
            // Transpile everything except node_modules and *.external.js
            function (filepath) {
                return !/node_modules/.test(filepath) && !externalJsRegex.test(filepath);
            },
        ],
    });
}

const fs = require('fs');
require('source-map-support').install({
    retrieveSourceMap: (source) => {
        if (source === 'server.js') {
            return {
                url: 'server.js',
                map: fs.readFileSync('server.js.map', 'utf8'),
            };
        }
        return null;
    },
});

// Platform-agnostic environment variables
let propertiesFile = './properties/web-bff.properties';

if (process.env.INTEGRATION_TEST_ENV === 'true') {
    propertiesFile = './properties/web-bff.integration-test.properties';
} else if (process.env.ENVIRONMENT_PROXY) {
    propertiesFile = `./properties/web-bff.${process.env.ENVIRONMENT_PROXY}-proxy.properties`;
}

require('./env').default(propertiesFile);

// Start the bff
require('./server');
