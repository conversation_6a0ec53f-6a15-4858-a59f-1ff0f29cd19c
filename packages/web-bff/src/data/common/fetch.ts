import http, { IncomingHttpHeaders } from 'http';
import https from 'https';
import axiosRetry from 'axios-retry';
import axios, { AxiosRequestConfig, Method, ResponseType } from 'axios';
import type { GrowthBookType } from '@gumtree/shared/src/custom-typings/global';

import {
    RedirectError,
    UnauthorizedError,
    APIError,
    RequestHeaderTooLargeError,
    SystemError,
} from '@gumtree/middleware/src/error';
import {
    HTTP_MOVE_PERMANENTLY,
    HTTP_FOUND,
    HTTP_TEMPORARY_REDIRECT,
    HTTP_REDIRECT_SEE_OTHER,
    HTTP_UNAUTHORIZED,
    HTTP_REQUEST_HEADER_FIELDS_TOO_LARGE,
    HTTP_INTERNAL_SERVER_ERROR,
} from '@gumtree/middleware/src/utils/http-status-code';
import {
    GrowthBookServerSideFeatures,
    GrowthBookFeature,
} from '@gumtree/shared/src/model/experiment';

import logger from '../../logger';

import HttpMetrics from '../../metrics/modules/http-metrics';
import { REQUEST_ID_HEADER_KEY } from '../../util/append-request-id';

axiosRetry(axios, { retries: 1 });

export default function createFetch() {
    return async function fetch(
        {
            url,
            requestHeaders,
            method = 'get',
            responseType = 'json',
            options = {},
            growthBook,
        }: CommonFetchOptions & { growthBook?: GrowthBookType },
        metricsTaggingOptions?: MetricsOptions
    ) {
        const headers = extendRequestHeaders(requestHeaders, growthBook);

        const requestId = requestHeaders[REQUEST_ID_HEADER_KEY];

        if (requestId) {
            delete requestHeaders[REQUEST_ID_HEADER_KEY];
        }

        if (headers && process.env.ENVIRONMENT_PROXY) {
            // We pass all headers from the browser straight through to the service we're calling.
            // I'm a bit dubious about this as a practice, but it causes an issue when we're hitting
            // and HTTPS endpoints, as the host doesn't match the server you're calling and the SSL
            // handshake fails.
            //
            // Only doing this for local development, but we should try and determine if it should be
            // productionised.
            delete headers.host;
        }

        logger.debug({
            message: `Fetching data from [${url}] with headers: ${JSON.stringify(headers)}`,
        } as any);

        let resStatus, resHeaders, data;

        const startDateTime = new Date().getTime();

        try {
            const response = await axios({
                url,
                headers,
                method,
                responseType,
                ...options,
                ...(responseType === 'json' ? { maxRedirects: 0 } : {}),
            });

            const endDateTime = new Date().getTime();

            if (metricsTaggingOptions) {
                HttpMetrics.registerHttpEvent(
                    metricsTaggingOptions.serviceName,
                    metricsTaggingOptions.method,
                    metricsTaggingOptions.path,
                    response.status,
                    endDateTime - startDateTime,
                    metricsTaggingOptions.data
                );
            }

            ({ status: resStatus, headers: resHeaders, data } = response);
        } catch (error: any) {
            const sourceFileName = 'web-bff/common/fetch';

            const endDateTime = new Date().getTime();

            if (error.response) {
                ({ status: resStatus, headers: resHeaders, data } = error.response);
                const errorMessage = `Response status: ${resStatus}. Failed to request API: [${url}]`;

                if (metricsTaggingOptions) {
                    HttpMetrics.registerHttpEvent(
                        metricsTaggingOptions.serviceName,
                        metricsTaggingOptions.method,
                        metricsTaggingOptions.path,
                        error.response.status,
                        endDateTime - startDateTime,
                        metricsTaggingOptions.data
                    );
                }

                if (resStatus >= HTTP_INTERNAL_SERVER_ERROR) {
                    throw new APIError(errorMessage, resStatus);
                }

                if (
                    [
                        HTTP_MOVE_PERMANENTLY,
                        HTTP_FOUND,
                        HTTP_TEMPORARY_REDIRECT,
                        HTTP_REDIRECT_SEE_OTHER,
                    ].includes(resStatus)
                ) {
                    const redirectURL = data?.redirectURL || error.response.headers?.location;
                    throw new RedirectError(resStatus, url, redirectURL);
                }

                if (resStatus === HTTP_UNAUTHORIZED) {
                    throw new UnauthorizedError(url, data?.redirectURL);
                }

                if (resStatus === HTTP_REQUEST_HEADER_FIELDS_TOO_LARGE) {
                    throw new RequestHeaderTooLargeError(url);
                }

                logger.warn(errorMessage, sourceFileName, error);
            } else {
                if (metricsTaggingOptions) {
                    HttpMetrics.registerHttpEvent(
                        metricsTaggingOptions.serviceName,
                        metricsTaggingOptions.method,
                        metricsTaggingOptions.path,
                        error.code,
                        endDateTime - startDateTime,
                        metricsTaggingOptions.data
                    );
                }

                const errorMessage = `Error code: ${error.code}. Failed to request API: [${url}]`;
                throw new SystemError(errorMessage, error.code);
            }
        }

        logger.debug({
            message: `API [${url}] response headers: ${JSON.stringify(resHeaders)}`,
        } as any);
        logger.debug({
            message: `API [${url}] response JSON: ${JSON.stringify(data)}`,
        } as any);

        if (process.env.DEV_ENV === 'true' && resHeaders['set-cookie']) {
            resHeaders = {
                ...resHeaders,
                'set-cookie': resHeaders['set-cookie'].map((cookie) =>
                    cookie.replaceAll(/Domain=[a-z]+.gumtree.io;/g, '').replaceAll(/Secure;?/g, '')
                ),
            };
        }
        if (requestId && data && typeof data === 'object') {
            data.requestId = requestId;
        }
        return {
            responseHeaders: resHeaders,
            statusCode: resStatus,
            data,
        };
    };
}

export interface CommonFetchOptions {
    url: string;
    method?: Method;
    responseType?: ResponseType;
    requestHeaders: IncomingHttpHeaders;
    /**
     * Other axios options.
     */
    options?: Omit<AxiosRequestConfig, 'url' | 'method' | 'responseType' | 'requestHeaders'>;
}

export interface MetricsOptions {
    serviceName: 'seller' | 'buyer';
    path: string;
    method: string;
    data?: any;
}

interface ExtendedAgentOptions extends http.AgentOptions {
    scheduling: any;
}

// Legacy API requires extended headers to be able to respond
function extendRequestHeaders(headers?: IncomingHttpHeaders, growthBook?: GrowthBookType) {
    return extendHeadersWithIpRelatedHeaders(extendHeadersWithExperiments(headers, growthBook));
}

export function extendHeadersWithExperiments(
    headers?: IncomingHttpHeaders,
    growthBook?: GrowthBookType
) {
    if (!headers) {
        return headers;
    }

    const experiments: string[] = [];
    const getFeatureValue = (experimentKey: string, defaultValue: string = '') => {
        if (!growthBook) return;
        const featureValue = growthBook.getFeatureValue(experimentKey, defaultValue);
        featureValue !== defaultValue && experiments.push(`${experimentKey}.${featureValue}`);
    };

    Object.values({ ...GrowthBookServerSideFeatures, ...GrowthBookFeature }).forEach((feature) => {
        getFeatureValue(feature);
    });

    if (experiments.length > 0) {
        headers['experiments'] = experiments.join(',');
    }
    return headers;
}

export function extendHeadersWithIpRelatedHeaders(headers?: IncomingHttpHeaders) {
    if (!headers || !headers['x-forwarded-for'] || typeof headers['x-forwarded-for'] !== 'string') {
        return headers;
    }

    headers && delete headers.host;

    const clientIp = headers['x-forwarded-for'].split(',').slice(0, 1).shift();

    return {
        ...headers,
        'True-GUM-IP': clientIp,
    };
}

/** Default is `Infinity` https://nodejs.org/docs/latest-v16.x/api/http.html#new-agentoptions  */
const KEEP_ALIVE_MAX_SOCKETS_PER_HOST = 50;
/** Default is `256` https://nodejs.org/docs/latest-v16.x/api/http.html#new-agentoptions  */
const KEEP_ALIVE_MAX_FREESOCKETS_PER_HOST = 50;

/**
 * node 19 defaults are:
 * ```js
 * globalAgent: new Agent({ keepAlive: true, scheduling: 'lifo', timeout: 5000 }
 * ```
 */
export const axiosKeepAliveOptions = {
    httpAgent: new http.Agent({
        keepAlive: true,
        maxSockets: KEEP_ALIVE_MAX_SOCKETS_PER_HOST,
        maxFreeSockets: KEEP_ALIVE_MAX_FREESOCKETS_PER_HOST,
        scheduling: 'lifo',
        timeout: 10000,
    } as ExtendedAgentOptions),
    httpsAgent: new https.Agent({
        keepAlive: true,
        maxSockets: KEEP_ALIVE_MAX_SOCKETS_PER_HOST,
        maxFreeSockets: KEEP_ALIVE_MAX_FREESOCKETS_PER_HOST,
        scheduling: 'lifo',
        timeout: 10000,
    } as ExtendedAgentOptions),
};
