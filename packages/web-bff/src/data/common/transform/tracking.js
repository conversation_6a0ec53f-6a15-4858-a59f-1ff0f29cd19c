/* logic copied from templates/shared/freemarker/common/_analyticsData.ftl */
import { safeGetData as get } from '@gumtree/ui-library/src/utils/data-service';
import { protectedAttributes } from '@gumtree/shared/src/model/privacy.model';

export default (data) => {
    let zenoDataLayer = get('zenoDataLayer', data);
    if (!zenoDataLayer) {
        return undefined;
    }

    if (get('model.core.pageType', data) === 'VIP') {
        try {
            const parsed = JSON.parse(zenoDataLayer);
            if (parsed?.a?.attr) {
                protectedAttributes.forEach(
                    (key) => key in parsed.a.attr && (parsed.a.attr[key] = '')
                );
                zenoDataLayer = JSON.stringify(parsed);
            }
        } catch {
            /** NOOP */
        }
    }

    return zenoDataLayer.replace('bushfire-mweb', 'responsive').replace('<', '').replace('>', '');
};
