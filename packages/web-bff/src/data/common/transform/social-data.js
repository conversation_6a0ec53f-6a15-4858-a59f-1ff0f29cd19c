import { safeGetData as get } from '@gumtree/ui-library/src/utils/data-service';

export default (data) => ({
    currentUrl: get('model.core.currentUrl', data),
    location: {
        locality: get('model.core.location.locality', data) || 'United Kingdom',
        region: get('model.core.location.region', data) || 'United Kingdom',
        postalCode: get('model.core.location.postalCode', data) || '',
    },
});
