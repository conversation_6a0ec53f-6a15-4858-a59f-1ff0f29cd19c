/**
 * threatmetrix
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0.0-SNAPSHOT
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import globalAxios, { AxiosPromise, AxiosInstance } from 'axios';
import { Configuration } from './configuration';
//@ts-ignore
import { BASE_PATH, RequestArgs, BaseAPI, RequiredError } from './base';

/**
 *
 * @export
 * @interface AccountRelationsResponse
 */
export interface AccountRelationsResponse {
    /**
     * user ids to smart device ids map
     * @type {{ [key: string]: string; }}
     * @memberof AccountRelationsResponse
     */
    userIdsToSmartDeviceIds?: { [key: string]: string };
    /**
     * smart device ids to user ids map
     * @type {{ [key: string]: number; }}
     * @memberof AccountRelationsResponse
     */
    smartDeviceIdsToUserIds?: { [key: string]: number };
}
/**
 * advert session
 * @export
 * @interface AdvertSession
 */
export interface AdvertSession {
    /**
     * user Id
     * @type {number}
     * @memberof AdvertSession
     */
    userId?: number;
    /**
     * account Email
     * @type {string}
     * @memberof AdvertSession
     */
    accountEmail?: string;
    /**
     * ip Address
     * @type {string}
     * @memberof AdvertSession
     */
    ipAddress?: string;
    /**
     *
     * @type {AdvertTracking}
     * @memberof AdvertSession
     */
    advertTracking: AdvertTracking;
}
/**
 *
 * @export
 * @interface AdvertTracking
 */
export interface AdvertTracking {
    /**
     * unique identifier
     * @type {string}
     * @memberof AdvertTracking
     */
    id?: string;
    /**
     * title
     * @type {string}
     * @memberof AdvertTracking
     */
    title?: string;
    /**
     * category id
     * @type {number}
     * @memberof AdvertTracking
     */
    categoryId?: number;
    /**
     * phone in description
     * @type {string}
     * @memberof AdvertTracking
     */
    phoneInDescription?: string;
    /**
     * location ID
     * @type {number}
     * @memberof AdvertTracking
     */
    locationId?: number;
    /**
     * price
     * @type {string}
     * @memberof AdvertTracking
     */
    price?: string;
    /**
     * account phone number
     * @type {string}
     * @memberof AdvertTracking
     */
    accountPhoneNumber?: string;
    /**
     * post code
     * @type {string}
     * @memberof AdvertTracking
     */
    postCode?: string;
    /**
     * user type
     * @type {string}
     * @memberof AdvertTracking
     */
    userType?: AdvertTrackingUserTypeEnum;
}

/**
 * @export
 * @enum {string}
 */
export enum AdvertTrackingUserTypeEnum {
    Regular = 'regular',
    Pro = 'pro',
}

/**
 *
 * @export
 * @interface GetSmartDeviceIdResponse
 */
export interface GetSmartDeviceIdResponse {
    /**
     * smart device id
     * @type {string}
     * @memberof GetSmartDeviceIdResponse
     */
    smartDeviceId?: string;
}
/**
 *
 * @export
 * @interface GetUsersResponse
 */
export interface GetUsersResponse {
    /**
     *
     * @type {Array<User>}
     * @memberof GetUsersResponse
     */
    users?: Array<User>;
}
/**
 * login session
 * @export
 * @interface LoginSession
 */
export interface LoginSession {
    /**
     * user id
     * @type {number}
     * @memberof LoginSession
     */
    userId?: number;
    /**
     * account Email
     * @type {string}
     * @memberof LoginSession
     */
    accountEmail?: string;
    /**
     * ip Address
     * @type {string}
     * @memberof LoginSession
     */
    ipAddress?: string;
    /**
     * login Type
     * @type {string}
     * @memberof LoginSession
     */
    loginType?: LoginSessionLoginTypeEnum;
}

/**
 * @export
 * @enum {string}
 */
export enum LoginSessionLoginTypeEnum {
    Loginform = 'loginform',
    Sociallogin = 'sociallogin',
}

/**
 * registration session
 * @export
 * @interface RegistrationSession
 */
export interface RegistrationSession {
    /**
     * user Id
     * @type {number}
     * @memberof RegistrationSession
     */
    userId?: number;
    /**
     * account Email
     * @type {string}
     * @memberof RegistrationSession
     */
    accountEmail?: string;
    /**
     * ip Address
     * @type {string}
     * @memberof RegistrationSession
     */
    ipAddress?: string;
}
/**
 * Threatmetrix response
 * @export
 * @interface Threatmetrix
 */
export interface Threatmetrix {
    /**
     * summary Risk Score
     * @type {string}
     * @memberof Threatmetrix
     */
    summaryRiskScore?: string;
}
/**
 *
 * @export
 * @interface User
 */
export interface User {
    /**
     * user id
     * @type {number}
     * @memberof User
     */
    userId?: number;
}
/**
 *
 * @export
 * @interface UserRelationRequest
 */
export interface UserRelationRequest {
    /**
     *
     * @type {Array<User>}
     * @memberof UserRelationRequest
     */
    users?: Array<User>;
}

/**
 * DeviceDataApi - axios parameter creator
 * @export
 */
export const DeviceDataApiAxiosParamCreator = (configuration?: Configuration) => {
    return {
        /**
         * Get smart device id by user id
         * @summary Get smart device id by user id
         * @param {number} userId user id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSmartDeviceIdByUserId: async (
            userId: number,
            options: any = {}
        ): Promise<RequestArgs> => {
            // verify required parameter 'userId' is not null or undefined
            if (userId === null || userId === undefined) {
                throw new RequiredError(
                    'userId',
                    'Required parameter userId was null or undefined when calling getSmartDeviceIdByUserId.'
                );
            }
            const localVarPath =
                `/threatmetrix/user/{user-id}/smart-device/smart-device-id`.replace(
                    `{${'user-id'}}`,
                    encodeURIComponent(String(userId))
                );
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options };
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            const queryParameters = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                if (Object.prototype.hasOwnProperty.call(localVarQueryParameter, key)) {
                    queryParameters.set(key, localVarQueryParameter[key]);
                }
            }
            for (const key in options.query) {
                if (Object.prototype.hasOwnProperty.call(options.query, key)) {
                    queryParameters.set(key, options.query[key]);
                }
            }
            localVarUrlObj.search = new URLSearchParams(queryParameters).toString();
            const headersFromBaseOptions =
                baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {
                ...localVarHeaderParameter,
                ...headersFromBaseOptions,
                ...options.headers,
            };

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * Get summary risk score by user id
         * @summary Get summary risk score by user id
         * @param {number} userId user id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSummaryRiskScoreByUserId: async (
            userId: number,
            options: any = {}
        ): Promise<RequestArgs> => {
            // verify required parameter 'userId' is not null or undefined
            if (userId === null || userId === undefined) {
                throw new RequiredError(
                    'userId',
                    'Required parameter userId was null or undefined when calling getSummaryRiskScoreByUserId.'
                );
            }
            const localVarPath = `/threatmetrix/user/{user-id}/summary-risk-score`.replace(
                `{${'user-id'}}`,
                encodeURIComponent(String(userId))
            );
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options };
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            const queryParameters = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                if (Object.prototype.hasOwnProperty.call(localVarQueryParameter, key)) {
                    queryParameters.set(key, localVarQueryParameter[key]);
                }
            }
            for (const key in options.query) {
                if (Object.prototype.hasOwnProperty.call(options.query, key)) {
                    queryParameters.set(key, options.query[key]);
                }
            }
            localVarUrlObj.search = new URLSearchParams(queryParameters).toString();
            const headersFromBaseOptions =
                baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {
                ...localVarHeaderParameter,
                ...headersFromBaseOptions,
                ...options.headers,
            };

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * Get user relations
         * @summary Get user relations
         * @param {UserRelationRequest} userRelationRequest
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUserRelationsByUserIds: async (
            userRelationRequest: UserRelationRequest,
            options: any = {}
        ): Promise<RequestArgs> => {
            // verify required parameter 'userRelationRequest' is not null or undefined
            if (userRelationRequest === null || userRelationRequest === undefined) {
                throw new RequiredError(
                    'userRelationRequest',
                    'Required parameter userRelationRequest was null or undefined when calling getUserRelationsByUserIds.'
                );
            }
            const localVarPath = `/threatmetrix/user`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options };
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            const queryParameters = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                if (Object.prototype.hasOwnProperty.call(localVarQueryParameter, key)) {
                    queryParameters.set(key, localVarQueryParameter[key]);
                }
            }
            for (const key in options.query) {
                if (Object.prototype.hasOwnProperty.call(options.query, key)) {
                    queryParameters.set(key, options.query[key]);
                }
            }
            localVarUrlObj.search = new URLSearchParams(queryParameters).toString();
            const headersFromBaseOptions =
                baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {
                ...localVarHeaderParameter,
                ...headersFromBaseOptions,
                ...options.headers,
            };
            const nonString = typeof userRelationRequest !== 'string';
            const needsSerialization =
                nonString && configuration && configuration.isJsonMime
                    ? configuration.isJsonMime(localVarRequestOptions.headers['Content-Type'])
                    : nonString;
            localVarRequestOptions.data = needsSerialization
                ? JSON.stringify(userRelationRequest !== undefined ? userRelationRequest : {})
                : userRelationRequest || '';

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * Get users by smart-device-id
         * @summary Get users by smart-device-id
         * @param {string} smartDeviceId smart device id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUsersBySmartDeviceId: async (
            smartDeviceId: string,
            options: any = {}
        ): Promise<RequestArgs> => {
            // verify required parameter 'smartDeviceId' is not null or undefined
            if (smartDeviceId === null || smartDeviceId === undefined) {
                throw new RequiredError(
                    'smartDeviceId',
                    'Required parameter smartDeviceId was null or undefined when calling getUsersBySmartDeviceId.'
                );
            }
            const localVarPath = `/threatmetrix/smart-device/{smart-device-id}/user`.replace(
                `{${'smart-device-id'}}`,
                encodeURIComponent(String(smartDeviceId))
            );
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options };
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            const queryParameters = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                if (Object.prototype.hasOwnProperty.call(localVarQueryParameter, key)) {
                    queryParameters.set(key, localVarQueryParameter[key]);
                }
            }
            for (const key in options.query) {
                if (Object.prototype.hasOwnProperty.call(options.query, key)) {
                    queryParameters.set(key, options.query[key]);
                }
            }
            localVarUrlObj.search = new URLSearchParams(queryParameters).toString();
            const headersFromBaseOptions =
                baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {
                ...localVarHeaderParameter,
                ...headersFromBaseOptions,
                ...options.headers,
            };

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    };
};

/**
 * DeviceDataApi - functional programming interface
 * @export
 */
export const DeviceDataApiFp = (configuration?: Configuration) => {
    return {
        /**
         * Get smart device id by user id
         * @summary Get smart device id by user id
         * @param {number} userId user id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSmartDeviceIdByUserId(
            userId: number,
            options?: any
        ): Promise<
            (axios?: AxiosInstance, basePath?: string) => AxiosPromise<GetSmartDeviceIdResponse>
        > {
            const localVarAxiosArgs = await DeviceDataApiAxiosParamCreator(
                configuration
            ).getSmartDeviceIdByUserId(userId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs = {
                    ...localVarAxiosArgs.options,
                    url: (configuration?.basePath || basePath) + localVarAxiosArgs.url,
                };
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * Get summary risk score by user id
         * @summary Get summary risk score by user id
         * @param {number} userId user id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSummaryRiskScoreByUserId(
            userId: number,
            options?: any
        ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Threatmetrix>> {
            const localVarAxiosArgs = await DeviceDataApiAxiosParamCreator(
                configuration
            ).getSummaryRiskScoreByUserId(userId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs = {
                    ...localVarAxiosArgs.options,
                    url: (configuration?.basePath || basePath) + localVarAxiosArgs.url,
                };
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * Get user relations
         * @summary Get user relations
         * @param {UserRelationRequest} userRelationRequest
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getUserRelationsByUserIds(
            userRelationRequest: UserRelationRequest,
            options?: any
        ): Promise<
            (axios?: AxiosInstance, basePath?: string) => AxiosPromise<AccountRelationsResponse>
        > {
            const localVarAxiosArgs = await DeviceDataApiAxiosParamCreator(
                configuration
            ).getUserRelationsByUserIds(userRelationRequest, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs = {
                    ...localVarAxiosArgs.options,
                    url: (configuration?.basePath || basePath) + localVarAxiosArgs.url,
                };
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * Get users by smart-device-id
         * @summary Get users by smart-device-id
         * @param {string} smartDeviceId smart device id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getUsersBySmartDeviceId(
            smartDeviceId: string,
            options?: any
        ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<GetUsersResponse>> {
            const localVarAxiosArgs = await DeviceDataApiAxiosParamCreator(
                configuration
            ).getUsersBySmartDeviceId(smartDeviceId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs = {
                    ...localVarAxiosArgs.options,
                    url: (configuration?.basePath || basePath) + localVarAxiosArgs.url,
                };
                return axios.request(axiosRequestArgs);
            };
        },
    };
};

/**
 * DeviceDataApi - factory interface
 * @export
 */
export const DeviceDataApiFactory = (
    configuration?: Configuration,
    basePath?: string,
    axios?: AxiosInstance
) => {
    return {
        /**
         * Get smart device id by user id
         * @summary Get smart device id by user id
         * @param {number} userId user id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSmartDeviceIdByUserId(
            userId: number,
            options?: any
        ): AxiosPromise<GetSmartDeviceIdResponse> {
            return DeviceDataApiFp(configuration)
                .getSmartDeviceIdByUserId(userId, options)
                .then((request) => request(axios, basePath));
        },
        /**
         * Get summary risk score by user id
         * @summary Get summary risk score by user id
         * @param {number} userId user id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSummaryRiskScoreByUserId(userId: number, options?: any): AxiosPromise<Threatmetrix> {
            return DeviceDataApiFp(configuration)
                .getSummaryRiskScoreByUserId(userId, options)
                .then((request) => request(axios, basePath));
        },
        /**
         * Get user relations
         * @summary Get user relations
         * @param {UserRelationRequest} userRelationRequest
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUserRelationsByUserIds(
            userRelationRequest: UserRelationRequest,
            options?: any
        ): AxiosPromise<AccountRelationsResponse> {
            return DeviceDataApiFp(configuration)
                .getUserRelationsByUserIds(userRelationRequest, options)
                .then((request) => request(axios, basePath));
        },
        /**
         * Get users by smart-device-id
         * @summary Get users by smart-device-id
         * @param {string} smartDeviceId smart device id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUsersBySmartDeviceId(
            smartDeviceId: string,
            options?: any
        ): AxiosPromise<GetUsersResponse> {
            return DeviceDataApiFp(configuration)
                .getUsersBySmartDeviceId(smartDeviceId, options)
                .then((request) => request(axios, basePath));
        },
    };
};

/**
 * DeviceDataApi - object-oriented interface
 * @export
 * @class DeviceDataApi
 * @extends {BaseAPI}
 */
export class DeviceDataApi extends BaseAPI {
    /**
     * Get smart device id by user id
     * @summary Get smart device id by user id
     * @param {number} userId user id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeviceDataApi
     */
    public getSmartDeviceIdByUserId(userId: number, options?: any) {
        return DeviceDataApiFp(this.configuration)
            .getSmartDeviceIdByUserId(userId, options)
            .then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get summary risk score by user id
     * @summary Get summary risk score by user id
     * @param {number} userId user id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeviceDataApi
     */
    public getSummaryRiskScoreByUserId(userId: number, options?: any) {
        return DeviceDataApiFp(this.configuration)
            .getSummaryRiskScoreByUserId(userId, options)
            .then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get user relations
     * @summary Get user relations
     * @param {UserRelationRequest} userRelationRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeviceDataApi
     */
    public getUserRelationsByUserIds(userRelationRequest: UserRelationRequest, options?: any) {
        return DeviceDataApiFp(this.configuration)
            .getUserRelationsByUserIds(userRelationRequest, options)
            .then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get users by smart-device-id
     * @summary Get users by smart-device-id
     * @param {string} smartDeviceId smart device id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeviceDataApi
     */
    public getUsersBySmartDeviceId(smartDeviceId: string, options?: any) {
        return DeviceDataApiFp(this.configuration)
            .getUsersBySmartDeviceId(smartDeviceId, options)
            .then((request) => request(this.axios, this.basePath));
    }
}

/**
 * MigrationApi - axios parameter creator
 * @export
 */
export const MigrationApiAxiosParamCreator = (configuration?: Configuration) => {
    return {
        /**
         * Migrate data from mongoDB to Postgres
         * @summary Migrate data from mongoDB to Postgres
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        threatmetrixMigration: async (options: any = {}): Promise<RequestArgs> => {
            const localVarPath = `/threatmetrix/migrate`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options };
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            const queryParameters = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                if (Object.prototype.hasOwnProperty.call(localVarQueryParameter, key)) {
                    queryParameters.set(key, localVarQueryParameter[key]);
                }
            }
            for (const key in options.query) {
                if (Object.prototype.hasOwnProperty.call(options.query, key)) {
                    queryParameters.set(key, options.query[key]);
                }
            }
            localVarUrlObj.search = new URLSearchParams(queryParameters).toString();
            const headersFromBaseOptions =
                baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {
                ...localVarHeaderParameter,
                ...headersFromBaseOptions,
                ...options.headers,
            };

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    };
};

/**
 * MigrationApi - functional programming interface
 * @export
 */
export const MigrationApiFp = (configuration?: Configuration) => {
    return {
        /**
         * Migrate data from mongoDB to Postgres
         * @summary Migrate data from mongoDB to Postgres
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async threatmetrixMigration(
            options?: any
        ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await MigrationApiAxiosParamCreator(
                configuration
            ).threatmetrixMigration(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs = {
                    ...localVarAxiosArgs.options,
                    url: (configuration?.basePath || basePath) + localVarAxiosArgs.url,
                };
                return axios.request(axiosRequestArgs);
            };
        },
    };
};

/**
 * MigrationApi - factory interface
 * @export
 */
export const MigrationApiFactory = (
    configuration?: Configuration,
    basePath?: string,
    axios?: AxiosInstance
) => {
    return {
        /**
         * Migrate data from mongoDB to Postgres
         * @summary Migrate data from mongoDB to Postgres
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        threatmetrixMigration(options?: any): AxiosPromise<void> {
            return MigrationApiFp(configuration)
                .threatmetrixMigration(options)
                .then((request) => request(axios, basePath));
        },
    };
};

/**
 * MigrationApi - object-oriented interface
 * @export
 * @class MigrationApi
 * @extends {BaseAPI}
 */
export class MigrationApi extends BaseAPI {
    /**
     * Migrate data from mongoDB to Postgres
     * @summary Migrate data from mongoDB to Postgres
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MigrationApi
     */
    public threatmetrixMigration(options?: any) {
        return MigrationApiFp(this.configuration)
            .threatmetrixMigration(options)
            .then((request) => request(this.axios, this.basePath));
    }
}

/**
 * SessionApi - axios parameter creator
 * @export
 */
export const SessionApiAxiosParamCreator = (configuration?: Configuration) => {
    return {
        /**
         * advert session - ADD_LISTING
         * @summary advert session - ADD_LISTING
         * @param {string} sessionId Session ID is a temporary identifier that is unique to the visitor\&#39;s session
         * @param {AdvertSession} advertSession
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        advertSession: async (
            sessionId: string,
            advertSession: AdvertSession,
            options: any = {}
        ): Promise<RequestArgs> => {
            // verify required parameter 'sessionId' is not null or undefined
            if (sessionId === null || sessionId === undefined) {
                throw new RequiredError(
                    'sessionId',
                    'Required parameter sessionId was null or undefined when calling advertSession.'
                );
            }
            // verify required parameter 'advertSession' is not null or undefined
            if (advertSession === null || advertSession === undefined) {
                throw new RequiredError(
                    'advertSession',
                    'Required parameter advertSession was null or undefined when calling advertSession.'
                );
            }
            const localVarPath = `/threatmetrix/advert/session/{session-id}`.replace(
                `{${'session-id'}}`,
                encodeURIComponent(String(sessionId))
            );
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options };
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            const queryParameters = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                if (Object.prototype.hasOwnProperty.call(localVarQueryParameter, key)) {
                    queryParameters.set(key, localVarQueryParameter[key]);
                }
            }
            for (const key in options.query) {
                if (Object.prototype.hasOwnProperty.call(options.query, key)) {
                    queryParameters.set(key, options.query[key]);
                }
            }
            localVarUrlObj.search = new URLSearchParams(queryParameters).toString();
            const headersFromBaseOptions =
                baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {
                ...localVarHeaderParameter,
                ...headersFromBaseOptions,
                ...options.headers,
            };
            const nonString = typeof advertSession !== 'string';
            const needsSerialization =
                nonString && configuration && configuration.isJsonMime
                    ? configuration.isJsonMime(localVarRequestOptions.headers['Content-Type'])
                    : nonString;
            localVarRequestOptions.data = needsSerialization
                ? JSON.stringify(advertSession !== undefined ? advertSession : {})
                : advertSession || '';

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * login session - LOGIN
         * @summary login session - LOGIN
         * @param {string} sessionId Session ID is a temporary identifier that is unique to the visitor\&#39;s session
         * @param {LoginSession} loginSession
         * @param {boolean} [save]
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        loginSession: async (
            sessionId: string,
            loginSession: LoginSession,
            save?: boolean,
            options: any = {}
        ): Promise<RequestArgs> => {
            // verify required parameter 'sessionId' is not null or undefined
            if (sessionId === null || sessionId === undefined) {
                throw new RequiredError(
                    'sessionId',
                    'Required parameter sessionId was null or undefined when calling loginSession.'
                );
            }
            // verify required parameter 'loginSession' is not null or undefined
            if (loginSession === null || loginSession === undefined) {
                throw new RequiredError(
                    'loginSession',
                    'Required parameter loginSession was null or undefined when calling loginSession.'
                );
            }
            const localVarPath = `/threatmetrix/login/session/{session-id}`.replace(
                `{${'session-id'}}`,
                encodeURIComponent(String(sessionId))
            );
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options };
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (save !== undefined) {
                localVarQueryParameter.save = save;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json';

            const queryParameters = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                if (Object.prototype.hasOwnProperty.call(localVarQueryParameter, key)) {
                    queryParameters.set(key, localVarQueryParameter[key]);
                }
            }
            for (const key in options.query) {
                if (Object.prototype.hasOwnProperty.call(options.query, key)) {
                    queryParameters.set(key, options.query[key]);
                }
            }
            localVarUrlObj.search = new URLSearchParams(queryParameters).toString();
            const headersFromBaseOptions =
                baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {
                ...localVarHeaderParameter,
                ...headersFromBaseOptions,
                ...options.headers,
            };
            const nonString = typeof loginSession !== 'string';
            const needsSerialization =
                nonString && configuration && configuration.isJsonMime
                    ? configuration.isJsonMime(localVarRequestOptions.headers['Content-Type'])
                    : nonString;
            localVarRequestOptions.data = needsSerialization
                ? JSON.stringify(loginSession !== undefined ? loginSession : {})
                : loginSession || '';

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * Registration session - ACCOUNT_CREATION
         * @summary Registration session - ACCOUNT_CREATION
         * @param {string} sessionId Session ID is a temporary identifier that is unique to the visitor\&#39;s session
         * @param {RegistrationSession} registrationSession
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        registrationSession: async (
            sessionId: string,
            registrationSession: RegistrationSession,
            options: any = {}
        ): Promise<RequestArgs> => {
            // verify required parameter 'sessionId' is not null or undefined
            if (sessionId === null || sessionId === undefined) {
                throw new RequiredError(
                    'sessionId',
                    'Required parameter sessionId was null or undefined when calling registrationSession.'
                );
            }
            // verify required parameter 'registrationSession' is not null or undefined
            if (registrationSession === null || registrationSession === undefined) {
                throw new RequiredError(
                    'registrationSession',
                    'Required parameter registrationSession was null or undefined when calling registrationSession.'
                );
            }
            const localVarPath = `/threatmetrix/registration/session/{session-id}`.replace(
                `{${'session-id'}}`,
                encodeURIComponent(String(sessionId))
            );
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options };
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            const queryParameters = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                if (Object.prototype.hasOwnProperty.call(localVarQueryParameter, key)) {
                    queryParameters.set(key, localVarQueryParameter[key]);
                }
            }
            for (const key in options.query) {
                if (Object.prototype.hasOwnProperty.call(options.query, key)) {
                    queryParameters.set(key, options.query[key]);
                }
            }
            localVarUrlObj.search = new URLSearchParams(queryParameters).toString();
            const headersFromBaseOptions =
                baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {
                ...localVarHeaderParameter,
                ...headersFromBaseOptions,
                ...options.headers,
            };
            const nonString = typeof registrationSession !== 'string';
            const needsSerialization =
                nonString && configuration && configuration.isJsonMime
                    ? configuration.isJsonMime(localVarRequestOptions.headers['Content-Type'])
                    : nonString;
            localVarRequestOptions.data = needsSerialization
                ? JSON.stringify(registrationSession !== undefined ? registrationSession : {})
                : registrationSession || '';

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    };
};

/**
 * SessionApi - functional programming interface
 * @export
 */
export const SessionApiFp = (configuration?: Configuration) => {
    return {
        /**
         * advert session - ADD_LISTING
         * @summary advert session - ADD_LISTING
         * @param {string} sessionId Session ID is a temporary identifier that is unique to the visitor\&#39;s session
         * @param {AdvertSession} advertSession
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async advertSession(
            sessionId: string,
            advertSession: AdvertSession,
            options?: any
        ): Promise<
            (axios?: AxiosInstance, basePath?: string) => AxiosPromise<{ [key: string]: object }>
        > {
            const localVarAxiosArgs = await SessionApiAxiosParamCreator(
                configuration
            ).advertSession(sessionId, advertSession, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs = {
                    ...localVarAxiosArgs.options,
                    url: (configuration?.basePath || basePath) + localVarAxiosArgs.url,
                };
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * login session - LOGIN
         * @summary login session - LOGIN
         * @param {string} sessionId Session ID is a temporary identifier that is unique to the visitor\&#39;s session
         * @param {LoginSession} loginSession
         * @param {boolean} [save]
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async loginSession(
            sessionId: string,
            loginSession: LoginSession,
            save?: boolean,
            options?: any
        ): Promise<
            (axios?: AxiosInstance, basePath?: string) => AxiosPromise<{ [key: string]: object }>
        > {
            const localVarAxiosArgs = await SessionApiAxiosParamCreator(configuration).loginSession(
                sessionId,
                loginSession,
                save,
                options
            );
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs = {
                    ...localVarAxiosArgs.options,
                    url: (configuration?.basePath || basePath) + localVarAxiosArgs.url,
                };
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * Registration session - ACCOUNT_CREATION
         * @summary Registration session - ACCOUNT_CREATION
         * @param {string} sessionId Session ID is a temporary identifier that is unique to the visitor\&#39;s session
         * @param {RegistrationSession} registrationSession
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async registrationSession(
            sessionId: string,
            registrationSession: RegistrationSession,
            options?: any
        ): Promise<
            (axios?: AxiosInstance, basePath?: string) => AxiosPromise<{ [key: string]: object }>
        > {
            const localVarAxiosArgs = await SessionApiAxiosParamCreator(
                configuration
            ).registrationSession(sessionId, registrationSession, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs = {
                    ...localVarAxiosArgs.options,
                    url: (configuration?.basePath || basePath) + localVarAxiosArgs.url,
                };
                return axios.request(axiosRequestArgs);
            };
        },
    };
};

/**
 * SessionApi - factory interface
 * @export
 */
export const SessionApiFactory = (
    configuration?: Configuration,
    basePath?: string,
    axios?: AxiosInstance
) => {
    return {
        /**
         * advert session - ADD_LISTING
         * @summary advert session - ADD_LISTING
         * @param {string} sessionId Session ID is a temporary identifier that is unique to the visitor\&#39;s session
         * @param {AdvertSession} advertSession
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        advertSession(
            sessionId: string,
            advertSession: AdvertSession,
            options?: any
        ): AxiosPromise<{ [key: string]: object }> {
            return SessionApiFp(configuration)
                .advertSession(sessionId, advertSession, options)
                .then((request) => request(axios, basePath));
        },
        /**
         * login session - LOGIN
         * @summary login session - LOGIN
         * @param {string} sessionId Session ID is a temporary identifier that is unique to the visitor\&#39;s session
         * @param {LoginSession} loginSession
         * @param {boolean} [save]
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        loginSession(
            sessionId: string,
            loginSession: LoginSession,
            save?: boolean,
            options?: any
        ): AxiosPromise<{ [key: string]: object }> {
            return SessionApiFp(configuration)
                .loginSession(sessionId, loginSession, save, options)
                .then((request) => request(axios, basePath));
        },
        /**
         * Registration session - ACCOUNT_CREATION
         * @summary Registration session - ACCOUNT_CREATION
         * @param {string} sessionId Session ID is a temporary identifier that is unique to the visitor\&#39;s session
         * @param {RegistrationSession} registrationSession
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        registrationSession(
            sessionId: string,
            registrationSession: RegistrationSession,
            options?: any
        ): AxiosPromise<{ [key: string]: object }> {
            return SessionApiFp(configuration)
                .registrationSession(sessionId, registrationSession, options)
                .then((request) => request(axios, basePath));
        },
    };
};

/**
 * SessionApi - object-oriented interface
 * @export
 * @class SessionApi
 * @extends {BaseAPI}
 */
export class SessionApi extends BaseAPI {
    /**
     * advert session - ADD_LISTING
     * @summary advert session - ADD_LISTING
     * @param {string} sessionId Session ID is a temporary identifier that is unique to the visitor\&#39;s session
     * @param {AdvertSession} advertSession
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SessionApi
     */
    public advertSession(sessionId: string, advertSession: AdvertSession, options?: any) {
        return SessionApiFp(this.configuration)
            .advertSession(sessionId, advertSession, options)
            .then((request) => request(this.axios, this.basePath));
    }

    /**
     * login session - LOGIN
     * @summary login session - LOGIN
     * @param {string} sessionId Session ID is a temporary identifier that is unique to the visitor\&#39;s session
     * @param {LoginSession} loginSession
     * @param {boolean} [save]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SessionApi
     */
    public loginSession(
        sessionId: string,
        loginSession: LoginSession,
        save?: boolean,
        options?: any
    ) {
        return SessionApiFp(this.configuration)
            .loginSession(sessionId, loginSession, save, options)
            .then((request) => request(this.axios, this.basePath));
    }

    /**
     * Registration session - ACCOUNT_CREATION
     * @summary Registration session - ACCOUNT_CREATION
     * @param {string} sessionId Session ID is a temporary identifier that is unique to the visitor\&#39;s session
     * @param {RegistrationSession} registrationSession
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SessionApi
     */
    public registrationSession(
        sessionId: string,
        registrationSession: RegistrationSession,
        options?: any
    ) {
        return SessionApiFp(this.configuration)
            .registrationSession(sessionId, registrationSession, options)
            .then((request) => request(this.axios, this.basePath));
    }
}
