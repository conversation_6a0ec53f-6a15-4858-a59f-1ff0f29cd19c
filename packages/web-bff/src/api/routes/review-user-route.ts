import express, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import jwtDecode from 'jwt-decode';
import type { JwtPayload } from '@gumtree/middleware/src/authorisation/api-authentication-middleware';
import logger from '../../logger';
import { reviewsAPI, reviewsWriteAPI } from '../clients/review-index';
import { threatmetrixAPI } from '../clients/threatmetrix-index';
import { userFetchAP<PERSON>, userInfoFetchAPI } from '../clients/user-index';
import {
    CanReviewerReviewRequest,
    CreateReviewRequestDirectionEnum,
} from '../clients/user-reviews';
import { conversationsAPI } from '../clients/conversations-index';
import { jwtAuthorizationApi } from '../clients/authorisation-index';
import { Message } from '../clients/conversations/api';
import { UserResponse } from '../clients/users';
import reviewUserMetrics from '../../metrics/modules/review-user-metrics';

export const shouldTriggerReview = (l1CategoryId, l2CategoryId) => {
    try {
        const FOR_SALE_L1_CATEGORY_ID = 2549;
        const MOTORS_L2_CATEGORY_IDS = [9311, 94, 10442, 4707, 1028, 10022, 10021];
        if (l1CategoryId === FOR_SALE_L1_CATEGORY_ID) {
            return true;
        } else if (l2CategoryId) {
            const result = MOTORS_L2_CATEGORY_IDS.includes(l2CategoryId);
            return result;
        }
        return false;
    } catch (e) {
        console.error(e);
        return false;
    }
};

const reviewUserRouter = express.Router();

reviewUserRouter.use(express.json() as RequestHandler);

const getUserAccounts = async (id: number) => {
    const { data } = await userFetchAPI.getUserById(id);
    return data;
};

reviewUserRouter.post('/reviews', async (req, res) => {
    const { userId } = jwtDecode<JwtPayload>(req.accessToken!);

    try {
        const [sellerId, buyerId, itemId, rating, categoryId] = [
            req.query.sellerId,
            req.query.buyerId,
            req.query.adId,
            req.query.rating,
            req.query.categoryId,
        ].map(Number);
        const [itemTitle] = [req.query.title].map(String);
        const feedback = req.query.feedback;
        const positiveFeedback = req.query.positiveFeedback;
        const negativeFeedback = req.query.negativeFeedback;

        const revieweeId = userId === sellerId ? buyerId : sellerId;
        const direction =
            userId === sellerId
                ? CreateReviewRequestDirectionEnum.S2B
                : CreateReviewRequestDirectionEnum.B2S;

        let feedbackConstruction;

        if (req.query.combinedFeedback) {
            feedbackConstruction = {
                tags: { ['positive']: positiveFeedback, ['negative']: negativeFeedback },
            };
        } else {
            feedbackConstruction = { tags: { [rating === 5 ? 'positive' : 'negative']: feedback } };
        }

        if (!req.query.isQA) {
            const isPermittedByTm = await isReviewPermittedByTm({ userId, revieweeId });

            if (!isPermittedByTm) {
                res.status(500).json(null);
                return;
            }
        }

        const reviewerData = await getUserAccounts(userId);
        const revieweeData = await getUserAccounts(revieweeId);

        if (!revieweeData?.accountIds?.length || !reviewerData?.accountIds?.length) {
            return;
        }

        const { data } = await reviewsWriteAPI.createReview({
            reviewerId: reviewerData?.accountIds[0],
            revieweeId: revieweeData?.accountIds[0],
            itemId,
            rating,
            itemTitle,
            categoryId,
            direction,
            feedback: feedbackConstruction,
            metaAttributes: {},
        });
        res.status(200).json(data);
    } catch (e: any) {
        logger.error('/reviews', 'bff/review-user-route', e);
        res.status(500).json(null);
    }
});

reviewUserRouter.post(`/reviewer/can-review`, async (req, res) => {
    const { userId, email } = jwtDecode<JwtPayload>(req.accessToken!);

    try {
        const { conversationId } = req.query as { conversationId: string };

        if (typeof req.query.conversationId !== 'string') {
            throw Error(
                `Expected string param "conversationId" but saw: ${req.query.conversationId}`
            );
        }

        const [adId, sellerId, buyerId, l1CategoryId, l2CategoryId] = [
            req.query.adId,
            req.query.sellerId,
            req.query.buyerId,
            req.query.l1CategoryId,
            req.query.l2CategoryId,
        ].map(Number);

        if (!shouldTriggerReview(l1CategoryId, l2CategoryId)) {
            res.status(200).json(false);
            return;
        }

        const revieweeId = userId === sellerId ? buyerId : sellerId;

        const isPermittedByMessageHistory = await isReviewPermittedByMessageHistory({
            conversationId,
            userId,
            revieweeId,
            email,
        });

        if (!isPermittedByMessageHistory) {
            res.status(200).json(false);
            return;
        }

        const reviewerData = await getUserAccounts(userId);
        const revieweeData = await getUserAccounts(revieweeId);

        if (
            (reviewerData?.accountIds && reviewerData?.accountIds?.length > 1) ||
            (revieweeData?.accountIds && revieweeData?.accountIds?.length > 1) ||
            !revieweeData?.accountIds?.length ||
            !reviewerData?.accountIds?.length
        ) {
            return;
        }

        const body: CanReviewerReviewRequest = {
            reviewer: {
                reviewerId: reviewerData?.accountIds[0],
            },
            revieweeId: revieweeData?.accountIds[0],
            itemId: adId,
        };

        const {
            data: { value: canReview },
        } = await reviewsAPI.canReviewerReview(body);

        if (!canReview) {
            res.status(200).json(false);
            return;
        }

        res.status(200).json(true);
    } catch (e: any) {
        logger.error('/reviewer/can-review', 'bff/review-user-route', e);
        res.status(500).json(null);
    }
});

reviewUserRouter.post(`/reviewee/list`, async (req, res) => {
    const l1CategoryId = req.body.l1CategoryId;
    const l2CategoryId = req.body.l2CategoryId;

    if (!shouldTriggerReview(l1CategoryId, l2CategoryId)) {
        res.status(200).json({ revieweeList: [] });
        return;
    }

    const { userId, email } = jwtDecode<JwtPayload>(req.accessToken!);
    const offset = 0;
    const {
        data: { token },
    } = await jwtAuthorizationApi.generateJwtToken({ userId, email });
    try {
        const times = { convsStart: performance.now(), reviewInfoList: [] as number[] } as {
            convsStart: number;
            convsEnd: number;
            reviewInfoList: number[];
            reviewInfoListEnd: number;
        };

        const { data } = await conversationsAPI.getAllConversations(
            userId,
            offset,
            `Bearer ${token}`
        );

        // strongly judgement by the conversations
        if (!data.conversations || data.conversations.length === 0) {
            res.status(200).json({ revieweeList: [] });
            return;
        }

        times.convsEnd = performance.now();

        // filter by the adId and the can review rule
        const adId = Number(req.body.adId);
        const conversations = data.conversations.filter((conversation) => {
            if (conversation.adId !== adId) {
                return false;
            }

            // judgement the canReview conversation
            const messages: Message[] = conversation.messages!!;
            const [sellerId, buyerId] = [conversation.sellerId, conversation.buyerId].map(Number);
            const revieweeId = userId === sellerId ? buyerId : sellerId;
            const isPermittedByMessageHistory = [
                () => hasMinMessagesCount(messages, MIN_MESSAGES_IN_CONVERSATION),
                () => isRevieeweLastMessageSentAtWithin28Days(messages, revieweeId),
                () => hasEachParticipantSentMinMessageCount(messages, userId),
            ].every((isPermitted) => isPermitted());
            return isPermittedByMessageHistory;
        });

        if (conversations.length === 0) {
            res.status(200).json({ revieweeList: [] });
            return;
        }

        // filter by the interface on the backend
        const canReviewRevieweeList: RevieweeListResponse[] = [];
        await Promise.all(
            conversations.map(async (conversation) => {
                const reviewInfoStart = performance.now();
                const [sellerId, buyerId] = [conversation.sellerId, conversation.buyerId].map(
                    Number
                );
                const revieweeId = userId === sellerId ? buyerId : sellerId;
                const reviewerData = await getUserAccounts(userId);
                const revieweeData = await getUserAccounts(revieweeId);
                if (
                    (reviewerData?.accountIds && reviewerData?.accountIds?.length > 1) ||
                    (revieweeData?.accountIds && revieweeData?.accountIds?.length > 1) ||
                    !revieweeData?.accountIds?.length ||
                    !reviewerData?.accountIds?.length
                ) {
                    return;
                }
                const body: CanReviewerReviewRequest = {
                    reviewer: {
                        reviewerId: reviewerData?.accountIds[0],
                    },
                    revieweeId: revieweeData?.accountIds[0],
                    itemId: adId,
                };

                const {
                    data: { value: canReview },
                } = await reviewsAPI.canReviewerReview(body);
                if (!canReview) {
                    return;
                }

                const reviewsData = await ratingReceived(revieweeData.accountIds);
                const lastMessageUpdatedAt = conversation.messages
                    ? conversation.messages[conversation.messages.length - 1].updatedAt
                    : '';
                const reviewInfoEnd = performance.now();
                times.reviewInfoList.push(reviewInfoEnd - reviewInfoStart);
                canReviewRevieweeList.push({
                    id: conversation.id,
                    adId: adId,
                    sellerId: sellerId,
                    buyerId: buyerId,
                    lastMessageUpdatedAt,
                    conversee: revieweeData,
                    reviews: reviewsData,
                });
            })
        );
        // sort by the lastMessageUpdatedAt desc
        if (canReviewRevieweeList.length > 0) {
            canReviewRevieweeList.sort((a, b) => {
                return (
                    Date.parse(b.lastMessageUpdatedAt || '') -
                    Date.parse(a.lastMessageUpdatedAt || '')
                );
            });
        }

        times.reviewInfoListEnd = performance.now();
        reviewUserMetrics.registerConversationRetrievalTime(times.convsEnd - times.convsStart);
        reviewUserMetrics.registerRevieweeListRetrievalTime(
            times.reviewInfoListEnd - times.convsStart
        );
        times.reviewInfoList.forEach((time) => {
            reviewUserMetrics.registerReviewInfoRetrievalTime(time);
        });

        res.status(200).json({ revieweeList: canReviewRevieweeList });
    } catch (e: any) {
        logger.error(`post /conversations for userId: ${userId}`, 'bff/message-centre', e);
        res.status(500).json({ conversations: [] });
    }
});

export default reviewUserRouter;

const MIN_MESSAGES_IN_CONVERSATION = 5;
const MIN_MESSAGES_FROM_PARTICIPANT = 2;

export async function getAccountIds(userId: number) {
    const { accountIds } = await getUserAccounts(userId);
    return accountIds;
}

export async function ratingReceived(accountIds: UserResponse['accountIds']) {
    try {
        if (accountIds?.length === 1) {
            const { data = {} } = await reviewsAPI.findRatingReceivedByRevieweeId(accountIds[0]);
            return data;
        } else {
            return;
        }
    } catch (e: any) {
        logger.error('/user-reviews', 'findRatingReceivedByRevieweeId', e);
        return null;
    }
}

export async function getPublicProfile(accountIds) {
    try {
        if (accountIds?.length === 1) {
            const { data = {} } = await userInfoFetchAPI.getAccountById(accountIds[0]);
            return data;
        } else {
            return;
        }
    } catch (e: any) {
        logger.error('/user-service', 'getAccountById', e);
        return null;
    }
}

async function isReviewPermittedByMessageHistory({
    conversationId,
    userId,
    revieweeId,
    email,
}: {
    conversationId: string;
    userId: number;
    revieweeId: number;
    email: string;
}) {
    const {
        data: { token },
    } = await jwtAuthorizationApi.generateJwtToken({ userId, email });
    const { data } = await conversationsAPI.getConversationById(
        userId,
        conversationId,
        `Bearer ${token}`
    );

    const messages: Message[] = data.messages!!;

    return [
        () => hasMinMessagesCount(messages, MIN_MESSAGES_IN_CONVERSATION),
        () => isRevieeweLastMessageSentAtWithin28Days(messages, revieweeId),
        () => hasEachParticipantSentMinMessageCount(messages, userId),
    ].every((isPermitted) => isPermitted());
}

async function isReviewPermittedByTm({
    userId,
    revieweeId,
}: {
    userId: number;
    revieweeId: number;
}) {
    try {
        const userDevice = await threatmetrixAPI.getSmartDeviceIdByUserId(userId);
        const revieweeDevice = await threatmetrixAPI.getSmartDeviceIdByUserId(revieweeId);

        return userDevice.data.smartDeviceId !== revieweeDevice.data.smartDeviceId;
    } catch (e: any) {
        logger.error('/getSmartDeviceIdByUserId', e);
        return null;
    }
}

function hasMinMessagesCount(messages: Message[], minMessagesCount: number): boolean {
    return messages && messages.length >= minMessagesCount;
}

function isRevieeweLastMessageSentAtWithin28Days(messages: Message[], revieweeId: number): boolean {
    let lastRevieweeMessage: Message | undefined = undefined;

    for (let i = messages.length - 1; i >= 0; i--) {
        const message = messages[i];
        if (message.userId === revieweeId) {
            lastRevieweeMessage = message;
            break;
        }
    }

    if (!lastRevieweeMessage) {
        return false;
    }

    const createdAtDate = new Date(lastRevieweeMessage.createdAt!);
    const currentDate = new Date();

    const timeDifference = currentDate.getTime() - createdAtDate.getTime();
    const daysDifference = timeDifference / (1000 * 60 * 60 * 24);

    return daysDifference <= 28;
}

function hasEachParticipantSentMinMessageCount(messages: Message[], userId: number): any {
    const userCounts = messages.reduce(
        (counts, message) => {
            if (message.userId === userId) {
                counts.userMessages++;
            } else {
                counts.revieweeMessages++;
            }

            return counts;
        },
        { userMessages: 0, revieweeMessages: 0 }
    );

    return (
        userCounts.userMessages >= MIN_MESSAGES_FROM_PARTICIPANT &&
        userCounts.revieweeMessages >= MIN_MESSAGES_FROM_PARTICIPANT
    );
}

interface RevieweeListResponse {
    id: string | undefined;
    adId: number | undefined;
    sellerId: number | undefined;
    buyerId: number | undefined;
    lastMessageUpdatedAt: string | undefined;
    conversee: UserResponse;
    reviews: {} | null | undefined;
}
