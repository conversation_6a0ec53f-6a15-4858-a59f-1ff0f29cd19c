 
import express, { Request, RequestHandler } from 'express';
import bodyParser from 'body-parser';
import globalAxios, { AxiosError } from 'axios';
import logger from '../../logger';

const loginRouter = express.Router();
loginRouter.use(bodyParser.urlencoded({ extended: true }) as RequestHandler);

const axiosInstance = globalAxios.create();

loginRouter.post(`/via-form`, async (req, res) => {
    try {
        delete req.headers.host;
        const formParams = `${new URLSearchParams(req.body)}`;
        await axiosInstance.request({
            method: 'POST',
            url: `${process.env.SELLER_INTERNAL_URL}/login`,
            headers: req.headers,
            data: formParams,
            maxRedirects: 0,
            // timeout: 12000,
        });
    } catch (e: any) {
        if (e.isAxiosError && (e as AxiosError)?.response?.status === 303) {
            const resHeaders = (e as AxiosError).response?.headers;
            Object.entries(resHeaders as Request['headers']).forEach(([key, value]) => {
                value && res.setHeader(key, value);
            });
            res.json({ location: resHeaders?.location });
        } else if (e.isAxiosError && e.response) {
            res.status(e.response.status).json(e.response.data);
        } else {
            logger.error(`POST /via-form failed`, 'bff/login-route', e);
            res.status(500);
        }
    }
});

// login or register
loginRouter.post('/via-social', async (req, res) => {
    try {
        const formParams = `${new URLSearchParams(req.body)}`;

        await axiosInstance.request({
            method: 'POST',
            url: `${process.env.SELLER_INTERNAL_URL}/login`,
            headers: req.headers,
            data: formParams,
            maxRedirects: 0,
        });

        res.status(500).json({ success: false });
    } catch (e: any) {
        if (e.isAxiosError && (e as AxiosError)?.response?.status === 303) {
            const resHeaders = (e as AxiosError).response?.headers as Request['headers'];

            Object.entries(resHeaders as Request['headers']).forEach(([key, value]) => {
                value && res.setHeader(key, value);
            });

            res.json({ success: true });
        } else {
            logger.error(`POST /via-social failed`, 'bff/login-route', e);
            res.status(500).json({ success: false });
        }
    }
});

export default loginRouter;
