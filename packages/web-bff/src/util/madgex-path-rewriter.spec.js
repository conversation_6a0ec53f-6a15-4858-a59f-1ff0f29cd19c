import madgexPathRewriter from './madgex-path-rewriter';

describe('madgex-path-rewriter', () => {
    it('removes /uk from the path', () => {
        expect(madgexPathRewriter('/jobs/uk/getasset/mock-asset-id')).toEqual(
            '/jobs/getasset/mock-asset-id'
        );
    });

    it('removes /srpsearch+ from the id', () => {
        expect(madgexPathRewriter('/jobs/getasset/srpsearch+mock-asset-id')).toEqual(
            '/jobs/getasset/mock-asset-id'
        );
    });

    ['_20.', '_74.'].forEach((imageSize) => {
        it(`removes the imageSize denoted by an underscore (e.g. ${imageSize}), as used by the App from the id`, () => {
            expect(madgexPathRewriter(`/jobs/getasset/mock-asset-id${imageSize}`)).toEqual(
                '/jobs/getasset/mock-asset-id'
            );
        });
    });

    ['?imageSize=300,250', '?imageSize=138,115'].forEach((imageSize) => {
        it(`removes the imageSize denoted by a queryParam (e.g. ${imageSize}), as used by the App`, () => {
            expect(madgexPathRewriter(`/jobs/getasset/mock-asset-id${imageSize}`)).toEqual(
                '/jobs/getasset/mock-asset-id'
            );
        });
    });

    it(`doesnt strip any other query params apart from imageSize`, () => {
        expect(
            madgexPathRewriter(`/jobs/getasset/mock-asset-id?imageSize=300,250&test=yes`)
        ).toEqual('/jobs/getasset/mock-asset-id?test=yes');

        expect(
            madgexPathRewriter(`/jobs/getasset/mock-asset-id?another-example=here&test=yes`)
        ).toEqual('/jobs/getasset/mock-asset-id?another-example=here&test=yes');
    });
});
