import dotenv from 'dotenv';
import logger from './logger';
import env from './env';

jest.mock('dotenv', () => ({
    config: jest.fn(() => ({ error: true })),
}));
jest.mock('./logger', () => ({
    error: jest.fn(),
}));

describe('Environment config', () => {
    let exit;
    beforeAll(() => {
        exit = process.exit;
        process.exit = jest.fn();
    });

    afterAll(() => {
        process.exit = exit;
    });

    it('loads variables', () => {
        dotenv.config.mockImplementationOnce(() => ({}));
        env();
        expect(dotenv.config).toHaveBeenCalledWith({});
    });

    it('loads variables from custom path', () => {
        const path = './properties';
        dotenv.config.mockImplementationOnce(() => ({}));
        env(path);
        expect(dotenv.config).toHaveBeenCalledWith({ path });
    });

    it('if properties not present log error', () => {
        dotenv.config.mockImplementationOnce(() => ({ error: true }));
        env();
        expect(logger.error).toHaveBeenCalled();
    });

    it('if properties not present exit', () => {
        dotenv.config.mockImplementation(() => ({ error: true }));
        env();
        expect(process.exit).toHaveBeenCalled();
    });
});
