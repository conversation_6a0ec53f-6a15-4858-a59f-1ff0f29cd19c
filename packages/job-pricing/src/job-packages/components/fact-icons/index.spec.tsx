import React from 'react';
import { render, screen } from '@testing-library/react';

import FactIcons from './index';

const defaultProps = {
    factIcons: [
        {
            factId: 1,
        },
        {
            factId: 2,
        },
        {
            factId: 3,
        },
    ],
};

type Props = React.ComponentProps<typeof FactIcons>;

const renderFactIcons = (extraProps = {} as Props) =>
    render(<FactIcons {...defaultProps} {...extraProps} />);

describe('FactIcons', () => {
    describe('Component', () => {
        it('renders', () => {
            const SUT = renderFactIcons();
            expect(SUT.asFragment()).toMatchInlineSnapshot(`
                <DocumentFragment>
                  <section
                    class="job-pricing-fact-icons"
                  >
                    <div
                      class="container"
                    >
                      <div
                        class="grid grid--container"
                        style="display: flex; justify-content: center;"
                      >
                        <div
                          class="grid grid--col-11 grid--col-10-l grid--col-11-xl"
                        >
                          <header>
                            <h2
                              class="job-pricing-fact-icons-title"
                            >
                              Advertise on the UK's No.1 classifieds site
                            </h2>
                          </header>
                          <article
                            class="job-pricing-fact-icons-items"
                          >
                            <div
                              class="grid grid--container"
                              style="display: flex; justify-content: center;"
                            >
                              <div
                                class="grid grid--item grid--col-6 grid--col-3-xl"
                              >
                                <div
                                  class="job-pricing-fact-icons-item"
                                >
                                  <div
                                    class="job-pricing-fact-icons-item-icon"
                                  />
                                  <article
                                    class="job-pricing-fact-icons-item-content"
                                  >
                                    <header>
                                      <h3 />
                                    </header>
                                    <p />
                                  </article>
                                </div>
                              </div>
                              <div
                                class="grid grid--item grid--col-6 grid--col-3-xl"
                              >
                                <div
                                  class="job-pricing-fact-icons-item"
                                >
                                  <div
                                    class="job-pricing-fact-icons-item-icon"
                                  />
                                  <article
                                    class="job-pricing-fact-icons-item-content"
                                  >
                                    <header>
                                      <h3 />
                                    </header>
                                    <p />
                                  </article>
                                </div>
                              </div>
                              <div
                                class="grid grid--item grid--col-6 grid--col-3-xl"
                              >
                                <div
                                  class="job-pricing-fact-icons-item"
                                >
                                  <div
                                    class="job-pricing-fact-icons-item-icon"
                                  />
                                  <article
                                    class="job-pricing-fact-icons-item-content"
                                  >
                                    <header>
                                      <h3 />
                                    </header>
                                    <p />
                                  </article>
                                </div>
                              </div>
                            </div>
                          </article>
                        </div>
                      </div>
                    </div>
                  </section>
                </DocumentFragment>
            `);
        });

        it('renders the title text', () => {
            renderFactIcons();
            expect(screen.getByText('Advertise on the UK\'s No.1 classifieds site')).toBeInTheDocument();
        });
    });
});
