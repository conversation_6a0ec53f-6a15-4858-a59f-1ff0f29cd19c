import React from 'react';
import { array } from 'prop-types';

import { Grid } from '@gumtree/ui-library';

import Wrapper from '../wrapper';

import FactIcon from './fact-icon';

import './fact-icons.scss';

const FactIcons = ({ factIcons }) => (
    <section className="job-pricing-fact-icons">
        <Wrapper>
            <header>
                <h2 className="job-pricing-fact-icons-title">
                    Advertise on the UK's No.1 classifieds site
                </h2>
            </header>
            <article className="job-pricing-fact-icons-items">
                <Grid container justifyContent="center">
                    {factIcons.map((factIcon) => (
                        <Grid key={`${factIcon.factId}`} item col={6} colXl={3}>
                            <FactIcon factIcon={factIcon} />
                        </Grid>
                    ))}
                </Grid>
            </article>
        </Wrapper>
    </section>
);

FactIcons.propTypes = {
    factIcons: array.isRequired,
};

export default FactIcons;
