import React from 'react';

import { Grid, Image } from '@gumtree/ui-library';

import Wrapper from '../wrapper';

import './companies.scss';

import justEat from './images/justeat.png';
import handy from './images/handy.png';
import nandos from './images/nandos.png';
import morrisons from './images/morrisons.png';
import citySprint from './images/citysprint.png';
import lidl from './images/lidl.png';
import sodexo from './images/sodexo.png';

const Companies = () => {
    return (
        <section className="job-pricing-companies">
            <Wrapper>
                <section className="job-pricing-companies-header">
                    <h2 className="job-pricing-companies-title">You're in good company</h2>
                    <p className="job-pricing-companies-subtitle">
                        We work with hundreds of companies from small businesses to large enterprise
                    </p>
                </section>
                <Grid className="job-pricing-companies-items" container justifyContent="center">
                    <Grid item col={3} colXl={2}>
                        <Image src={justEat} alt="Just Eat Company Logo" />
                    </Grid>
                    <Grid item col={3} colXl={2}>
                        <Image src={handy} alt="Handy Company Logo" />
                    </Grid>
                    <Grid item col={3} colXl={2}>
                        <Image className="nandos" src={nandos} alt="Nandos Company Logo" />
                    </Grid>
                    <Grid item col={3} colXl={2}>
                        <Image src={morrisons} alt="Morrison Company Logo" />
                    </Grid>
                    <Grid className="job-pricing-companies-row-space" item col={3} colXl={2}>
                        <Image src={citySprint} alt="City Sprint Company Logo" />
                    </Grid>
                    <Grid className="job-pricing-companies-row-space" item col={3} colXl={2}>
                        <Image src={lidl} alt="Lidl Company Logo" />
                    </Grid>
                    <Grid className="job-pricing-companies-row-space" item col={3} colXl={2}>
                        <Image src={sodexo} alt="Sodexo Company Logo" />
                    </Grid>
                </Grid>
            </Wrapper>
        </section>
    );
};

export default Companies;
