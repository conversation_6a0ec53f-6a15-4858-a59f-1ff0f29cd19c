import React from 'react';
import { renderWithThemeRender } from '@gumtree/shared/src/test-utils/mock-theme';
import { render, screen } from '@testing-library/react';

import { redirectToUrl } from '@gumtree/ui-library/src/utils/browser-service';
import { trackV2 as trackV2 } from '@gumtree/shell/src/common/common-actions';

import userEvent from '@testing-library/user-event';
import Marketing from './index';

const renderMarketing = () => renderWithThemeRender(render, <Marketing />);

jest.mock('@gumtree/ui-library/src/utils/browser-service', () => ({
    redirectToUrl: jest.fn(),
}));

jest.mock('@gumtree/shell/src/common/common-actions', () => ({
    trackV2: jest.fn(),
}));

describe('Marketing', () => {
    it('renders', () => {
        const SUT = renderMarketing();
        expect(SUT.asFragment()).toMatchSnapshot();
    });

    it('renders a post job ad button', () => {
        renderMarketing();
        expect(screen.getByRole('button', { name: 'Post a standard job ad' })).toBeInTheDocument();
    });

    it('tracks button clicks with action "PostAdBegin"', () => {
        renderMarketing();
        const button = screen.getByRole('button', { name: 'Post a standard job ad' });
        userEvent.click(button);

        expect(trackV2).toHaveBeenCalledWith('PostAdBeginStandard2', 1, { eventValue: 3900 });
    });

    it('redirects to the url', (done) => {
        renderMarketing();
        const button = screen.getByRole('button', { name: 'Post a standard job ad' });
        userEvent.click(button);

        setTimeout(() => {
            expect(redirectToUrl).toHaveBeenCalledWith(
                'https://recruiters.gumtree.com/post-a-job/?ProductId=1'
            );
            done();
        }, 150);
    });
});
