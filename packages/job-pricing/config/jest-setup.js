// Extends use of jest dom throughout all tests - https://github.com/testing-library/jest-dom#usage
require('@testing-library/jest-dom');

const oldWindowLocation = window.location;

beforeAll(() => {
    delete window.location;

    window.location = Object.defineProperties(
        {},
        {
            ...Object.getOwnPropertyDescriptors(oldWindowLocation),
            assign: {
                configurable: true,
                value: jest.fn(),
            },
        }
    );
});
afterAll(() => {
    // restore `window.location` to the original `jsdom`
    // `Location` object
    window.location = oldWindowLocation;
});
