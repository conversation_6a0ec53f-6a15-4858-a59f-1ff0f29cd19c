export const propertyToShareSellerToClient = {
    formErrors: {},
    categoryId: 12183,
    locationId: 203,
    postcode: 'TW91EL',
    visibleOnMap: true,
    area: null,
    termsAgreed: null,
    title: '',
    description: '',
    previousContactName: null,
    contactName: 'Tiffany',
    previousContactEmail: null,
    contactEmail: '<EMAIL>',
    contactTelephone: null,
    contactUrl: null,
    useEmail: true,
    usePhone: false,
    useUrl: false,
    checkoutVariationId: null,
    mainImageId: 0,
    imageIds: [],
    youtubeLink: null,
    websiteUrl: 'http://',
    firstName: null,
    lastName: null,
    emailAddress: '<EMAIL>',
    telephoneNumber: null,
    password: null,
    optInMarketing: true,
    vrmStatus: 'VRM_NONE',
    attributes: {
        property_couples: null,
        price: null,
        price_frequency: null,
        available_date: null,
        seller_type: 'private',
        property_type: null,
        property_room_type: null,
    },
    features: {
        WEBSITE_URL: { selected: false, productName: 'WEBSITE_URL' },
        FEATURED: { selected: false, productName: 'FEATURE_7_DAY' },
        URGENT: { selected: false, productName: 'URGENT' },
        SPOTLIGHT: { selected: false, productName: 'HOMEPAGE_SPOTLIGHT' },
    },
};
