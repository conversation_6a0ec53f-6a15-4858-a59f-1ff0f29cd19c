export const proPropertyToRentPostResponse = {
    panels: [
        'category',
        'location',
        'seller-type',
        'ad-title',
        'images',
        'description',
        'attribute-panel',
        'website-link',
        'bump',
        'contact-details-pro',
        'overall-price',
        'confirmation',
    ],
    form: {
        formErrors: {},
        categoryId: 12181,
        locationId: 203,
        postcode: 'TW91EL',
        visibleOnMap: true,
        area: null,
        termsAgreed: null,
        title: 'Property / To Rent',
        description:
            'Property / To RentProperty / To RentProperty / To RentProperty / To RentProperty / To Rent',
        previousContactName: null,
        contactName: 'Tiffany',
        previousContactEmail: null,
        contactEmail: '<EMAIL>',
        contactTelephone: null,
        contactUrl: null,
        useEmail: true,
        usePhone: false,
        useUrl: false,
        checkoutVariationId: null,
        mainImageId: null,
        imageIds: [],
        youtubeLink: null,
        websiteUrl: 'http://gumtree.com',
        firstName: null,
        lastName: null,
        emailAddress: null,
        telephoneNumber: null,
        password: null,
        optInMarketing: null,
        vrmStatus: 'VRM_NONE',
        attributes: {
            property_number_beds: '5',
            price: '123',
            price_frequency: 'per_week',
            seller_type: 'trade',
            available_date: '21/10/2023',
            property_type: 'flat',
        },
        features: { FEATURED: { selected: false, productName: 'FEATURE_7_DAY' } },
        removeDraft: null,
    },
    pricingMetadata: {
        insertionPrice: {
            productName: 'INSERTION',
            price: 0.0,
            includedInPackage: false,
            clientValue: '0.00',
            displayValue: '&pound;0.00',
            productType: 'INSERTION',
            free: true,
            value: 'INSERTION',
        },
        bumpUpPrice: {
            productName: 'BUMP_UP',
            price: 17.99,
            includedInPackage: false,
            clientValue: '17.99',
            displayValue: '&pound;17.99',
            productType: 'BUMP_UP',
            free: false,
            value: 'BUMP_UP',
        },
        featureOptions: {
            SPOTLIGHT: {
                type: 'SPOTLIGHT',
                expiryDescription: null,
                active: false,
                prices: [
                    {
                        productName: 'HOMEPAGE_SPOTLIGHT',
                        price: 39.99,
                        includedInPackage: false,
                        clientValue: '39.99',
                        displayValue: '7 days - &pound;39.99',
                        productType: 'SPOTLIGHT',
                        free: false,
                        value: 'HOMEPAGE_SPOTLIGHT',
                    },
                ],
                description:
                    'Your ad will appear on the Gumtree homepage and will be seen by millions of people.',
            },
            FEATURED: {
                type: 'FEATURED',
                expiryDescription: null,
                active: false,
                prices: [
                    {
                        productName: 'FEATURE_3_DAY',
                        price: 29.99,
                        includedInPackage: false,
                        clientValue: '29.99',
                        displayValue: '3 days - &pound;29.99',
                        productType: 'FEATURED',
                        free: false,
                        value: 'FEATURE_3_DAY',
                    },
                    {
                        productName: 'FEATURE_7_DAY',
                        price: 49.99,
                        includedInPackage: false,
                        clientValue: '49.99',
                        displayValue: '7 days - &pound;49.99',
                        productType: 'FEATURED',
                        free: false,
                        value: 'FEATURE_7_DAY',
                    },
                    {
                        productName: 'FEATURE_14_DAY',
                        price: 79.99,
                        includedInPackage: false,
                        clientValue: '79.99',
                        displayValue: '14 days - &pound;79.99',
                        productType: 'FEATURED',
                        free: false,
                        value: 'FEATURE_14_DAY',
                    },
                ],
                description:
                    'Up to 7 times more views and replies*. Your ad will appear at the top of the listings for 3, 7 or 14 days.',
            },
            WEBSITE_URL: {
                type: 'WEBSITE_URL',
                expiryDescription: '36497 days left',
                active: true,
                prices: null,
                description: 'Website link',
            },
            URGENT: {
                type: 'URGENT',
                expiryDescription: null,
                active: false,
                prices: [
                    {
                        productName: 'URGENT',
                        price: 29.99,
                        includedInPackage: false,
                        clientValue: '29.99',
                        displayValue: '7 days - &pound;29.99',
                        productType: 'URGENT',
                        free: false,
                        value: 'URGENT',
                    },
                ],
                description:
                    'Up to 3 times more views and replies*. Perfect if you need to sell, rent or hire quickly.',
            },
            NOT_VISIBLE_PRODUCT: {
                type: 'NOT_VISIBLE_PRODUCT',
                expiryDescription: null,
                active: false,
                prices: [],
                description: 'Products that are not visible on edit screen.',
            },
        },
    },
    postAdImages: [],
    maxNumberOfImages: null,
    panelAttributes: {},
    postAdAttributeGroups: [
        {
            label: 'Property details',
            panelId: 'attribute-panel',
            id: 'flats-houses-details-group',
            highPriority: false,
            attributes: {
                price: {
                    mandatory: true,
                    type: 'CURRENCY',
                    label: 'Rent',
                    displayLabel: true,
                    values: [],
                    id: 'price',
                    description: null,
                    priceSensitive: false,
                    selectedValue: { present: false },
                },
                price_frequency: {
                    mandatory: true,
                    type: 'RADIO',
                    label: 'Rent period',
                    displayLabel: true,
                    values: [
                        {
                            value: 'per_month',
                            displayValue: 'Monthly',
                            selected: false,
                            disabled: false,
                        },
                        {
                            value: 'per_week',
                            displayValue: 'Weekly',
                            selected: true,
                            disabled: false,
                        },
                    ],
                    id: 'price_frequency',
                    description: null,
                    priceSensitive: false,
                    selectedValue: { present: true },
                },
                available_date: {
                    mandatory: true,
                    type: 'DATE',
                    label: 'Date available',
                    displayLabel: true,
                    values: [],
                    id: 'available_date',
                    description: null,
                    priceSensitive: false,
                    selectedValue: { present: false },
                },
                property_type: {
                    mandatory: true,
                    type: 'DROPDOWN',
                    label: 'Property type',
                    displayLabel: true,
                    values: [
                        {
                            value: '',
                            displayValue: 'Please select...',
                            selected: false,
                            disabled: false,
                        },
                        { value: 'flat', displayValue: 'Flat', selected: true, disabled: false },
                        { value: 'house', displayValue: 'House', selected: false, disabled: false },
                        { value: 'other', displayValue: 'Other', selected: false, disabled: false },
                    ],
                    id: 'property_type',
                    description: null,
                    priceSensitive: false,
                    selectedValue: { present: true },
                },
                property_number_beds: {
                    mandatory: true,
                    type: 'DROPDOWN',
                    label: 'No. of Bedrooms',
                    displayLabel: true,
                    values: [
                        {
                            value: '',
                            displayValue: 'Please select...',
                            selected: false,
                            disabled: false,
                        },
                        { value: '0', displayValue: 'Studio', selected: false, disabled: false },
                        { value: '1', displayValue: '1', selected: false, disabled: false },
                        { value: '2', displayValue: '2', selected: false, disabled: false },
                        { value: '3', displayValue: '3', selected: false, disabled: false },
                        { value: '4', displayValue: '4', selected: false, disabled: false },
                        { value: '5', displayValue: '5', selected: true, disabled: false },
                        { value: '6', displayValue: '6', selected: false, disabled: false },
                        { value: '7', displayValue: '7', selected: false, disabled: false },
                        { value: '8', displayValue: '8', selected: false, disabled: false },
                        { value: '9', displayValue: '9', selected: false, disabled: false },
                        { value: '10', displayValue: '10', selected: false, disabled: false },
                        { value: '11', displayValue: '11', selected: false, disabled: false },
                        { value: '12', displayValue: '12', selected: false, disabled: false },
                        { value: '13', displayValue: '13', selected: false, disabled: false },
                        { value: '14', displayValue: '14', selected: false, disabled: false },
                        { value: '15', displayValue: '15', selected: false, disabled: false },
                        { value: '16', displayValue: '16', selected: false, disabled: false },
                        { value: '17', displayValue: '17', selected: false, disabled: false },
                        { value: '18', displayValue: '18', selected: false, disabled: false },
                        { value: '19', displayValue: '19', selected: false, disabled: false },
                        { value: '20', displayValue: '20', selected: false, disabled: false },
                    ],
                    id: 'property_number_beds',
                    description: null,
                    priceSensitive: false,
                    selectedValue: { present: true },
                },
            },
            formAttributes: {
                price: {
                    attributeName: 'price',
                    attributeDisplayName: 'Rent',
                    value: '123',
                    valueDisplayName: '123',
                },
                price_frequency: {
                    attributeName: 'price_frequency',
                    attributeDisplayName: 'Rent period',
                    value: 'per_week',
                    valueDisplayName: 'Weekly',
                },
                available_date: {
                    attributeName: 'available_date',
                    attributeDisplayName: 'Date available',
                    value: '21/10/2023',
                    valueDisplayName: '21/10/2023',
                },
                property_type: {
                    attributeName: 'property_type',
                    attributeDisplayName: 'Property type',
                    value: 'flat',
                    valueDisplayName: 'Flat',
                },
                property_number_beds: {
                    attributeName: 'property_number_beds',
                    attributeDisplayName: 'No. of Bedrooms',
                    value: '5',
                    valueDisplayName: '5',
                },
            },
            panelIdOrDefault: 'attribute-panel',
        },
    ],
    sellerType: {
        sellerType: 'trade',
        label: 'Are you acting as an agent?',
        sellerTypeSelected: true,
        text: null,
        popUp: null,
        id: 'seller-type-group',
        attribute: {
            mandatory: true,
            type: null,
            label: 'Are you acting as an agent?',
            displayLabel: null,
            values: [
                { value: 'trade', displayValue: 'Yes', selected: true, disabled: false },
                { value: 'private', displayValue: 'No', selected: false, disabled: true },
            ],
            id: 'seller-type-group',
            description: null,
            priceSensitive: true,
            selectedValue: { present: true },
        },
    },
    totalPrice: 0.0,
    showInsertionPrice: false,
    supportsChangeCategory: false,
    supportsChangeLocation: false,
    supportsContactUrl: false,
    supportsChangeVisibleOnMap: true,
    supportsPostToAnyLocation: false,
    mapDetails: {
        latitude: 51.457163,
        longitude: -0.307661,
        radius: 3.485,
    },
    locationCrumb: [
        { id: 10000393, name: 'England' },
        { id: 10000344, name: 'London' },
        { id: 375, name: 'South West London' },
        { id: 203, name: 'Richmond' },
    ],
    categoryCrumb: [
        {
            id: 10201,
            name: 'Property',
            seoName: 'flats-houses',
            children: true,
            selected: false,
            childrenItems: [],
            drilled: false,
        },
        {
            id: 12181,
            name: 'To Rent',
            seoName: 'property-to-rent',
            children: false,
            selected: true,
            childrenItems: [],
            drilled: false,
        },
    ],
    validCategorySelected: true,
    validLocationSelected: true,
    legal: null,
    descriptionHint:
        '<h3>Remember to:</h3><ul><li>Include the EPC (Energy Performance Certificate) ratings</li><li>Mention any other costs that need to be paid, such as a deposit, utility bills, or agency fees.</li></ul>',
    imagesHint: null,
    advertId: 1001477337,
    status: 'FORM',
    contactEmails: ['<EMAIL>'],
    userLoggedIn: true,
    alternativeCategoryId: null,
    priceGuidance: null,
    gaEvents: null,
};
