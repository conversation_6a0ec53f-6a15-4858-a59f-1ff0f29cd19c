import { manageMyAdsModel } from '../../model/manage-my-ads';

 
import singleAdData from '../../fixtures/manage-my-ads/single-ad.json';
import singleAdWithFeaturesData from '../../fixtures/manage-my-ads/single-ad-with-features.json';
import noFeaturesData from '../../fixtures/manage-my-ads/no-features-data.json';

import { generateUniqueId } from '../../utils';

describe('manage-my-ads-promote.cy.ts', () => {
    const singleAdPageId = generateUniqueId();
    const noFeaturesPageId = generateUniqueId();

    const mockAndVisit = (data) => {
        cy.addMockEndpoint({
            matchOn: {
                url: `/manage/ads?mad-filter-search=Update&status=ACTIVE_ADS&page=${singleAdPageId}`,
                method: 'GET',
            },
            respondWith: {
                json: data,
                status: 200,
            },
        });

        cy.addMockEndpoint({
            matchOn: {
                url: `/manage/ads/buy-features/`,
                method: 'POST',
            },
            redirectTo: {
                location: `${Cypress.config().baseUrl}/?version=true&this-is-a-dummy=checkout-page`,
                status: 303,
            },
        });

        cy.visit(`/manage/ads?filter=active&page=${singleAdPageId}`);
    };

    it('The promote panel on the Manage my Ads page', () => {
        mockAndVisit(singleAdData);

        // Promote Ad Workflow
        cy.get(manageMyAdsModel.promoteButton).eq(0).click();

        cy.get(manageMyAdsModel.buyFeaturesBanner.container).should('not.exist');

        cy.get(manageMyAdsModel.buyFeaturesBanner.checkbox('urgent')).click({ force: true });
        cy.get(manageMyAdsModel.buyFeaturesBanner.container).should('be.visible');

        cy.get(manageMyAdsModel.buyFeaturesBanner.price).should('contain.text', 'Total £3.20');
        cy.get(manageMyAdsModel.buyFeaturesBanner.features).should(
            'contain.text',
            'Promotion features - 1'
        );

        cy.get(manageMyAdsModel.buyFeaturesBanner.checkbox('featured')).click({ force: true });
        cy.get(manageMyAdsModel.buyFeaturesBanner.dropdown).select(2);

        cy.get(manageMyAdsModel.buyFeaturesBanner.price).should('contain.text', 'Total £7.50');
        cy.get(manageMyAdsModel.buyFeaturesBanner.features).should(
            'contain.text',
            'Promotion features - 2'
        );

        cy.get(manageMyAdsModel.buyFeaturesBanner.continue).click();

        cy.url().should('contain', '/?version=true&this-is-a-dummy=checkout-page');
    });

    it('When no features are available', () => {
        cy.addMockEndpoint({
            matchOn: {
                url: `/manage/ads?mad-filter-search=Update&status=ACTIVE_ADS&page=${noFeaturesPageId}`,
                method: 'GET',
            },
            respondWith: {
                json: noFeaturesData,
                status: 200,
            },
        });

        cy.visit(`/manage/ads?filter=active&page=${noFeaturesPageId}`);

        cy.get(manageMyAdsModel.promoteButton).eq(0).click();

        cy.get(manageMyAdsModel.adFeatureCardContainer).eq(0).should('contain.text', '7 days left');
        cy.get(manageMyAdsModel.adFeatureCardContainer).eq(1).should('contain.text', '6 days left');
        cy.get(manageMyAdsModel.adFeatureCardContainer).eq(2).should('contain.text', 'Needs image');
    });

    describe('Features tooltip', () => {
        it('Featured - 7 days purchased', () => {
            mockAndVisit(singleAdWithFeaturesData);

            // When: user click on the Promote button
            cy.get(manageMyAdsModel.promoteButton).eq(0).click();
            cy.get(manageMyAdsModel.buyFeaturesBanner.container).should('not.exist');

            // Then: the correct label should be displayed
            cy.get(manageMyAdsModel.adFeatureCardContainer)
                .eq(0)
                .should('contain.text', '7 days left');

            // When: user click on the tooltip trigger
            cy.get(manageMyAdsModel.adFeatureExpiresTooltip.trigger.featured)
                .should('be.visible')
                .click();

            // Then: the correct tooltip content should be displayed
            cy.get(manageMyAdsModel.adFeatureExpiresTooltip.content.featured)
                .should('be.visible')
                .should(
                    'have.text',
                    'Your ad is already in the section at the top of category page in the list of Featured ads. You will be able to make it Featured again in 7 days.'
                );
        });

        it('Urgent - 7 days purchased', () => {
            mockAndVisit(singleAdWithFeaturesData);

            // When: user click on the Promote button
            cy.get(manageMyAdsModel.promoteButton).eq(0).click();
            cy.get(manageMyAdsModel.buyFeaturesBanner.container).should('not.exist');

            // Then: the correct label should be displayed
            cy.get(manageMyAdsModel.adFeatureCardContainer)
                .eq(1)
                .should('contain.text', '7 days left');

            // When: user click on the tooltip trigger
            cy.get(manageMyAdsModel.adFeatureExpiresTooltip.trigger.urgent)
                .should('be.visible')
                .click();

            // Then: the correct tooltip content should be displayed
            cy.get(manageMyAdsModel.adFeatureExpiresTooltip.content.urgent)
                .should('be.visible')
                .should(
                    'have.text',
                    'Your ad already has an Urgent label. The option to make your ad Urgent again will become available in 7 days.'
                );
        });

        it('Spotlight - 7 days purchased', () => {
            mockAndVisit(singleAdWithFeaturesData);

            // When: user click on the Promote button
            cy.get(manageMyAdsModel.promoteButton).eq(0).click();
            cy.get(manageMyAdsModel.buyFeaturesBanner.container).should('not.exist');

            // Then: the correct label should be displayed
            cy.get(manageMyAdsModel.adFeatureCardContainer)
                .eq(2)
                .should('contain.text', '7 days left');

            // When: user click on the tooltip trigger
            cy.get(manageMyAdsModel.adFeatureExpiresTooltip.trigger.spotlight)
                .should('be.visible')
                .click();

            // Then: the correct tooltip content should be displayed
            cy.get(manageMyAdsModel.adFeatureExpiresTooltip.content.spotlight)
                .should('be.visible')
                .should(
                    'have.text',
                    'Your ad already appears in the list of Spotlight ads on the Homepage. The option to make your ad Spotlight again will become available in 7 days.'
                );
        });

        it('Spotlight - requires image', () => {
            singleAdWithFeaturesData.model.adverts['1001641601'].expires.HOMEPAGE_SPOTLIGHT =
                'Needs image';

            mockAndVisit(singleAdWithFeaturesData);

            // When: user click on the Promote button
            cy.get(manageMyAdsModel.promoteButton).eq(0).click();
            cy.get(manageMyAdsModel.buyFeaturesBanner.container).should('not.exist');

            // Then: the correct label should be displayed
            cy.get(manageMyAdsModel.adFeatureCardContainer)
                .eq(2)
                .should('contain.text', 'Needs image');

            // When: user click on the tooltip trigger
            cy.get(manageMyAdsModel.adFeatureExpiresTooltip.trigger.spotlight)
                .should('be.visible')
                .click();

            // Then: the correct tooltip content should be displayed
            cy.get(manageMyAdsModel.adFeatureExpiresTooltip.content.spotlight)
                .should('be.visible')
                .should(
                    'have.text',
                    'Please add at least 1 image to your ad to be able to Spotlight it.'
                );
        });

        it('BumpUp - has just been posted', () => {
            mockAndVisit(singleAdWithFeaturesData);

            // When: user click on the Promote button
            cy.get(manageMyAdsModel.promoteButton).eq(0).click();
            cy.get(manageMyAdsModel.buyFeaturesBanner.container).should('not.exist');

            // Then: the correct label should be displayed
            cy.get(manageMyAdsModel.adFeatureCardContainer)
                .eq(3)
                .should('contain.text', 'Becomes available 1h after posting');

            // When: user click on the tooltip trigger
            cy.get(manageMyAdsModel.adFeatureExpiresTooltip.trigger.bumpup)
                .should('be.visible')
                .click();

            // Then: the correct tooltip content should be displayed
            cy.get(manageMyAdsModel.adFeatureExpiresTooltip.content.bumpup)
                .should('be.visible')
                .should(
                    'have.text',
                    'You will be able to bump up your ad 1h after posting. This will move your ad to the top of search results as if it was just posted.'
                );
        });

        it('BumpUp - has just been bumped up', () => {
            singleAdWithFeaturesData.model.adverts['1001641601'].bumpTimes = '1';
            mockAndVisit(singleAdWithFeaturesData);

            // When: user click on the Promote button
            cy.get(manageMyAdsModel.promoteButton).eq(0).click();
            cy.get(manageMyAdsModel.buyFeaturesBanner.container).should('not.exist');

            // Then: the correct label should be displayed
            cy.get(manageMyAdsModel.adFeatureCardContainer)
                .eq(3)
                .should('contain.text', 'Becomes available 1h after bump up');

            // When: user click on the tooltip trigger
            cy.get(manageMyAdsModel.adFeatureExpiresTooltip.trigger.bumpup)
                .should('be.visible')
                .click();

            // Then: the correct tooltip content should be displayed
            cy.get(manageMyAdsModel.adFeatureExpiresTooltip.content.bumpup)
                .should('be.visible')
                .should(
                    'have.text',
                    'Your ad was Bumped up recently. You will be able to bump it up again 1h after last bump up.'
                );
        });
    });
});
