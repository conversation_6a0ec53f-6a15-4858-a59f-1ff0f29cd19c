import { srpModel } from '../../model/srp';
import { generateUniqueId } from '../../utils';

const searchResultsData = require('../../fixtures/srp/favourite-an-ad-dishwasher.json');

const mockFavouriteAdRes = { errorNameValuePairs: [], success: true };

const actions = ['save', 'unsave'];

describe('favourite-an-ad.spec', () => {
    actions.forEach((action) => {
        it(`${action} an ad when user is logged in`, () => {
            cy.viewport('macbook-15');
            const advertId = generateUniqueId();

            searchResultsData.model.core.savedAds['1001446686'] = action === 'unsave';

            cy.addMockEndpoint({
                matchOn: {
                    url: `/search?q=dishwasher_${advertId}&search_category=all`,
                    method: 'GET',
                },
                respondWith: {
                    json: searchResultsData,
                    status: 200,
                },
            });

            // When: User visit a SRP
            cy.visit({
                url: `/search?search_category=all&q=dishwasher_${advertId}`,
            });

            cy.intercept(
                'POST',
                `/savedads/**/${action === 'save' ? 'add' : 'remove'}?token=*`,
                mockFavouriteAdRes
            ).as(action);

            // Then: Each listing should have a favourite button displayed
            cy.get(srpModel.favouriteBtn).should('have.length', 5);

            // When: User click on the favourite button of the first listing
            cy.get(srpModel.favouriteBtn).eq(0).click();

            // Then: The corresponding request should be fired with correct URL and method
            cy.wait(`@${action}`)
                .its('request')
                .then((request) => {
                    cy.wrap(request.method).should('equal', 'POST');
                    cy.wrap(request.url)
                        .should('contain', '/savedads')
                        .and('contain', `/${action === 'save' ? 'add' : 'remove'}`);
                });

            // And: The favourite button status should be updated
            cy.get(srpModel.favouriteBtn)
                .eq(0)
                .should(
                    'have.attr',
                    'data-testid',
                    action === 'save' ? 'full-heart' : 'empty-heart'
                );
        });
    });
});
