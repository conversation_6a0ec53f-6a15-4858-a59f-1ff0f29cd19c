 
import { profileModel } from '../../model/profile';
import { generateUniqueId } from '../../utils';

describe('profile.spec', () => {
    it('should display profile page and click through tabs', () => {
        const profileData = require('../../fixtures/profile/profile.json');
        const adDetailsResults = require('../../fixtures/vip/vip-for-sale.json');
        const advertId = generateUniqueId();

        cy.addMockEndpoint({
            matchOn: {
                url: `/p/christmas-decorations/this-is-a-test-ad-for-sale/${advertId}`,
                method: 'GET',
            },
            respondWith: {
                json: adDetailsResults,
                status: 200,
            },
        });

        cy.addMockEndpoint({
            matchOn: {
                url: `/profile/accounts/${adDetailsResults.model.vipData.accountPublicId}`,
                method: 'GET',
            },
            respondWith: {
                json: profileData,
                status: 200,
            },
        })

        // Given: User visit For Sale VIP
        cy.visit({
            url: `/p/christmas-decorations/this-is-a-test-ad-for-sale/${advertId}`,
        });

        // Then I click on View Profile link
        cy.get(profileModel.viewProfile).click();

        // click on review tab
        cy.get(profileModel.reviewTab).click();
        //click back on listings tab
        cy.get(profileModel.listingTab).click();
    });
});
