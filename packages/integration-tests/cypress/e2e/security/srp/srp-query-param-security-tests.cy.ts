import { generateUniqueId } from '../../../utils';

const searchResults = require('../../../fixtures/srp/search-results-with-nearby-advert.json');

describe('srp-query-param-security-tests.spec', () => {
    it('Query param based attacks should not be possible on the search results page', () => {
        const advertId = generateUniqueId();

        cy.addMockEndpoint({
            matchOn: {
                url: `/search?%3Fdate=%20Payload%3A%20%3B%27%221%3C%21--%3E%3C%2Ftitle%2F%3C%2Ftextarea%2F%3C%2Fscript%2F%3E%3Cdetails%2Fopen%2Fontoggle%3D_%3Dalert%2C_%28%27test_fail%27%29%3E&distance=10&q=ads-close-by-${advertId}&search_location=Richmond%2C%20London`,
                method: 'GET',
            },
            respondWith: {
                json: searchResults,
                status: 200,
            },
        });

        cy.visit(`/`);

        const stub = cy.stub();
        cy.on('window:alert', stub);

        // Ignore any errors - we're only interested in whether it executes the malicious Javascript.
        Cypress.on('uncaught:exception', () => {
            return false;
        });

        cy.visit(
            `/search?q=ads-close-by-${advertId}&search_location=Richmond,%20London&distance=10&?date=%20Payload:%20;%27%221%3C!--%3E%3C/title/%3C/textarea/%3C/script/%3E%3Cdetails/open/ontoggle=_=alert,_(%27test_fail%27)%3E`
        );

        cy.then(() => {
            /* eslint-disable-next-line jest/valid-expect */
            expect(stub).not.to.have.been.called;
        });
    });
});
