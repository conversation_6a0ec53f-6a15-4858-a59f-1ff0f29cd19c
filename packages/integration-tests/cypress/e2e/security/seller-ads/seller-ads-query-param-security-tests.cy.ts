import { generateUniqueId } from '../../../utils';

const sellerAdsResults = require('../../../fixtures/seller-ads.json');

describe('seller-ads-query-param-security-tests.spec', () => {
    it('Query param based attacks should not be possible on the seller ads page', () => {
        const advertId = generateUniqueId();

        cy.addMockEndpoint({
            matchOn: {
                url: `/sellerads/${advertId}?date=%20Payload%3A%20%3B%27%221%3C%21--%3E%3C%2Ftitle%2F%3C%2Ftextarea%2F%3C%2Fscript%2F%3E%3Cdetails%2Fopen%2Fontoggle%3D_%3Dalert%2C_%28%27test_fail%27%29%3E&page=1`,
                method: 'GET',
            },
            respondWith: {
                json: sellerAdsResults,
                status: 200,
            },
        });

        cy.visit(`/`);

        const stub = cy.stub();
        cy.on('window:alert', stub);

        // Ignore any errors - we're only interested in whether it executes the malicious Javascript.
        Cypress.on('uncaught:exception', () => {
            return false;
        });

        cy.visit(
            `/sellerads/${advertId}?page=1&date=%20Payload:%20;%27%221%3C!--%3E%3C/title/%3C/textarea/%3C/script/%3E%3Cdetails/open/ontoggle=_=alert,_(%27test_fail%27)%3E`
        );

        cy.then(() => {
            /* eslint-disable-next-line jest/valid-expect */
            expect(stub).not.to.have.been.called;
        });
    });
});
