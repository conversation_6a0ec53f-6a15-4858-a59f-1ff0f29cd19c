import { commonModel } from '../../model/common';

const home = require('../../fixtures/home/<USER>');

describe('app-skeleton-element-visibility.spec', () => {
    before(() => {
        cy.addMockEndpoint({
            matchOn: {
                url: '/',
                method: 'GET',
            },
            respondWith: {
                json: home,
                status: 200,
            },
        });
    });

    it('when viewing on app-view, some elements should not be displayed', () => {
        cy.visit('/', {
            headers: {
                'User-Agent': 'iOS/GumtreeApp/h64x83o7jbz3dvefdpaqetov'
            }
        });

        cy.get(commonModel.navbar.container).should('not.exist');
        cy.get(commonModel.browseLinks.container).should('not.exist');
        cy.get(commonModel.footer.container).should('not.exist');
    });

    it('when viewing on web-view, all elements should be displayed', () => {
        cy.visit('/', {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            }
        });

        cy.get(commonModel.navbar.container).should('be.visible');
        cy.get(commonModel.browseLinks.container).should('be.visible');
        cy.get(commonModel.footer.container).should('be.visible');
    });
});
