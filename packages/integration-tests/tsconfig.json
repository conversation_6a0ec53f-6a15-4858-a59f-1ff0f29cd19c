{"compilerOptions": {"module": "commonjs", "esModuleInterop": true, "experimentalDecorators": true, "useUnknownInCatchVariables": false, "inlineSources": true, "lib": ["es2017", "dom", "dom.iterable", "ES2022.A<PERSON>y"], "moduleResolution": "node", "noFallthroughCasesInSwitch": true, "noImplicitAny": false, "noImplicitReturns": true, "noUnusedLocals": false, "noUnusedParameters": true, "outDir": "dist", "rootDir": ".", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "es2017", "jsx": "react", "allowJs": true, "resolveJsonModule": true, "noErrorTruncation": true, "types": ["cypress", "cypress-file-upload", "@4tw/cypress-drag-drop"]}, "include": ["**/*.js", "**/*.ts", "../../node_modules/cypress"]}