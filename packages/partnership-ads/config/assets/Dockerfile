FROM europe-west4-docker.pkg.dev/gum-host-7491/docker-artifact-repo/frontend-base:node-20.18.0-latest AS base
# Create app directory
WORKDIR /srv

COPY . .

RUN yarn install --ignore-optional

# ---- Copy Files/Build ----
FROM base AS build
WORKDIR /srv
# Copy the code for this service
# Source only changes should be rebuilt faster
COPY . .

# Run the build
RUN yarn workspace @gumtree/partnership-ads build:assets
# Run the tests
RUN yarn workspace @gumtree/partnership-ads lint:script
RUN yarn test partnership-ads

# --- Release with Alpine ----
FROM europe-west4-docker.pkg.dev/gum-host-7491/docker-artifact-repo/dock.es.ecg.tools/hub.docker.com/nginx:1.14.0-alpine

COPY --from=build /srv/packages/partnership-ads/dist/assets /usr/share/nginx/html
COPY --from=build /srv/packages/partnership-ads/config/assets/nginx.conf /etc/nginx/nginx.conf
