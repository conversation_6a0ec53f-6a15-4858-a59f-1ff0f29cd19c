events {}

http {
    large_client_header_buffers 4 16k;
    
    include /etc/nginx/mime.types;

    access_log  /dev/stdout;
    error_log  /dev/stderr;

    gzip on;

    server {
        server_name localhost;

        location / {
            root /usr/share/nginx/html;
            rewrite .*/partnership-ads/(.*)$ /$1;
        }

        location ~ \.(css|js)$ {
            root /usr/share/nginx/html;
            rewrite .*/partnership-ads/(.*)$ /$1;
            add_header Cache-Control "public,max-age=86400";
        }

        location ~ \.(jpg|jpeg|gif|png|svg|xml|ico)$ {
            root /usr/share/nginx/html;
            rewrite .*/partnership-ads/(.*)$ /$1;
            add_header Cache-Control "public,max-age=31536000";
        }

        location ~ \.(ttf|woff|woff2)$ {
            add_header Access-Control-Allow-Origin *;
            root /usr/share/nginx/html;
            rewrite .*/partnership-ads/(.*)$ /$1;
            add_header Cache-Control "public,max-age=31536000";
        }
    }
}
