import React from 'react';
import { PageType } from '@gumtree/shared/src/types/client-data';
import { Platform } from '@gumtree/shared/src/types/platform';

import BingAdSlot from '@gumtree/third-party-ads/src/framework/bing/server-side/components/bing-ad-slot';

import { renderToString } from 'react-dom/server';
import { AdModel } from '@gumtree/third-party-ads/src/framework/bing/common/transform-data-into-ad-models';

export default (
    platform: Platform,
    showCarousel: boolean,
    slotsToRender: AdModel[],
    pageType: PageType,
    clientIp: string,
    assetUrl: string
): string => {
    return renderToString(
        <html lang="en-GB">
            <head>
                <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
                <meta httpEquiv="X-UA-Compatible" content="IE=edge,chrome=1" />
                <meta
                    name="viewport"
                    content="width=device-width, initial-scale=1.0, minimum-scale=1.0"
                />
                <meta name="format-detection" content="telephone=no" />
                <meta name="google" content="nositelinkssearchbox" />
                <title>Bing</title>
                <base href={assetUrl} />
            </head>
            <body style={{ height: 'fit-content', margin: 0 }}>
                <BingAdSlot
                    slotsToRender={slotsToRender}
                    showCarousel={showCarousel}
                    platform={platform}
                    pageType={pageType}
                    clientIp={clientIp}
                />
            </body>
        </html>
    );
};
