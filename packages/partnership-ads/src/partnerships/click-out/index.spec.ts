import { isAnyvanEnabled } from '@gumtree/shared/src/partnerships/categories-config/anyvan';
import getResponse from './index';
import { ParsedQueryParameters } from './query-params-parser';
import { getClickoutUrl } from '../shared/anyvan';
import getAssetPath from '../util/get-asset-path';

jest.mock('../shared/anyvan', () => ({
    getClickoutUrl: jest.fn(),
}));

jest.mock('../util/get-asset-path', () => jest.fn());

jest.mock('@gumtree/shared/src/partnerships/categories-config/anyvan', () => ({
    isAnyvanEnabled: jest.fn(),
}));

describe('getResponse', () => {
    const assetUrl = 'https://example.com/';
    const parsedQueryParameters: ParsedQueryParameters = {
        pageType: 'VIP',
        l1Category: 'for-sale',
        l2Category: 'mobile-phones',
        l3Category: 'apple',
        listingLocation: 'London',
        listingTitle: 'iPhone 12',
        listingImage: 'image1.jpg',
    };

    beforeEach(() => {
        (getClickoutUrl as jest.Mock).mockReturnValue('https://anyvan.com/clickout');
        (getAssetPath as jest.Mock).mockReturnValue('logo-path');
        (isAnyvanEnabled as jest.Mock).mockReturnValue(true);
    });

    it('returns expected response when Anyvan is enabled', () => {
        const expectedResponse = {
            id: 'anyvan',
            icon: `${assetUrl}logo-path`,
            ctaText: 'Deliver this with AnyVan',
            clickOutUrl: 'https://anyvan.com/clickout',
        };

        expect(getResponse(parsedQueryParameters, assetUrl)).toEqual(expectedResponse);

        expect(isAnyvanEnabled).toHaveBeenCalledWith(
            parsedQueryParameters.pageType,
            parsedQueryParameters.l1Category,
            parsedQueryParameters.l2Category,
            parsedQueryParameters.l3Category
        );

        expect(getClickoutUrl).toHaveBeenCalledWith('app', parsedQueryParameters.l1Category, {
            pickup: parsedQueryParameters.listingLocation,
            title: parsedQueryParameters.listingTitle,
            images: [parsedQueryParameters.listingImage],
        });
    });

    it('returns undefined when Anyvan is not enabled', () => {
        (isAnyvanEnabled as jest.Mock).mockReturnValue(false);

        expect(getResponse(parsedQueryParameters, assetUrl)).toBeUndefined();

        expect(isAnyvanEnabled).toHaveBeenCalledWith(
            parsedQueryParameters.pageType,
            parsedQueryParameters.l1Category,
            parsedQueryParameters.l2Category,
            parsedQueryParameters.l3Category
        );

        expect(getClickoutUrl).not.toHaveBeenCalled();
    });

    it('handles missing optional parameters', () => {
        const minimalParams: ParsedQueryParameters = {
            pageType: 'VIP',
            l1Category: 'for-sale',
        };

        const expectedResponse = {
            id: 'anyvan',
            icon: `${assetUrl}logo-path`,
            ctaText: 'Deliver this with AnyVan',
            clickOutUrl: 'https://anyvan.com/clickout',
        };

        expect(getResponse(minimalParams, assetUrl)).toEqual(expectedResponse);

        expect(getClickoutUrl).toHaveBeenCalledWith('app', minimalParams.l1Category, {
            images: [],
        });
    });
});
