import styled from '@emotion/styled';
import { colors, fontSizes, fontWeights, gutterSizes } from '@gumtree/ui-library/src/base/theme';

export const Container = styled.div`
    display: flex;
    align-items: center;
    height: 30px;
`;

export const Logo = styled.img<{ isMobile: boolean }>`
    ${(props) =>
        props.isMobile
            ? 'height: 20px;'
            : `
                margin-top: -3px;
                height: 14px;
              `}
`;

export const CompactLink = styled.a<{ isMobile: boolean }>`
    color: ${colors.blue};
    text-decoration: none;
    padding-left: ${gutterSizes.small};
    font-size: ${fontSizes.base};
    font-weight: ${fontWeights.lightBold};

    ${(props) =>
        !props.isMobile &&
        `
            &:hover {
                text-decoration: underline;
            }
            `};
`;
