import React from 'react';

import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import type { SellerType } from '@gumtree/shared/src/types/advertising/partnership';
import Template from './template';
import { formatPrice } from './transform';
import type { Params } from './types';
import type { DeviceType, GaPageType } from '../shared/types';
import trackPartnershipClick from '../../tracking/track-partnership-click';

jest.mock('./../../tracking/track-partnership-click');

type Props = React.ComponentProps<typeof Template>;
const renderTemplate = (extraProps = {} as Props) => render(<Template {...extraProps} />);

describe('CarMoney VIP Component', () => {
    const baseParams = {
        canonicalUrl: 'url',
        baseUrl: 'baseUrl',
        encryptedVrnCarMoney: 'vrn',
        imageUrl: 'imageUrl',
        deviceType: 'desktop' as DeviceType,
        gaPageType: 'VIP' as GaPageType,
        priceInPence: String(100 * 3000),
        sellerType: 'Trade' as SellerType,
    };

    it('has its subcomponents', () => {
        const SUT = renderTemplate({ ...baseParams });

        expect(screen.getByText('Borrow amount')).toBeInTheDocument();
        expect(screen.getByText('Monthly cost')).toBeInTheDocument();
        expect(screen.getByText('Best available rate')).toBeInTheDocument();
        expect(screen.getByText('Total cost of credit')).toBeInTheDocument();
        expect(screen.getByText('Total payable (including deposit)')).toBeInTheDocument();

        expect(screen.getByTestId('car-price')).toHaveTextContent(
            `£${formatPrice(Number(baseParams.priceInPence) / 100)}`
        );
        expect(SUT.asFragment()).toMatchSnapshot();
    });

    it('calculates smallAmount correctly', () => {
        const params: Params = {
            ...baseParams,
            priceInPence: String(100 * 123.0),
        };
        renderTemplate({ ...params });

        expect(screen.getByTestId('car-price')).toHaveTextContent('£123.00');
        expect(screen.getByTestId('Monthly cost')).toHaveTextContent('£2.75');
        expect(screen.getByTestId('Best available rate')).toHaveTextContent('12.90%');
        expect(screen.getByTestId('Total cost of credit')).toHaveTextContent('£42.00');
        expect(screen.getByTestId('Total payable (including deposit)')).toHaveTextContent(
            '£165.00'
        );
    });

    it('calculates largeAmount correctly', () => {
        const params: Params = {
            ...baseParams,
            priceInPence: String(100 * 123450.0),
        };
        renderTemplate({ ...params });

        expect(screen.getByTestId('car-price')).toHaveTextContent('£123,450.00');
        expect(screen.getByTestId('Monthly cost')).toHaveTextContent('£2,536.12');
        expect(screen.getByTestId('Best available rate')).toHaveTextContent('8.90%');
        expect(screen.getByTestId('Total cost of credit')).toHaveTextContent('£28,717.20');
        expect(screen.getByTestId('Total payable (including deposit)')).toHaveTextContent(
            '£152,167.20'
        );
    });

    it('calculates correctly', () => {
        const params: Params = {
            ...baseParams,
            priceInPence: String(100 * 24495.0),
        };
        renderTemplate({ ...params });

        expect(screen.getByTestId('car-price')).toHaveTextContent('£24,495.00');
        expect(screen.getByTestId('Monthly cost')).toHaveTextContent('£503.22');
        expect(screen.getByTestId('Best available rate')).toHaveTextContent('8.90%');
        expect(screen.getByTestId('Total cost of credit')).toHaveTextContent('£5,698.20');
        expect(screen.getByTestId('Total payable (including deposit)')).toHaveTextContent(
            '£30,193.20'
        );
    });

    it('has GA tracking', () => {
        const params = {
            ...baseParams,
            l1Category: 'cars-vans-motorbikes',
            l2Category: 'cars',
        };
        renderTemplate({ ...params });
        const button = screen.getByTestId('apply-now-btn');

        userEvent.click(button);

        expect(trackPartnershipClick).toHaveBeenCalledWith({
            partner: 'CarMoney',
            slot: 'Default',
            l1: 'cars-vans-motorbikes',
            l2: 'cars',
        });
    });
});
