import styled from '@emotion/styled';
import {
    boxSizes,
    colors,
    fontSizes,
    fontWeights,
    gutterSizes,
    lineHeights,
} from '@gumtree/ui-library/src/base/theme';
import { getAppViewTheme } from '@gumtree/ui-library/src/base/themes/appview-theme';
import { modernTheme } from '@gumtree/ui-library/src/base/themes/modern-theme';

export const Container = styled.div`
    display: flex;
    flex-direction: column;

    @media (prefers-color-scheme: dark) {
        background-color: ${colors.slateBlue};
        color: ${colors.white};
    }
`;

export const Title = styled.h2`
    font-size: ${fontSizes.header};
    font-weight: ${fontWeights.bold};
    margin: 0 0 ${gutterSizes.base};
    line-height: ${lineHeights.medium};

    @media (prefers-color-scheme: dark) {
        color: ${colors.white};
    }
`;

export const FormContainer = styled.form`
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: ${gutterSizes.medium};
`;

export const LogoContainer = styled.div`
    height: ${lineHeights.medium};
    grid-column: span 5;
    align-self: center;

    img {
        height: 100%;
        object-fit: contain;
    }
`;

export const NormalLogo = styled.img`
    @media (prefers-color-scheme: dark) {
        display: none;
    }
`;

export const DarkModeLogo = styled.img`
    display: none;
    @media (prefers-color-scheme: dark) {
        display: inline;
    }
`;

export const CtaContainer = styled.div`
    grid-column: span 7;
    align-self: end;
`;

export const StyledButton = styled.button`
    display: block;
    cursor: pointer;
    padding: ${gutterSizes.medium} ${gutterSizes.xlarge};
    line-height: ${lineHeights.medium};
    background-color: ${modernTheme.palette.primary.main};
    color: ${getAppViewTheme().palette.primary.mainContrastText};
    overflow: hidden;
    font-size: ${fontSizes.base};
    font-weight: ${fontWeights.bold};
    border-radius: ${boxSizes.borderRadius4};
    text-align: center;
    border: none;
    width: 100%;
`;

export const BulletList = styled.ul`
    list-style: disc;
    padding-left: ${gutterSizes.xlarge};
    padding-bottom: ${gutterSizes.large};
    font-size: ${fontSizes.base};
    line-height: ${lineHeights.medium};
`;
