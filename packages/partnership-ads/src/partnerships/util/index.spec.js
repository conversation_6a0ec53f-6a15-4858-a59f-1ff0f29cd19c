import { getUrlParamsAsString } from './index';

describe('urlParamsAsString()', () => {
    it('comparethemarket/cars/web/vip returns url params as a string', () => {
        const urlParams = {
            vrn: 'AAAABSkfLdH5splJ',
            AFFCLIE: 'EG96',
            value: '500',
            prdcls: 'PC',
            rqstyp: 'newmotorquote',
            imageurl: 'https://i.ebayimg.com/00/s/MTAyNFg3Njg=/z/TIMAAOSwE9Bb8Bx~/$_1.JPG',
        };
        expect(getUrlParamsAsString(urlParams)).toMatchSnapshot();
    });
    it('comparethemarket/motorbikes/web/vip returns url params as a string', () => {
        const urlParams = {
            prdcls: 'BI',
            rqstyp: 'newbikequote',
            AFFCLIE: 'EK60',
            vrn: undefined,
            value: '1725',
            imageurl: 'https://i.ebayimg.com/00/s/MTAyNFg1NzY=/z/LfQAAOSw1hJb7wsw/$_1.JPG',
        };
        expect(getUrlParamsAsString(urlParams)).toMatchSnapshot();
    });
    it('comparethemarket/pets/web/vip returns url params as a string', () => {
        const urlParams = { type: 'DOG', AFFCLIE: 'EG98', age: '27/09/2018', price: '380' };
        expect(getUrlParamsAsString(urlParams)).toMatchSnapshot();
    });
    it('comparethemarket/vans/web/vip returns url params as a string', () => {
        const urlParams = {
            vrn: 'AAAABxj3QbG3+UvS',
            AFFCLIE: 'EG96',
            value: '5250',
            prdcls: 'PC',
            rqstyp: 'newmotorquote',
            imageurl: 'https://i.ebayimg.com/00/s/NzY4WDEwMjQ=/z/gvAAAOSwoVZb9Gi~/$_1.JPG',
        };
        expect(getUrlParamsAsString(urlParams)).toMatchSnapshot();
    });
    it('zuto/web/vip returns url params as a string', () => {
        const urlParams = {
            utm_source: 'gumtree',
            utm_medium: 'affiliate',
            utm_campaign: 'ftcdesktop',
            imageurl: 'https://i.ebayimg.com/00/s/MTAyNFg1NzY=/z/LfQAAOSw1hJb7wsw/$_1.JPG',
            value: '1725',
        };
        expect(getUrlParamsAsString(urlParams)).toMatchSnapshot();
    });
});
