import { L1Category } from '@gumtree/shared/src/types/client-data';

export type Payload = {
    pickup?: string;
    title?: string;
    images: string[];
};

export const urlDefault = 'https://www.anyvan.com/partner-landing/gumtree';
export const urlApp = 'https://www.anyvan.com/partner-landing/gumtree-app';
export const urlRemovals = 'https://www.anyvan.com/partners/gumtree-removals';

const btoa = (str: string): string => {
    if (typeof window === 'undefined') {
        return Buffer.from(str, 'utf8').toString('base64');
    } else {
        return window.btoa(str);
    }
};

export const encodePayload = (payload: Payload): string =>
    encodeURIComponent(
        btoa(
            JSON.stringify({
                ...(payload.title ? { title: encodeURIComponent(payload.title) } : {}),
                ...(payload.pickup ? { pickup: encodeURIComponent(payload.pickup) } : {}),
                images: payload.images,
            })
        )
    );

export const getClickoutUrl = (
    platform: 'web' | 'app',
    l1Category: L1Category | undefined,
    payload: Payload
) =>
    l1Category === 'flats-houses'
        ? urlRemovals
        : `${platform === 'app' ? urlApp : urlDefault}?data=${encodePayload(payload)}`;
