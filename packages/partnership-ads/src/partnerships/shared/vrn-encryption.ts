import { decrypt } from '@gumtree/shared/src/util/encryption';
import { carMoneyEncryptionIv } from '@gumtree/shared/src/partnerships/parse-model';
import { encryptVrn } from '@gumtree/shared/src/partnerships/vrn-encryption';

const ENCRYPTION_KEY_ZUTO = process.env.ENCRYPTION_KEY_ZUTO;
const ENCRYPTION_KEY_CAR_MONEY = process.env.ENCRYPTION_KEY_CAR_MONEY;
const ENCRYPTION_IV_CAR_MONEY = carMoneyEncryptionIv;

export const decryptZutoVrn = (
    encryptedVrnZuto: string | undefined,
    zutoDecryptionKey = ENCRYPTION_KEY_ZUTO
): string | undefined => {
    if (!encryptedVrnZuto || !zutoDecryptionKey) {
        return undefined;
    }

    const encryptedValueAsByteArray = Buffer.from(encryptedVrnZuto, 'base64');
    const ivAsByteArray = encryptedValueAsByteArray.slice(0, 16);
    const encryptedMessageAsByteArray = encryptedValueAsByteArray.slice(
        16,
        encryptedValueAsByteArray.length
    );
    const encryptedMessageAsString = encryptedMessageAsByteArray.toString('base64');

    return decrypt(
        encryptedMessageAsString,
        'aes-128-cbc',
        Buffer.from(zutoDecryptionKey, 'base64'),
        ivAsByteArray
    );
};

export const encryptVrnForCarMoney = (
    vrnInPlainText: string | undefined,
    carmoneyEncryptionKey = ENCRYPTION_KEY_CAR_MONEY,
    carmoneyEncryptionIv = ENCRYPTION_IV_CAR_MONEY
): string | undefined => {
    return encryptVrn(vrnInPlainText, carmoneyEncryptionKey, carmoneyEncryptionIv);
};

export default (
    encryptedVrnZuto: string | undefined,
    zutoDecryptionKey = ENCRYPTION_KEY_ZUTO,
    carmoneyEncryptionKey = ENCRYPTION_KEY_CAR_MONEY,
    carmoneyEncryptionIv = ENCRYPTION_IV_CAR_MONEY
): string | undefined => {
    return encryptVrnForCarMoney(
        decryptZutoVrn(encryptedVrnZuto, zutoDecryptionKey),
        carmoneyEncryptionKey,
        carmoneyEncryptionIv
    );
};
