import bingEndpoint from '../bing';
import logger from '../../logger';

export default async (req, res) => {
    try {
        req.sanitizeXSS('slotId');

        if (req.query.platform === 'web') {
            logger.info(`REQ_ATTEMPTING_AMAZON: ${req.headers['user-agent']}`);
            res.status(404).send(
                '<html lang="en-GB"><head></head><body style="margin: 0"></body></html>'
            );
            return;
        } else {
            return bingEndpoint(req, res);
        }
    } catch (ex) {
        throw new Error(ex);
    }
};
