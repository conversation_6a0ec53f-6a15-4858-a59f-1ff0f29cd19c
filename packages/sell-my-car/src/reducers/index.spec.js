import commonReducers from '@gumtree/shell/src/reducers/common';
import page from '@gumtree/shell/src/reducers/page';
import buyerReducers from '@gumtree/shell/src/reducers/buyer';
import reducers from './index';

import content from './seo-content';

describe('Sell My Car reducer index', () => {
    it('exports the App reducers', () => {
        const sellMyCarReducers = {
            ...commonReducers,
            ...buyerReducers,
            page,
            content,
        };
        expect(reducers).toEqual(expect.objectContaining(sellMyCarReducers));
    });
});
