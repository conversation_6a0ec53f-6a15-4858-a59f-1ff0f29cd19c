// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SEO Landing Header component renders correctly 1`] = `
<DocumentFragment>
  <div
    class="landing-header"
  >
    <section
      class="hero"
    >
      <h1
        class="title"
      >
        header title
      </h1>
      <p
        class="subtitle"
      >
        header subtitle
      </p>
    </section>
    <div
      class="boxout"
    >
      <form
        action="/vehicle-verification/verification"
        class="vrm css-nqjir9-vrm-lookup"
        method="post"
      >
        <div
          class=""
        />
        <div
          class="vrm-container grid grid--container"
          style="display: flex; justify-content: center;"
        >
          <div
            class="vrm-title grid"
          >
            <h4
              class="title"
            >
              <span
                aria-hidden="true"
                class="icon icon--car css-0 eom5h670"
              />
              Sell your car online for FREE
            </h4>
            <p
              class="subtitle"
            >
              Selling a car is free for private sellers
            </p>
          </div>
          <div
            class="vrm-cols grid"
          >
            <div
              class="vrm-col grid"
            >
              <div
                class="form-element form-element--input"
              >
                <input
                  autocomplete="off"
                  class="input input-text"
                  data-testid="input-vrm"
                  id="vrm"
                  maxlength="8"
                  minlength="2"
                  name="attributes[vrn]"
                  placeholder="ENTER REG"
                  required=""
                  type="text"
                />
              </div>
            </div>
            <div
              class="vrm-col grid"
            >
              <button
                class="button button--primary css-17bzfp3-button-button"
                rel="noopener noreferrer"
                type="submit"
              >
                <span
                  aria-hidden="true"
                  class="icon icon--pin css-0 eom5h670"
                />
                Sell my car
              </button>
            </div>
          </div>
        </div>
        <input
          name="categoryId"
          type="hidden"
          value="9311"
        />
      </form>
    </div>
    <div />
  </div>
</DocumentFragment>
`;
