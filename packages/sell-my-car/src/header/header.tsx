import React from 'react';
import { string, oneOfType, array, node, object } from 'prop-types';
import Hero from './hero';
import VrmLookup from '../vrm-lookup/vrm-lookup';
import MotorsPartnershipBanner from './motors-partnership-banner';

import './header.scss';

const Header = ({ legal, title, subtitle, children, sellerUrl, buyerUrl, bffUrl }) => (
    <div className="landing-header">
        <MotorsPartnershipBanner />
        <Hero title={title} subtitle={subtitle} hasLegalText={legal && !!legal.subtitle} />
        <div className="boxout">
            <VrmLookup
                bffUrl={bffUrl}
                buyerUrl={buyerUrl}
                sellerUrl={sellerUrl}
                title="Sell your car online for FREE"
                subtitle="Selling a car is free for private sellers"
                buttonIcon="pin"
                eventAction="PostAdBegin"
                eventCategory="LandingPageSellMyCar"
                type="lookup"
            />
        </div>
        {children}
    </div>
);

Header.propTypes = {
    bffUrl: string,
    buyerUrl: string.isRequired,
    children: oneOfType([array, node]).isRequired,
    legal: object.isRequired,
    sellerUrl: string.isRequired,
    subtitle: string.isRequired,
    title: string.isRequired,
};

Header.defaultProps = {
    bffUrl: '',
};

export default Header;
