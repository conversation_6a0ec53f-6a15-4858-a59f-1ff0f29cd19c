import { trackAndNavigate } from '@gumtree/shell/src/common/common-actions';
import { mapStateToProps, mapDispatchToProps } from './profile-listings-container';

jest.mock('../redux/recruiter-profile-actions', () => ({
    toggleFavouriteListing: jest.fn(),
}));

jest.mock('@gumtree/shell/src/common/common-actions', () => ({
    trackAndNavigate: jest.fn(),
}));

describe('Profile listings container...', () => {
    describe('mapStateToProps...', () => {
        const render = mapStateToProps({
            recruiterProfile: {
                adverts: [],
                profileName: '',
            },
        });
        it('should contain adverts', () => {
            const SUT = render({ recruiterProfile: { adverts: [1, 2, 3] } });

            expect(SUT.adverts).toEqual([1, 2, 3]);
        });

        it('should contain profile name', () => {
            const SUT = render({ recruiterProfile: { profileName: 'abc' } });

            expect(SUT.profileName).toEqual('abc');
        });
    });

    describe('mapDispatchToProps', () => {
        describe('onClickListing...', () => {
            it('should call trackAndNavigate correctly.', () => {
                const dispatch = jest.fn();
                mapDispatchToProps(dispatch).onClickListing('abc');

                expect(trackAndNavigate).toHaveBeenCalledWith(
                    'ViewJoBListing',
                    'abc',
                    'ResultsRecruiter'
                );
            });
        });

        describe('onToggleFavouriteListing...', () => {
            it('should call toggleFavouriteListing correctly.', () => {
                const dispatch = jest.fn();
                mapDispatchToProps(dispatch).onToggleFavouriteListing('abc');

                expect(trackAndNavigate).toHaveBeenCalledWith('abc');
            });
        });
    });
});
