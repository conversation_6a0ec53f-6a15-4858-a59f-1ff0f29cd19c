import { updateCanonicalUrl } from '.';

describe('updateCanonicalUrl', () => {
    it('should update canonicalUrl if currentUrl exists', () => {
        const data = {
            model: {
                core: {
                    currentUrl: 'https://www.staging.gumtree.io/bff/profile/recruiter/4000072324',
                },
            },
        };

        const expectedOutput = {
            model: {
                core: {
                    currentUrl: 'https://www.staging.gumtree.io/bff/profile/recruiter/4000072324',
                    canonicalUrl: 'https://www.staging.gumtree.io/jobs/cmp/4000072324',
                },
            },
        };

        updateCanonicalUrl(data);

        expect(data).toEqual(expectedOutput);
    });

    it('should not update canonicalUrl if currentUrl does not exist', () => {
        const data = {
            model: {
                core: {},
            },
        };

        const expectedOutput = {
            model: {
                core: {},
            },
        };

        updateCanonicalUrl(data);

        expect(data).toEqual(expectedOutput);
    });

    it('should not update canonicalUrl if data is undefined', () => {
        const data = undefined;

        const expectedOutput = undefined;

        updateCanonicalUrl(data);

        expect(data).toEqual(expectedOutput);
    });
});
