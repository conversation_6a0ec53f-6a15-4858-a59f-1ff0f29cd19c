import {
    updateListingFavourites,
    getSavedListingIds,
    addSavedListingId,
    removeSavedListingId,
    updateSavedListingIds,
} from './recruiter-profile-selectors';

describe('Recruiter profile selectors...', () => {
    describe('updateListingFavourites...', () => {
        it(`should use savedAds collection found in the model,
        to set each listings isFavourite state.`, () => {
            const listings = [
                { id: 1, isFavourite: false },
                { id: 2, isFavourite: false },
                { id: 3, isFavourite: false },
                { id: 4, isFavourite: false },
                { id: 5, isFavourite: false },
            ];

            const savedAds = [1, 3, 5];

            const SUT = updateListingFavourites({ listings, savedAds });

            expect(SUT).toEqual([
                { id: 1, isFavourite: true },
                { id: 2, isFavourite: false },
                { id: 3, isFavourite: true },
                { id: 4, isFavourite: false },
                { id: 5, isFavourite: true },
            ]);
        });
    });

    describe('getSavedListingIds...', () => {
        it('should return the ID\'s of all favourited listings.', () => {
            const listings = [
                { id: 1, isFavourite: true },
                { id: 2, isFavourite: false },
                { id: 3, isFavourite: true },
                { id: 4, isFavourite: false },
                { id: 5, isFavourite: true },
            ];

            const SUT = getSavedListingIds({ listings });

            expect(SUT).toEqual([1, 3, 5]);
        });
    });

    describe('addSavedListingId...', () => {
        it('should append a listing ID to a list of favourited listing ID\'s', () => {
            const listingIds = [1, 2, 3];
            const listingId = 4;

            const SUT = addSavedListingId({ listingIds, listingId });

            expect(SUT).toEqual([1, 2, 3, 4]);
        });
    });

    describe('removeSavedListingId...', () => {
        it('should should remove a listing ID from a list of favourited listing ID\'s', () => {
            const listingIds = [1, 2, 3, 4];
            const listingId = 4;

            const SUT = removeSavedListingId({ listingIds, listingId });

            expect(SUT).toEqual([1, 2, 3]);
        });
    });

    describe('UpdatesavedListingIds...', () => {
        it('should favourite a listing.', () => {
            const listings = [
                { id: 1, isFavourite: false },
                { id: 2, isFavourite: false },
            ];
            const listingId = 2;
            const action = 'add';

            const SUT = updateSavedListingIds({ listings, listingId, action });

            expect(SUT).toEqual([2]);
        });

        it('should unfavourite a listing.', () => {
            const listings = [
                { id: 1, isFavourite: true },
                { id: 2, isFavourite: true },
            ];
            const listingId = 2;
            const action = 'remove';

            const SUT = updateSavedListingIds({ listings, listingId, action });

            expect(SUT).toEqual([1]);
        });

        it('should return passed listing ID\'s by default.', () => {
            const listings = [
                { id: 1, isFavourite: true },
                { id: 2, isFavourite: true },
            ];
            const listingId = 2;
            const action = null;

            const SUT = updateSavedListingIds({ listings, listingId, action });

            expect(SUT).toEqual([1, 2]);
        });
    });
});
