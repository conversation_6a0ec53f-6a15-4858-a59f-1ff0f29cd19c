import { getParamName } from './recruiter-profile-services';

describe('Recruiter profile services...', () => {
    describe('getParamName()...', () => {
        it('should return "add"', () => {
            const SUT = getParamName(false);
            expect(SUT).toEqual('add');
        });

        it('should return "remove"', () => {
            const SUT = getParamName(true);
            expect(SUT).toEqual('remove');
        });
    });
});
