/* eslint-disable react/display-name */
import React from 'react';
import { render } from '@testing-library/react';
import { Provider } from 'react-redux';
import { combineReducers, createStore } from 'redux';
import { ThemeProvider } from '@emotion/react';
import * as themeFactory from '@gumtree/ui-library/src/base/themes/theme-factory';
import commonReducers from '@gumtree/shell/src/reducers/common';

import App from './app';

jest.mock('@gumtree/shell/src/header/header-container', () => () => <div>MockedHeader</div>);
jest.mock('@gumtree/ui-library', () => ({
    Container: ({ children }: any) => <div data-testid="Container">{children}</div>,
}));
jest.mock('./error-illustration', () => () => <div>ErrorIllustrationMock</div>);
jest.mock('./app.style', () => ({
    Wrapper: ({ children }: any) => <div data-testid="Wrapper">{children}</div>,
    Content: ({ children }: any) => <div data-testid="Content">{children}</div>,
    Illustration: ({ children }: any) => <div data-testid="Illustration">{children}</div>,
    Title: ({ children }: any) => <div data-testid="Title">{children}</div>,
    Message: ({ children }: any) => <div data-testid="Message">{children}</div>,
    // eslint-disable-next-line react/button-has-type
    HomeButton: ({ label }: any) => <button>{label}</button>,
}));

describe('App', () => {
    it('renders correctly', () => {
        const theme = themeFactory.createTheme({ page: { pageTheme: 'web-view' } });
        const store = createStore(combineReducers(commonReducers), {});

        const { container } = render(
            <ThemeProvider theme={theme}>
                <Provider store={store}>
                    <App />
                </Provider>
            </ThemeProvider>
        );

        expect(container).toMatchSnapshot();
    });
});
