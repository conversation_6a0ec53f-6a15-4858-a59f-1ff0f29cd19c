import styled from '@emotion/styled';
import {
    colors,
    gutterSizes,
    mediaQuery,
    breakpoints,
    fontWeights,
    fontSizes,
} from '@gumtree/ui-library/src/base/theme';

export const AdHeader = styled.div`
    border-bottom: solid thin ${colors.bark10};
    padding: 0 ${gutterSizes.large} ${gutterSizes.large} ${gutterSizes.large};
    display: flex;
`;

export const AdTopSection = styled.div`
    padding: ${gutterSizes.large} ${gutterSizes.large} ${gutterSizes.base};
`;

export const AdDetailsContainer = styled.div`
    display: flex;
    flex-direction: row;
`;

export const PictureContainer = styled.a`
    margin-right: ${gutterSizes.large};
    flex-shrink: 0;
`;

export const AdvertDetails = styled.div`
    display: flex;
    flex-direction: column;
    flex-grow: 1;
`;

export const AdvertDetailsTopRow = styled.div`
    display: flex;
`;

export const AdvertDetailsStatsRow = styled.div`
    display: flex;
    flex-wrap: wrap;
    padding-top: ${gutterSizes.base};
    color: ${colors.newGrey};
    gap: ${gutterSizes.xlarge};

    ${mediaQuery.until(breakpoints.small)} {
        gap: ${gutterSizes.large};
    }
`;

export const AdvertGoLiveStatusRow = styled.div`
    color: ${colors.newGrey};
    display: flex;
`;

export const FeaturesContainer = styled.div`
    display: block;

    div {
        :not(:last-of-type)::after {
            content: ', ';
            white-space: pre;
        }
    }
`;

export const HiddenOnMobile = styled.span`
    ${mediaQuery.until(breakpoints.medium)} {
        display: none;
    }
`;

export const Statistics = styled.a`
    display: flex;
    gap: ${gutterSizes.xlarge};

    ${mediaQuery.until(breakpoints.small)} {
        gap: ${gutterSizes.large};
    }
`;

export const Statistic = styled.div`
    display: flex;
    align-items: center;

    path {
        stroke: ${colors.newGrey};
    }
`;

export const Count = styled.span`
    padding-left: ${gutterSizes.medium10};
`;

export const AdvertTitle = styled.a`
    font-weight: ${fontWeights.bold};
    flex-grow: 0;
`;

export const AdvertPrice = styled.a`
    color: ${colors.green};
    font-weight: ${fontWeights.bold};
    font-size: ${fontSizes.header};

    ${mediaQuery.until(breakpoints.small)} {
        font-size: ${fontSizes.base};
    }
`;

export const AdBottomSection = styled.div`
    padding: 0 ${gutterSizes.large};
    display: flex;
    color: ${colors.newGrey};

    a {
        li {
            cursor: pointer;
        }
    }
`;

export const PostedDate = styled.div`
    padding-right: ${gutterSizes.large};
`;
