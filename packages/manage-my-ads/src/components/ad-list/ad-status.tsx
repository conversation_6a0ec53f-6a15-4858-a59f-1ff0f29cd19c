import React from 'react';
import HintTooltip from '@gumtree/ui-library/src/hint-tooltip/hint-tooltip';
import { qaAttribute } from '@gumtree/ui-library/src/utils/qa-service';
import { getRepostAdType } from '@gumtree/shared/src/util/repost-ad-service';

import { AdStatusContainer, AdStatusLink, Dot, StatusText } from './ad-status.style';
import getAdStatusConfig from './ad-status-config';
import { AWAITING_PHONE_VERIFIED } from '../../consts/ad-type-filters';

const AdStatus = ({ advert, href }) => {
    const { sellerType, l1CategoryId, bumpPrice, status, price } = advert;

    const isPaidExpiredAd =
        status === 'EXPIRED' &&
        getRepostAdType({ sellerType, l1CategoryId }) === 'paid' &&
        parseInt(bumpPrice) > 0;

    const isPaidDraftAd = status === 'DRAFT' && !price.insertion.free;

    const adStatusConfig = getAdStatusConfig(status, isPaidExpiredAd, isPaidDraftAd);

    let dotText = '';

    if (isPaidDraftAd || status === 'NEEDS_EDITING' || status === AWAITING_PHONE_VERIFIED) {
        dotText = '!';
    }

    return (
        <AdStatusContainer>
            <AdStatusLink href={href}>
                <Dot
                    fillColor={adStatusConfig.fillColour}
                    borderColor={adStatusConfig.borderColour}
                    borderThickness={adStatusConfig.borderThickness}
                    {...qaAttribute('status-dot')}
                >
                    {dotText}
                </Dot>
                <StatusText {...qaAttribute('status-text')}>{adStatusConfig.statusText}</StatusText>
            </AdStatusLink>
            {adStatusConfig.tooltipText && (
                <HintTooltip
                    content={adStatusConfig.tooltipText}
                    triggerColor="grey"
                    closeBtnColor="lightGrey"
                    theme="white"
                    elName="ad-status"
                    hasCloseBtn={false}
                    align="left"
                    size="large"
                />
            )}
        </AdStatusContainer>
    );
};

export default AdStatus;
