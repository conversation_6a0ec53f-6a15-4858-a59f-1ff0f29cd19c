import { render } from '@testing-library/react';
import React from 'react';
import AdFeatureStats from './ad-feature-stats';

describe('ad-feature-stats', () => {
    const mockExpires = {
        spotlight: '7 days left',
        urgent: '7 days left',
        featured: '7 days left',
    };

    const hasBeenBumpedUp = true;

    it('renders', () => {
        const rendered = render(
            <AdFeatureStats expires={mockExpires} hasBeenBumpedUp={hasBeenBumpedUp} />
        );

        expect(rendered.asFragment()).toMatchSnapshot();
    });

    it('renders without BumpUp', () => {
        const hasBeenBumpedUp = false;
        const rendered = render(
            <AdFeatureStats expires={mockExpires} hasBeenBumpedUp={hasBeenBumpedUp} />
        );

        expect(rendered.asFragment()).toMatchSnapshot();
    });

    it('.stats-label elements should be rendered in custom order', () => {
        const { container } = render(
            <AdFeatureStats expires={mockExpires} hasBeenBumpedUp={hasBeenBumpedUp} />
        );
        const labelElements = Array.from(container.querySelectorAll('.stats-label'));
        const expectedOrder = ['Featured', 'Urgent', 'Spotlight', 'Bump up'];
        const actualOrder = labelElements.map((element) => element.textContent);

        expect(actualOrder).toEqual(expectedOrder);
    });
});
