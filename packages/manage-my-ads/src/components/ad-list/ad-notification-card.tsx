/* eslint-disable react/jsx-curly-brace-presence */
import React from 'react';
import { NotificationCard } from '@gumtree/ui-library';
import { gutterSizes } from '@gumtree/ui-library/src/base/theme';
import { AWAITING_PHONE_VERIFIED } from '../../consts/ad-type-filters';

const AdNotificationCard = ({ advert }) => {
    const {
        status,
        price: { insertion },
    } = advert;

    const isRemoved = status === 'DELETED_CS';
    const isPaidDraft = status === 'DRAFT' && !insertion.free;
    const needVerification = status === AWAITING_PHONE_VERIFIED;

    const cardContent = isPaidDraft ? (
        <>A payment is required for this advert.</>
    ) : status === 'NEEDS_EDITING' ? (
        <>
            Your ad is on hold for breaking{' '}
            <a
                className="btn-link"
                href={`https://help.gumtree.com/s/policies?article=What-s-Not-Allowed-on-Gumtree&cat=Posting_Policies`}
                target="_blank"
                rel="noreferrer"
            >
                posting rules
            </a>
            {'.'} We have emailed you to explain why.
        </>
    ) : isRemoved ? (
        <>
            Your ad is removed for breaking{' '}
            <a
                className="btn-link"
                href={`https://help.gumtree.com/s/policies?article=What-s-Not-Allowed-on-Gumtree&cat=Posting_Policies`}
                target="_blank"
                rel="noreferrer"
            >
                posting rules
            </a>
            {'.'} We have emailed you to explain why.
        </>
    ) : needVerification ? (
        <>Verify your phone number to activate your ad</>
    ) : (
        <></>
    );

    return (
        <NotificationCard
            content={cardContent}
            theme={isRemoved ? 'grey' : 'default'}
            margin={`0 0 ${gutterSizes.large}`}
            dataQ="ad-notification-card"
        />
    );
};

export default AdNotificationCard;
