import { mocked } from 'jest-mock';

import buyerTransform from '@gumtree/web-bff/src/data/buyer/transform';
import { mapBFFURLParameterToSellerURLParameter } from '../../utils/url-constructor';
import transform from './index';
import { AD_TYPE_FILTER_VALUES } from '../../consts/ad-type-filters';

jest.mock('@gumtree/web-bff/src/data/buyer/transform', () => jest.fn(() => ({})));
jest.mock('../../utils/url-constructor');

describe('Transform seller data with cookies', () => {
    const mockBuyerData = {
        sellerData: 'mock',
        baseConfig: {},
    };

    const mockConstructManageMyAdsSellerUrl = mocked(mapBFFURLParameterToSellerURLParameter);

    beforeEach(() => {
        (buyerTransform as jest.Mock).mockReturnValue(mockBuyerData);
        mockConstructManageMyAdsSellerUrl.mockReturnValue(AD_TYPE_FILTER_VALUES.ACTIVE);
    });

    const performTransform = (modelData = {}) => {
        return transform(
            {
                model: {
                    ...modelData,
                },
            },
            'active',
            ''
        );
    };

    const performTransformWithAdvert = (id, data, productData = {}) => {
        return transform(
            {
                model: {
                    featureForm: {
                        previews: [
                            {
                                id: parseInt(id),
                            },
                        ],
                    },
                    adverts: {
                        [id]: {
                            expires: {},
                            ...data,
                        },
                    },
                    prices: {
                        [id]: {
                            INSERTION: {
                                ...productData,
                            },
                        },
                    },
                },
            },
            'active',
            ''
        );
    };

    it('includes transformed seller data', () => {
        const output = performTransform({});
        expect(output).toEqual(expect.objectContaining(mockBuyerData));
    });

    it('includes the advertCount', () => {
        const output = performTransform({ advertCount: 100 });
        expect(output.manageMyAdsDetails.advertCount).toEqual(100);
    });

    it('includes the adFilterType', () => {
        const output = performTransform({});

        expect(mockConstructManageMyAdsSellerUrl).toHaveBeenCalledWith('active');
        expect(output.manageMyAdsDetails.adFilterType).toEqual(AD_TYPE_FILTER_VALUES.ACTIVE);
    });

    it('includes the postAdUrl', () => {
        const output = performTransform({
            core: {
                jobsConfig: {
                    postAdUrl: 'http://unit-test',
                },
            },
        });

        expect(output.manageMyAdsDetails.postAdUrl).toEqual('http://unit-test/job-listing/');
    });

    describe('includes adverts', () => {
        it('when there are no adverts', () => {
            const output = performTransform({
                adverts: {},
            });

            expect(output.manageMyAdsDetails.adverts).toHaveLength(0);
        });

        it('when there is a single advert', () => {
            const output = performTransform({
                adverts: {
                    '1': {
                        adId: 1,
                        expires: {},
                    },
                },
                featureForm: {
                    previews: [
                        {
                            id: 1,
                        },
                    ],
                },
                prices: {
                    '1': {
                        INSERTION: {},
                    },
                },
            });

            expect(output.manageMyAdsDetails.adverts).toHaveLength(1);
        });

        it('when there are multiple adverts', () => {
            const output = performTransform({
                adverts: {
                    '1': { adId: 1, expires: {} },
                    '2': { adId: 2, expires: {} },
                    '3': { adId: 3, expires: {} },
                },
                featureForm: {
                    previews: [
                        {
                            id: 1,
                        },
                        {
                            id: 2,
                        },
                        {
                            id: 3,
                        },
                    ],
                },
                prices: {
                    '1': {
                        INSERTION: {},
                    },
                    '2': {
                        INSERTION: {},
                    },
                    '3': {
                        INSERTION: {},
                    },
                },
            });

            expect(output.manageMyAdsDetails.adverts).toHaveLength(3);
        });

        it('in the same order they appear in the previews section', () => {
            const output = transform(
                {
                    model: {
                        featureForm: {
                            previews: [
                                {
                                    id: 120,
                                },
                                {
                                    id: 9,
                                },
                                {
                                    id: 123,
                                },
                            ],
                        },
                        adverts: {
                            ['9']: {
                                adId: '9',
                                rootImgUrl: '',
                                altTag: 'This is the alt tag',
                                expires: {},
                            },
                            ['120']: {
                                adId: '120',
                                rootImgUrl: '',
                                altTag: 'This is the alt tag',
                                expires: {},
                            },
                            ['123']: {
                                adId: '123',
                                rootImgUrl: '',
                                altTag: 'This is the alt tag',
                                expires: {},
                            },
                        },
                        prices: {
                            '9': {
                                INSERTION: {},
                            },
                            '120': {
                                INSERTION: {},
                            },
                            '123': {
                                INSERTION: {},
                            },
                        },
                    },
                },
                'active',
                ''
            );

            expect(output.manageMyAdsDetails.adverts[0].adId).toEqual('120');
            expect(output.manageMyAdsDetails.adverts[1].adId).toEqual('9');
            expect(output.manageMyAdsDetails.adverts[2].adId).toEqual('123');
        });

        it('with the adId populated', () => {
            const output = performTransformWithAdvert('123', { adId: 123 });
            expect(output.manageMyAdsDetails.adverts[0].adId).toBe(123);
        });

        it('with the title populated', () => {
            const output = performTransformWithAdvert('123', { adId: 123, title: 'title test' });
            expect(output.manageMyAdsDetails.adverts[0].title).toBe('title test');
        });

        it('with the displayPrice populated', () => {
            const output = performTransformWithAdvert('123', { adId: 123, displayPrice: '£200' });
            expect(output.manageMyAdsDetails.adverts[0].displayPrice).toBe('£200');
        });

        it('with the views populated', () => {
            const output = performTransformWithAdvert('123', { adId: 123, views: '2' });
            expect(output.manageMyAdsDetails.adverts[0].views).toBe('2');
        });

        it('with the listingViews populated', () => {
            const output = performTransformWithAdvert('123', { adId: 123, listingViews: '5' });
            expect(output.manageMyAdsDetails.adverts[0].listingViews).toBe('5');
        });

        it('with the bumpTimes populated', () => {
            const output = performTransformWithAdvert('123', { adId: 123, bumpTimes: '1' });
            expect(output.manageMyAdsDetails.adverts[0].bumpTimes).toBe(1);
        });

        it('with the replies populated', () => {
            const output = performTransformWithAdvert('123', { adId: 123, replies: '8' });
            expect(output.manageMyAdsDetails.adverts[0].replies).toBe('8');
        });

        it('with age populated', () => {
            const output = performTransformWithAdvert('123', { adId: 123, age: '8 days ago' });
            expect(output.manageMyAdsDetails.adverts[0].age).toBe('8 days ago');
        });

        it('with the status populated', () => {
            const output = performTransformWithAdvert('123', { adId: 123, status: 'LIVE' });
            expect(output.manageMyAdsDetails.adverts[0].status).toBe('LIVE');
        });

        it('with the advertUrl populated', () => {
            const output = performTransformWithAdvert('123', {
                adId: 123,
                advertUrl: 'http://mock-advert-url',
            });
            expect(output.manageMyAdsDetails.adverts[0].advertUrl).toBe('http://mock-advert-url');
        });

        describe('with the images populated', () => {
            it('when there no images', () => {
                const output = transform(
                    {
                        model: {
                            featureForm: {
                                previews: [
                                    {
                                        id: 123,
                                        numberOfImages: 0,
                                    },
                                ],
                            },
                            adverts: {
                                ['123']: {
                                    adId: '123',
                                    rootImgUrl: '',
                                    altTag: 'This is the alt tag',
                                    expires: {},
                                },
                            },
                            prices: {
                                '123': {
                                    INSERTION: {},
                                },
                            },
                        },
                    },
                    'active',
                    ''
                );

                expect(output.manageMyAdsDetails.adverts[0].images).toEqual({
                    numberOfImages: 0,
                    rootImgUrl: null,
                    altTag: 'This is the alt tag',
                });
            });

            it('when there is no matching preview to extract the image count from', () => {
                const output = transform(
                    {
                        model: {
                            featureForm: {
                                previews: [
                                    {
                                        id: 123,
                                        numberOfImages: 1,
                                    },
                                ],
                            },
                            adverts: {
                                ['123']: {
                                    adId: '123',
                                    rootImgUrl: '',
                                    altTag: 'This is the alt tag',
                                    expires: {},
                                },
                            },
                            prices: {
                                '123': {
                                    INSERTION: {},
                                },
                            },
                        },
                    },
                    'active',
                    ''
                );

                expect(output.manageMyAdsDetails.adverts[0].images).toEqual({
                    numberOfImages: 1,
                    rootImgUrl: null,
                    altTag: 'This is the alt tag',
                });
            });

            it('when there is an image without an ebayimg url', () => {
                const output = transform(
                    {
                        model: {
                            featureForm: {
                                previews: [
                                    {
                                        id: 123,
                                        numberOfImages: 1,
                                    },
                                ],
                            },
                            adverts: {
                                ['123']: {
                                    adId: '123',
                                    rootImgUrl:
                                        'https://imagedelivery.net/ePR8PyKf84wPHx7_RYmEag/aba8ed27-118f-40aa-2724-ff230adefc00/78',
                                    altTag: 'This is the alt tag',
                                    expires: {},
                                },
                            },
                            prices: {
                                '123': {
                                    INSERTION: {},
                                },
                            },
                        },
                    },
                    'active',
                    ''
                );

                expect(output.manageMyAdsDetails.adverts[0].images).toEqual({
                    numberOfImages: 1,
                    rootImgUrl:
                        'https://imagedelivery.net/ePR8PyKf84wPHx7_RYmEag/aba8ed27-118f-40aa-2724-ff230adefc00/78',
                    altTag: 'This is the alt tag',
                });
            });

            it('when there is an advert with an ebayimg url', () => {
                const output = transform(
                    {
                        model: {
                            featureForm: {
                                previews: [
                                    {
                                        id: 123,
                                        numberOfImages: 5,
                                    },
                                ],
                            },
                            adverts: {
                                ['123']: {
                                    adId: '123',
                                    rootImgUrl:
                                        'https://i.ebayimg.com/00/s/MTAyNFg3Njg=/z/StkAAOSwJzpcnN~c/$_',
                                    altTag: 'This is the alt tag',
                                    expires: {},
                                },
                            },
                            prices: {
                                '123': {
                                    INSERTION: {},
                                },
                            },
                        },
                    },
                    'active',
                    ''
                );

                expect(output.manageMyAdsDetails.adverts[0].images).toEqual({
                    numberOfImages: 5,
                    rootImgUrl:
                        'https://i.ebayimg.com/00/s/MTAyNFg3Njg=/z/StkAAOSwJzpcnN~c/$_79.jpg',
                    altTag: 'This is the alt tag',
                });
            });
        });

        describe('with the expires object populated', () => {
            it('when there was no expires in the advert, it should return an empty object', () => {
                const output = performTransformWithAdvert('123', { adId: 123 });
                expect(output.manageMyAdsDetails.adverts[0].expires).toStrictEqual({});
            });

            it('when there is an URGENT expiry, it should be returned', () => {
                const output = performTransformWithAdvert('123', {
                    adId: 123,
                    expires: { URGENT: 'test' },
                });

                expect(output.manageMyAdsDetails.adverts[0].expires).toStrictEqual({
                    urgent: 'test',
                });
            });

            it('when there is a FEATURE_14_DAY expiry, it should be returned', () => {
                const output = performTransformWithAdvert('123', {
                    adId: 123,
                    expires: { FEATURE_14_DAY: 'test' },
                });

                expect(output.manageMyAdsDetails.adverts[0].expires).toStrictEqual({
                    featured: 'test',
                });
            });

            it('when there is a FEATURE_7_DAY expiry, it should be returned', () => {
                const output = performTransformWithAdvert('123', {
                    adId: 123,
                    expires: { FEATURE_7_DAY: 'test' },
                });

                expect(output.manageMyAdsDetails.adverts[0].expires).toStrictEqual({
                    featured: 'test',
                });
            });

            it('when there is a FEATURE_3_DAY expiry, it should be returned', () => {
                const output = performTransformWithAdvert('123', {
                    adId: 123,
                    expires: { FEATURE_3_DAY: 'test' },
                });

                expect(output.manageMyAdsDetails.adverts[0].expires).toStrictEqual({
                    featured: 'test',
                });
            });

            it('when there are multiple FEATURE_x expiries, they should be returned in order of shortest feature purchased (like legacy)', () => {
                let output = performTransformWithAdvert('123', {
                    adId: 123,
                    expires: {
                        FEATURE_14_DAY: 'test14',
                        FEATURE_7_DAY: 'test7',
                        FEATURE_3_DAY: 'test3',
                    },
                });

                expect(output.manageMyAdsDetails.adverts[0].expires).toStrictEqual({
                    featured: 'test3',
                });

                output = performTransformWithAdvert('123', {
                    adId: 123,
                    expires: {
                        FEATURE_14_DAY: 'test14',
                        FEATURE_7_DAY: 'test7',
                    },
                });

                expect(output.manageMyAdsDetails.adverts[0].expires).toStrictEqual({
                    featured: 'test7',
                });
            });

            it('when there is a HOMEPAGE_SPOTLIGHT expiry, it should be returned', () => {
                const output = performTransformWithAdvert('123', {
                    adId: 123,
                    expires: { HOMEPAGE_SPOTLIGHT: 'test' },
                });

                expect(output.manageMyAdsDetails.adverts[0].expires).toStrictEqual({
                    spotlight: 'test',
                });
            });
        });

        describe('with the prices object populated', () => {
            it('when there is a FEATURE_14_DAY expiry, it should be returned', () => {
                const output = performTransformWithAdvert(
                    '123',
                    {
                        adId: 123,
                    },
                    {
                        productName: 'INSERTION',
                        price: 9.99,
                        includedInPackage: false,
                        clientValue: '9.99',
                        productType: 'INSERTION',
                        displayValue: '&pound;9.99',
                        free: false,
                        value: 'INSERTION',
                    }
                );

                expect(output.manageMyAdsDetails.adverts[0].price).toStrictEqual({
                    insertion: {
                        productName: 'INSERTION',
                        price: 9.99,
                        free: false,
                    },
                });
            });
        });
    });

    describe('includes paging information', () => {
        it('with the current page', () => {
            const output = performTransform({ currentPageNumber: 5 });
            expect(output.manageMyAdsDetails.paging.current).toEqual(5);
        });

        it('with the total pages', () => {
            const output = performTransform({ totalNumberOfPages: 15 });
            expect(output.manageMyAdsDetails.paging.total).toEqual(15);
        });

        it('with the pagePath', () => {
            const output = performTransform({});
            expect(output.manageMyAdsDetails.paging.pagePath).toEqual(
                '/manage/ads?filter=active&page={PAGE}'
            );
        });
    });
});
