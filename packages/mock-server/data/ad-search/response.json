{"items": [{"locations": [{"id": ********, "name": "england", "display_name": "England", "primary": false}, {"id": ********, "name": "surrey", "display_name": "Surrey", "primary": false}, {"id": ********, "name": "<PERSON><PERSON>am", "display_name": "<PERSON><PERSON><PERSON>", "primary": true}, {"id": ********, "name": "uk", "display_name": "United Kingdom", "primary": false}], "cs_reviewed": false, "created_date": *************, "description": "michal test job ad\r\n", "tags": ["job"], "paid_for": false, "location_name": ["<PERSON><PERSON>am", "surrey", "england", "uk"], "public_website_url": "https://www.demo.gumtree.io/jobs/job/73330/michal-test-job-ad", "location_id": [********, ********, ********, ********], "version": 1, "location_display_name": ["<PERSON><PERSON><PERSON>", "Surrey", "England", "United Kingdom"], "id": **********, "account_id": **********, "published_date": *************, "status": "LIVE", "last_modified_date": *************, "categories": [{"id": 12201, "name": "housekeeping-and-cleaning", "display_name": "Housekeeping & Cleaning", "primary": false}, {"id": 2553, "name": "jobs", "display_name": "Jobs", "primary": false}, {"id": 12203, "name": "domestic", "display_name": "Domestic", "primary": true}, {"id": 1, "name": "all", "display_name": "All Categories", "primary": false}], "centroid": {"latitude": 51.427489, "longitude": -0.552244}, "account": {"id": **********}, "attribute": {"salary_text": "up to 300", "salary_period": "per_year", "recruiter": "<PERSON>", "salary": {"gte": 0, "lte": 1000000}}, "title": "michal test job ad", "bump_up_count": 0, "pin": {"location": [-0.552244, 51.427489]}, "visible_on_map": false, "contact_email": "<EMAIL>", "expiry_date": *************}, {"locations": [{"id": ********, "name": "england", "display_name": "England", "primary": false}, {"id": ********, "name": "uk", "display_name": "United Kingdom", "primary": false}, {"id": ********, "name": "brighton", "display_name": "Brighton", "primary": true}, {"id": ********, "name": "east-sussex", "display_name": "East Sussex", "primary": false}], "cs_reviewed": false, "created_date": *************, "contact_url": "http://www.test.com/", "contact_telephone": "*********", "description": "Madgex Test\r\n", "tags": ["job"], "paid_for": false, "location_name": ["brighton", "east-sussex", "england", "uk"], "public_website_url": "https://www.demo.gumtree.io/jobs/job/73329/madgex-test", "location_id": [********, ********, ********, ********], "version": 1, "location_display_name": ["Brighton", "East Sussex", "England", "United Kingdom"], "id": **********, "account_id": **********, "published_date": *************, "status": "LIVE", "last_modified_date": *************, "categories": [{"id": 12201, "name": "housekeeping-and-cleaning", "display_name": "Housekeeping & Cleaning", "primary": false}, {"id": 2553, "name": "jobs", "display_name": "Jobs", "primary": false}, {"id": 12202, "name": "commercial-jobs", "display_name": "Commercial", "primary": true}, {"id": 1, "name": "all", "display_name": "All Categories", "primary": false}], "centroid": {"latitude": 50.834233, "longitude": -0.136921}, "account": {"id": **********}, "attribute": {"salary_text": "Competitive", "recruiter": "<PERSON>"}, "title": "Madgex Test", "bump_up_count": 0, "pin": {"location": [-0.136921, 50.834233]}, "visible_on_map": false, "contact_email": "<EMAIL>", "expiry_date": *************}, {"locations": [{"id": ********, "name": "england", "display_name": "England", "primary": false}, {"id": ********, "name": "uk", "display_name": "United Kingdom", "primary": false}, {"id": ********, "name": "port-isaac", "display_name": "Port Isaac", "primary": true}, {"id": ********, "name": "cornwall", "display_name": "Cornwall", "primary": false}], "cs_reviewed": false, "additional_image_urls": [], "created_date": *************, "description": "Advanced 5th generation benchmark incentivize virtual paradigms. Up-sized disintermediate encoding whiteboard back-end content. Fully-configurable transitional adapter recontextualize sticky e-business. Polarised attitude-oriented core unleash real-time ROI. Self-enabling actuating protocol matrix B2B content. Distributed fault-tolerant intranet reintermediate dot-com portals. Centralized foreground benchmark aggregate synergistic action-items. Ameliorated intermediate core transition virtual deliverables. User-centric transitional installation extend impactful action-items.\r\n\r\n\r\n\r\nResponsibilities:\r\n\r\n\r\n\r\nLegacy program backing up\r\nRegional bandwidth connecting\r\nCentral array calculating\r\nGlobal protocol connecting\r\nInternational bandwidth connecting\r\n", "tags": ["job"], "paid_for": false, "features": [{"@class": "com.gumtree.sapi.spec.domain.ExpirableFeature", "product": "FEATURE_7_DAY", "expiry_date": *************}], "primary_image_url": "https://www.demo.gumtree.io/jobs/getasset/0d153a46-d60c-42db-8f72-153bd2365fb6/", "location_name": ["port-isaac", "cornwall", "england", "uk"], "public_website_url": "https://www.demo.gumtree.io/jobs/job/73332/customer-infrastructure-specialist", "location_id": [********, ********, ********, ********], "version": 1, "location_display_name": ["Port Isaac", "Cornwall", "England", "United Kingdom"], "id": **********, "account_id": **********, "published_date": *************, "status": "LIVE", "last_modified_date": *************, "categories": [{"id": 12212, "name": "tradesmen", "display_name": "Tradesmen", "primary": true}, {"id": 2553, "name": "jobs", "display_name": "Jobs", "primary": false}, {"id": 1, "name": "all", "display_name": "All Categories", "primary": false}, {"id": 12209, "name": "construction-and-property", "display_name": "Construction & Property", "primary": false}], "centroid": {"latitude": 50.586272, "longitude": -4.829985}, "account": {"id": **********}, "attribute": {"salary_text": "£100 - £109.99", "salary_period": "per_day", "recruiter": "Atari Group (Madgex Test Data)", "salary": {"gte": 10000, "lte": 11000}}, "title": "Customer Infrastructure Specialist", "bump_up_count": 0, "pin": {"location": [-4.829985, 50.586272]}, "visible_on_map": false, "contact_email": "<EMAIL>", "expiry_date": *************}], "total": 3, "took": 23}