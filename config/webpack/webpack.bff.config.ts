/**
 * This config is for PRODUCTION ONLY.
 * It should be launched via `webpack`.
 */
import path from 'path';
import webpack from 'webpack';
import merge from 'webpack-merge';
/**
 * Can analyze bundle, triggered by environment variable.
 */
import { BundleAnalyzerPlugin } from 'webpack-bundle-analyzer';
/**
 * Copy source files into build folder.
 */
import CopyWebpackPlugin from 'copy-webpack-plugin';
/**
 * Reuse some configurations from assets.webpack.config.
 */
import {
    common,
    rule as assetsRule,
    fileLoaderProdName,
    fileLoaderRegex,
} from './webpack.assets.config';

const production = true;
const analyze = process.env.ANALYZE === 'true';

const rootPath = path.resolve(__dirname, '../..');
enum Path {
    entry = 'packages/web-bff/src',
    output = 'dist/web-bff/src',
    properties_src = 'properties/web-bff.production.properties',
    properties_dst = 'dist/web-bff/src/properties/web-bff.properties',
    env_stripper_src = 'packages/web-bff/scripts/env-stripper.sh',
    env_stripper_dst = 'dist/web-bff/scripts/env-stripper.sh',
}
const resolve = (...paths: Path[]) => path.join(rootPath, ...paths);

const rule: Record<'ignoreSass' | 'serverFiles', webpack.RuleSetRule> = {
    ignoreSass: {
        test: /\.scss$/,
        use: 'null-loader',
    },
    serverFiles: {
        test: fileLoaderRegex,
        use: {
            loader: 'file-loader',
            options: {
                name: fileLoaderProdName,
                emitFile: false,
            },
        },
    },
};

export default async () => {
    console.log({
        info: 'bff webpack config',
        production,
        analyze,
    });

    return merge<webpack.Configuration>(
        common,
        {
            devtool: 'source-map',
            entry: {
                server: resolve(Path.entry),
            },
            optimization: {
                minimize: false,
            },
            mode: 'production',
            module: {
                rules: [assetsRule.babel, assetsRule.externalJs, rule.ignoreSass, rule.serverFiles],
            },
            output: {
                path: resolve(Path.output),
                filename: '[name].js',
            },
            plugins: [
                new CopyWebpackPlugin([
                    {
                        from: resolve(Path.properties_src),
                        to: resolve(Path.properties_dst),
                    },
                    {
                        from: resolve(Path.env_stripper_src),
                        to: resolve(Path.env_stripper_dst),
                    },
                ]) as typeof CopyWebpackPlugin & { apply(compiler: webpack.Compiler): void },
            ],
            target: 'node',
        },
        analyze
            ? {
                  plugins: [
                      new BundleAnalyzerPlugin({
                          analyzerMode: 'static',
                          reportFilename: '../../bff.report.html',
                      }) as BundleAnalyzerPlugin & { apply(compiler: webpack.Compiler): void },
                  ],
              }
            : {}
    );
};
