FROM europe-west4-docker.pkg.dev/gum-host-7491/docker-artifact-repo/node:20.18.0-alpine AS build

WORKDIR /tmp/srv

# Use yarn offline-mirror to improve subsequent yarn install
RUN yarn config set yarn-offline-mirror ./npm-packages-offline-cache
RUN yarn config set yarn-offline-mirror-pruning true

COPY . .

# Install node_modules to speed-up `yarn install` in derived images.
# Delete all other directories to remove stale code.
# Considered generating/copying .eslintcache but saw stale results locally.
RUN yarn install --ignore-optional && \
    mkdir -p /srv && \
    mv node_modules /srv && \
    rm -rf /tmp/srv

WORKDIR /srv
