#!/usr/bin/env groovy
import org.jenkinsci.plugins.pipeline.modeldefinition.Utils
import com.gumtree.jenkins.*

// Common config
def projectRepoUrl = '**************:gumtree-tech/frontend.git'
def credentialsId = 'github-ssh-private-key'

def pathToAssetsDirectory = 'assets/site-root'
def envToDeploy = env.GCP_PROJECT_ID.substring(0, env.GCP_PROJECT_ID.lastIndexOf('-'))
def googleBucketLocation = "gs://${envToDeploy}-static-assets"

void uploadAssetsFromHostToGcpBucket(localArtifactDir, gcpBucketLink) {
    try {
        withGcloud {
            def releaseId = sh(returnStdout: true, script: 'date +%Y%m%d%H%M%S').trim()
            def cacheControl = 'Cache-Control: public, max-age=0'

            sh """\
                gsutil -m -h x-goog-meta-release:$releaseId \
                -h '${cacheControl}' \
                cp -r ${localArtifactDir}/* ${gcpBucketLink}
                """

            // Required to copy hidden files and folders
            // Reference: https://superuser.com/q/61611
            sh """\
                gsutil -m -h x-goog-meta-release:$releaseId \
                -h '${cacheControl}' \
                cp -r ${localArtifactDir}/.[^.]* ${gcpBucketLink}
                """
        }
    } catch (err) {
        echo 'Error while uploading assets to google storage bucket.'
        throw err
    }
}

node {
    container("build") {
        def scmVars = checkout([
                $class           : 'GitSCM',
                branches         : [[name: env.BRANCH_NAME]],
                userRemoteConfigs: [[url: projectRepoUrl, credentialsId: credentialsId]]
        ])

        stage('Upload to Google bucket') {
            uploadAssetsFromHostToGcpBucket(pathToAssetsDirectory, googleBucketLocation)
        }
    }
}
